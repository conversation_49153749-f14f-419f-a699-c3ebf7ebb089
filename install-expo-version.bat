@echo off
echo ========================================
echo JARVIS Lite - Expo Quick Install
echo ========================================
echo.

echo Installing Expo CLI...
npm install -g @expo/cli

echo.
echo Creating Expo version of JARVIS Lite...
npx create-expo-app JarvisLiteExpo --template blank-typescript

echo.
echo Copying JARVIS Lite code to Expo project...
xcopy /E /I /Y src JarvisLiteExpo\src\
copy App.tsx JarvisLiteExpo\App.tsx

echo.
echo Installing dependencies...
cd JarvisLiteExpo
npm install @react-navigation/native @react-navigation/stack
npm install react-native-screens react-native-safe-area-context
npm install react-native-gesture-handler react-native-vector-icons
npm install @react-native-async-storage/async-storage
npm install moment

echo.
echo Starting Expo development server...
echo Scan the QR code with Expo Go app on your phone
npx expo start

pause
