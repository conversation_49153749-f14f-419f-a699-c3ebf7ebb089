/**
 * InteractionLogger.ts
 * Logs all user interactions locally in SQLite
 * Provides analytics and pattern detection data
 */

import SQLite from 'react-native-sqlite-storage';
import { InteractionLog } from '@/types';

// Enable debugging
SQLite.DEBUG(false);
SQLite.enablePromise(true);

interface LogFilter {
  intent?: string;
  dateFrom?: Date;
  dateTo?: Date;
  limit?: number;
}

class InteractionLogger {
  private db: SQLite.SQLiteDatabase | null = null;
  private isInitialized = false;

  constructor() {
    this.initialize();
  }

  /**
   * Initialize SQLite database
   */
  private async initialize(): Promise<void> {
    try {
      this.db = await SQLite.openDatabase({
        name: 'jarvis_lite.db',
        location: 'default',
        createFromLocation: '~jarvis_lite.db'
      });

      await this.createTables();
      this.isInitialized = true;
      console.log('InteractionLogger: Database initialized');
    } catch (error) {
      console.error('InteractionLogger: Failed to initialize database:', error);
    }
  }

  /**
   * Create necessary database tables
   */
  private async createTables(): Promise<void> {
    if (!this.db) return;

    const createInteractionsTable = `
      CREATE TABLE IF NOT EXISTS interactions (
        id TEXT PRIMARY KEY,
        timestamp INTEGER NOT NULL,
        intent TEXT NOT NULL,
        original_text TEXT,
        action TEXT NOT NULL,
        parameters TEXT,
        duration INTEGER,
        context TEXT,
        created_at INTEGER DEFAULT (strftime('%s', 'now'))
      );
    `;

    const createIndexes = `
      CREATE INDEX IF NOT EXISTS idx_interactions_timestamp ON interactions(timestamp);
      CREATE INDEX IF NOT EXISTS idx_interactions_intent ON interactions(intent);
      CREATE INDEX IF NOT EXISTS idx_interactions_action ON interactions(action);
    `;

    try {
      await this.db.executeSql(createInteractionsTable);
      await this.db.executeSql(createIndexes);
      console.log('InteractionLogger: Tables created successfully');
    } catch (error) {
      console.error('InteractionLogger: Failed to create tables:', error);
    }
  }

  /**
   * Log a user interaction
   */
  public async logInteraction(
    intent: string,
    action: string,
    metadata: {
      originalText?: string;
      parameters?: Record<string, any>;
      duration?: number;
      context?: Record<string, any>;
    } = {}
  ): Promise<string> {
    if (!this.isInitialized || !this.db) {
      console.warn('InteractionLogger: Database not initialized');
      return '';
    }

    const id = this.generateId();
    const timestamp = Date.now();
    const now = new Date();

    const context = {
      timeOfDay: this.getTimeOfDay(now),
      dayOfWeek: now.toLocaleDateString('en-US', { weekday: 'long' }),
      ...metadata.context
    };

    const insertQuery = `
      INSERT INTO interactions (
        id, timestamp, intent, original_text, action,
        parameters, duration, context
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?);
    `;

    try {
      await this.db.executeSql(insertQuery, [
        id,
        timestamp,
        intent,
        metadata.originalText || '',
        action,
        JSON.stringify(metadata.parameters || {}),
        metadata.duration || null,
        JSON.stringify(context)
      ]);

      console.log(`InteractionLogger: Logged interaction ${id}`);
      return id;
    } catch (error) {
      console.error('InteractionLogger: Failed to log interaction:', error);
      return '';
    }
  }

  /**
   * Get interaction logs with optional filtering
   */
  public async getLogs(filter: LogFilter = {}): Promise<InteractionLog[]> {
    if (!this.isInitialized || !this.db) {
      console.warn('InteractionLogger: Database not initialized');
      return [];
    }

    let query = 'SELECT * FROM interactions WHERE 1=1';
    const params: any[] = [];

    if (filter.intent) {
      query += ' AND intent = ?';
      params.push(filter.intent);
    }

    if (filter.dateFrom) {
      query += ' AND timestamp >= ?';
      params.push(filter.dateFrom.getTime());
    }

    if (filter.dateTo) {
      query += ' AND timestamp <= ?';
      params.push(filter.dateTo.getTime());
    }

    query += ' ORDER BY timestamp DESC';

    if (filter.limit) {
      query += ' LIMIT ?';
      params.push(filter.limit);
    }

    try {
      const [results] = await this.db.executeSql(query, params);
      const logs: InteractionLog[] = [];

      for (let i = 0; i < results.rows.length; i++) {
        const row = results.rows.item(i);
        logs.push({
          id: row.id,
          timestamp: new Date(row.timestamp),
          intent: row.intent,
          originalText: row.original_text,
          action: row.action,
          parameters: JSON.parse(row.parameters || '{}'),
          duration: row.duration,
          context: JSON.parse(row.context || '{}')
        });
      }

      return logs;
    } catch (error) {
      console.error('InteractionLogger: Failed to get logs:', error);
      return [];
    }
  }

  /**
   * Get logs for today
   */
  public async getTodayLogs(): Promise<InteractionLog[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    return this.getLogs({
      dateFrom: today,
      dateTo: tomorrow
    });
  }

  /**
   * Get logs for a specific date range
   */
  public async getLogsInRange(startDate: Date, endDate: Date): Promise<InteractionLog[]> {
    return this.getLogs({
      dateFrom: startDate,
      dateTo: endDate
    });
  }

  /**
   * Get most frequent intents
   */
  public async getMostFrequentIntents(limit: number = 10): Promise<Array<{intent: string, count: number}>> {
    if (!this.isInitialized || !this.db) {
      return [];
    }

    const query = `
      SELECT intent, COUNT(*) as count
      FROM interactions
      WHERE intent != 'unknown'
      GROUP BY intent
      ORDER BY count DESC
      LIMIT ?
    `;

    try {
      const [results] = await this.db.executeSql(query, [limit]);
      const intents: Array<{intent: string, count: number}> = [];

      for (let i = 0; i < results.rows.length; i++) {
        const row = results.rows.item(i);
        intents.push({
          intent: row.intent,
          count: row.count
        });
      }

      return intents;
    } catch (error) {
      console.error('InteractionLogger: Failed to get frequent intents:', error);
      return [];
    }
  }

  /**
   * Get usage statistics
   */
  public async getUsageStats(): Promise<{
    totalInteractions: number;
    todayInteractions: number;
    averagePerDay: number;
    mostActiveHour: number;
  }> {
    if (!this.isInitialized || !this.db) {
      return {
        totalInteractions: 0,
        todayInteractions: 0,
        averagePerDay: 0,
        mostActiveHour: 0
      };
    }

    try {
      // Total interactions
      const [totalResult] = await this.db.executeSql(
        'SELECT COUNT(*) as total FROM interactions'
      );
      const totalInteractions = totalResult.rows.item(0).total;

      // Today's interactions
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const [todayResult] = await this.db.executeSql(
        'SELECT COUNT(*) as today FROM interactions WHERE timestamp >= ?',
        [today.getTime()]
      );
      const todayInteractions = todayResult.rows.item(0).today;

      // Average per day (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const [avgResult] = await this.db.executeSql(
        'SELECT COUNT(*) as count FROM interactions WHERE timestamp >= ?',
        [thirtyDaysAgo.getTime()]
      );
      const averagePerDay = Math.round(avgResult.rows.item(0).count / 30);

      // Most active hour
      const [hourResult] = await this.db.executeSql(`
        SELECT strftime('%H', datetime(timestamp/1000, 'unixepoch', 'localtime')) as hour,
               COUNT(*) as count
        FROM interactions
        GROUP BY hour
        ORDER BY count DESC
        LIMIT 1
      `);
      const mostActiveHour = hourResult.rows.length > 0 ?
        parseInt(hourResult.rows.item(0).hour) : 0;

      return {
        totalInteractions,
        todayInteractions,
        averagePerDay,
        mostActiveHour
      };
    } catch (error) {
      console.error('InteractionLogger: Failed to get usage stats:', error);
      return {
        totalInteractions: 0,
        todayInteractions: 0,
        averagePerDay: 0,
        mostActiveHour: 0
      };
    }
  }

  /**
   * Clear all logs
   */
  public async clearLogs(): Promise<boolean> {
    if (!this.isInitialized || !this.db) {
      return false;
    }

    try {
      await this.db.executeSql('DELETE FROM interactions');
      console.log('InteractionLogger: All logs cleared');
      return true;
    } catch (error) {
      console.error('InteractionLogger: Failed to clear logs:', error);
      return false;
    }
  }

  /**
   * Clear old logs (older than specified days)
   */
  public async clearOldLogs(daysToKeep: number = 30): Promise<number> {
    if (!this.isInitialized || !this.db) {
      return 0;
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    try {
      const [result] = await this.db.executeSql(
        'DELETE FROM interactions WHERE timestamp < ?',
        [cutoffDate.getTime()]
      );

      const deletedCount = result.rowsAffected;
      console.log(`InteractionLogger: Cleared ${deletedCount} old logs`);
      return deletedCount;
    } catch (error) {
      console.error('InteractionLogger: Failed to clear old logs:', error);
      return 0;
    }
  }

  /**
   * Export logs as JSON
   */
  public async exportLogs(): Promise<string> {
    const logs = await this.getLogs();
    return JSON.stringify(logs, null, 2);
  }

  /**
   * Generate unique ID for log entries
   */
  private generateId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get time of day category
   */
  private getTimeOfDay(date: Date): string {
    const hour = date.getHours();

    if (hour >= 5 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 17) return 'afternoon';
    if (hour >= 17 && hour < 21) return 'evening';
    return 'night';
  }

  /**
   * Close database connection
   */
  public async close(): Promise<void> {
    if (this.db) {
      await this.db.close();
      this.db = null;
      this.isInitialized = false;
      console.log('InteractionLogger: Database connection closed');
    }
  }
}

// Export standalone functions for compatibility with prompt2.txt API
SQLite.enablePromise(true);

const DB_NAME = 'JARVIS.db';
const TABLE_NAME = 'interactions';

let db: SQLite.SQLiteDatabase | null = null;

export interface InteractionLogEntry {
  id?: number;
  timestamp: string;
  command: string;
  intent: string;
  parameters?: string; // stored as JSON
  actionTaken: string;
  duration?: number; // optional
}

export async function initLogger(): Promise<void> {
  try {
    db = await SQLite.openDatabase({ name: DB_NAME, location: 'default' });

    await db.executeSql(`
      CREATE TABLE IF NOT EXISTS ${TABLE_NAME} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        timestamp TEXT,
        command TEXT,
        intent TEXT,
        parameters TEXT,
        actionTaken TEXT,
        duration INTEGER
      );
    `);

    console.log('[InteractionLogger] Initialized.');
  } catch (err) {
    console.error('[InteractionLogger] DB init error:', err);
  }
}

export async function logInteraction(entry: InteractionLogEntry): Promise<void> {
  if (!db) return;

  const {
    timestamp,
    command,
    intent,
    parameters,
    actionTaken,
    duration = 0,
  } = entry;

  try {
    await db.executeSql(
      `INSERT INTO ${TABLE_NAME} (timestamp, command, intent, parameters, actionTaken, duration)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [timestamp, command, intent, JSON.stringify(parameters || {}), actionTaken, duration]
    );
    console.log('[InteractionLogger] Entry logged.');
  } catch (err) {
    console.error('[InteractionLogger] Log error:', err);
  }
}

export async function getRecentLogs(limit = 50): Promise<InteractionLogEntry[]> {
  if (!db) return [];

  try {
    const [results] = await db.executeSql(
      `SELECT * FROM ${TABLE_NAME} ORDER BY id DESC LIMIT ?`,
      [limit]
    );

    const logs: InteractionLogEntry[] = [];
    for (let i = 0; i < results.rows.length; i++) {
      const row = results.rows.item(i);
      logs.push({
        id: row.id,
        timestamp: row.timestamp,
        command: row.command,
        intent: row.intent,
        parameters: JSON.parse(row.parameters),
        actionTaken: row.actionTaken,
        duration: row.duration,
      });
    }

    return logs;
  } catch (err) {
    console.error('[InteractionLogger] Fetch error:', err);
    return [];
  }
}

export async function clearLogs(): Promise<void> {
  if (!db) return;

  try {
    await db.executeSql(`DELETE FROM ${TABLE_NAME}`);
    console.log('[InteractionLogger] Logs cleared.');
  } catch (err) {
    console.error('[InteractionLogger] Clear error:', err);
  }
}

export async function exportLogsAsJSON(): Promise<string> {
  const logs = await getRecentLogs(1000); // get more for export
  return JSON.stringify(logs, null, 2);
}

export const interactionLogger = new InteractionLogger();
