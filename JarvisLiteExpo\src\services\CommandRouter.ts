/**
 * CommandRouter.ts
 * Routes parsed intents to appropriate system actions
 * Coordinates between different service modules
 */

import { IntentObject, SystemAction, AppEvent } from '@/types';
import { systemControlAndroid } from './SystemControlAndroid';
import { offlineTTS } from './OfflineTTS';
import { interactionLogger } from './InteractionLogger';
import { EventEmitter } from './EventEmitter';

interface CommandResult {
  success: boolean;
  message: string;
  action?: SystemAction;
  data?: any;
}

class CommandRouter {
  private eventEmitter: EventEmitter;

  constructor() {
    this.eventEmitter = new EventEmitter();
  }

  /**
   * Route an intent to the appropriate action
   */
  public async routeCommand(intent: IntentObject): Promise<CommandResult> {
    try {
      // Log the interaction
      await interactionLogger.logInteraction(
        intent.intent,
        'command_received',
        {
          originalText: intent.originalText,
          confidence: intent.confidenceScore,
          parameters: intent.parameters
        }
      );

      // Route based on intent type
      let result: CommandResult;

      switch (intent.intent) {
        case 'open_app':
          result = await this.handleOpenApp(intent);
          break;

        case 'toggle_flashlight':
          result = await this.handleToggleFlashlight(intent);
          break;

        case 'adjust_volume':
          result = await this.handleAdjustVolume(intent);
          break;

        case 'toggle_wifi':
          result = await this.handleToggleWifi(intent);
          break;

        case 'make_call':
          result = await this.handleMakeCall(intent);
          break;

        case 'create_reminder':
          result = await this.handleCreateReminder(intent);
          break;

        case 'get_time':
          result = await this.handleGetTime(intent);
          break;

        case 'get_date':
          result = await this.handleGetDate(intent);
          break;

        case 'play_music':
          result = await this.handlePlayMusic(intent);
          break;

        case 'show_activity':
          result = await this.handleShowActivity(intent);
          break;

        case 'stop_listening':
          result = await this.handleStopListening(intent);
          break;

        default:
          result = await this.handleUnknownIntent(intent);
          break;
      }

      // Log the result
      await interactionLogger.logInteraction(
        intent.intent,
        result.success ? 'command_executed' : 'command_failed',
        {
          message: result.message,
          action: result.action,
          data: result.data
        }
      );

      // Emit event for UI updates
      this.eventEmitter.emit({
        type: 'command_completed',
        payload: { intent, result },
        timestamp: new Date()
      });

      return result;

    } catch (error) {
      const errorResult: CommandResult = {
        success: false,
        message: `Error processing command: ${error.message}`
      };

      await interactionLogger.logInteraction(
        intent.intent,
        'command_error',
        { error: error.message }
      );

      return errorResult;
    }
  }

  /**
   * Handle app opening commands
   */
  private async handleOpenApp(intent: IntentObject): Promise<CommandResult> {
    const appName = intent.parameters.app;
    
    if (!appName) {
      return {
        success: false,
        message: "I need to know which app to open."
      };
    }

    try {
      const success = await systemControlAndroid.launchApp(appName);
      
      if (success) {
        const message = `Opening ${appName}`;
        await offlineTTS.speak(message);
        
        return {
          success: true,
          message,
          action: {
            type: 'app_launch',
            target: appName
          }
        };
      } else {
        const message = `Sorry, I couldn't find or open ${appName}`;
        await offlineTTS.speak(message);
        
        return {
          success: false,
          message
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `Error opening ${appName}: ${error.message}`
      };
    }
  }

  /**
   * Handle flashlight toggle commands
   */
  private async handleToggleFlashlight(intent: IntentObject): Promise<CommandResult> {
    try {
      const isOn = await systemControlAndroid.toggleFlashlight();
      const message = `Flashlight ${isOn ? 'on' : 'off'}`;
      
      await offlineTTS.speak(message);
      
      return {
        success: true,
        message,
        action: {
          type: 'system_control',
          target: 'flashlight',
          parameters: { state: isOn }
        }
      };
    } catch (error) {
      return {
        success: false,
        message: `Error controlling flashlight: ${error.message}`
      };
    }
  }

  /**
   * Handle volume adjustment commands
   */
  private async handleAdjustVolume(intent: IntentObject): Promise<CommandResult> {
    try {
      const level = intent.parameters.level;
      const direction = intent.parameters.direction;
      
      let newVolume: number;
      
      if (level) {
        // Set specific volume level
        const volumeLevel = parseInt(level);
        if (isNaN(volumeLevel) || volumeLevel < 0 || volumeLevel > 100) {
          return {
            success: false,
            message: "Volume level must be between 0 and 100"
          };
        }
        newVolume = await systemControlAndroid.setVolume(volumeLevel);
      } else {
        // Adjust volume up or down
        const isUp = intent.originalText.toLowerCase().includes('up') || 
                     intent.originalText.toLowerCase().includes('increase');
        newVolume = await systemControlAndroid.adjustVolume(isUp ? 'up' : 'down');
      }
      
      const message = `Volume set to ${newVolume}%`;
      await offlineTTS.speak(message);
      
      return {
        success: true,
        message,
        action: {
          type: 'system_control',
          target: 'volume',
          parameters: { level: newVolume }
        }
      };
    } catch (error) {
      return {
        success: false,
        message: `Error adjusting volume: ${error.message}`
      };
    }
  }

  /**
   * Handle WiFi toggle commands
   */
  private async handleToggleWifi(intent: IntentObject): Promise<CommandResult> {
    try {
      const isEnabled = await systemControlAndroid.toggleWifi();
      const message = `WiFi ${isEnabled ? 'enabled' : 'disabled'}`;
      
      await offlineTTS.speak(message);
      
      return {
        success: true,
        message,
        action: {
          type: 'system_control',
          target: 'wifi',
          parameters: { enabled: isEnabled }
        }
      };
    } catch (error) {
      return {
        success: false,
        message: `Error controlling WiFi: ${error.message}`
      };
    }
  }

  /**
   * Handle phone call commands
   */
  private async handleMakeCall(intent: IntentObject): Promise<CommandResult> {
    const contact = intent.parameters.contact || intent.parameters.number;
    
    if (!contact) {
      return {
        success: false,
        message: "I need a contact name or phone number to call."
      };
    }

    try {
      const success = await systemControlAndroid.makeCall(contact);
      
      if (success) {
        const message = `Calling ${contact}`;
        await offlineTTS.speak(message);
        
        return {
          success: true,
          message,
          action: {
            type: 'system_control',
            target: 'phone',
            parameters: { contact }
          }
        };
      } else {
        return {
          success: false,
          message: `Sorry, I couldn't make the call to ${contact}`
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `Error making call: ${error.message}`
      };
    }
  }

  /**
   * Handle reminder creation commands
   */
  private async handleCreateReminder(intent: IntentObject): Promise<CommandResult> {
    const task = intent.parameters.task;
    const time = intent.parameters.time;
    const duration = intent.parameters.duration;
    
    if (!task) {
      return {
        success: false,
        message: "I need to know what to remind you about."
      };
    }

    try {
      // This would integrate with the reminder service
      // For now, we'll just acknowledge the request
      let message = `I'll remind you to ${task}`;
      
      if (time) {
        message += ` at ${time}`;
      } else if (duration) {
        message += ` in ${duration}`;
      }
      
      await offlineTTS.speak(message);
      
      return {
        success: true,
        message,
        action: {
          type: 'reminder',
          target: 'create',
          parameters: { task, time, duration }
        }
      };
    } catch (error) {
      return {
        success: false,
        message: `Error creating reminder: ${error.message}`
      };
    }
  }

  /**
   * Handle time query commands
   */
  private async handleGetTime(intent: IntentObject): Promise<CommandResult> {
    const now = new Date();
    const timeString = now.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
    
    const message = `It's ${timeString}`;
    await offlineTTS.speak(message);
    
    return {
      success: true,
      message,
      data: { time: timeString, timestamp: now }
    };
  }

  /**
   * Handle date query commands
   */
  private async handleGetDate(intent: IntentObject): Promise<CommandResult> {
    const now = new Date();
    const dateString = now.toLocaleDateString([], {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    
    const message = `Today is ${dateString}`;
    await offlineTTS.speak(message);
    
    return {
      success: true,
      message,
      data: { date: dateString, timestamp: now }
    };
  }

  /**
   * Handle music playback commands
   */
  private async handlePlayMusic(intent: IntentObject): Promise<CommandResult> {
    try {
      // Try to open a music app
      const musicApps = ['spotify', 'youtube music', 'google play music', 'music'];
      let success = false;
      
      for (const app of musicApps) {
        success = await systemControlAndroid.launchApp(app);
        if (success) break;
      }
      
      const message = success ? 'Playing music' : 'Opening music app';
      await offlineTTS.speak(message);
      
      return {
        success: true,
        message,
        action: {
          type: 'app_launch',
          target: 'music_app'
        }
      };
    } catch (error) {
      return {
        success: false,
        message: `Error playing music: ${error.message}`
      };
    }
  }

  /**
   * Handle activity query commands
   */
  private async handleShowActivity(intent: IntentObject): Promise<CommandResult> {
    try {
      // This would integrate with the activity timeline
      const message = "Let me show you your recent activity";
      await offlineTTS.speak(message);
      
      return {
        success: true,
        message,
        action: {
          type: 'navigation',
          target: 'ActivityTimeline'
        }
      };
    } catch (error) {
      return {
        success: false,
        message: `Error showing activity: ${error.message}`
      };
    }
  }

  /**
   * Handle stop listening commands
   */
  private async handleStopListening(intent: IntentObject): Promise<CommandResult> {
    const message = "Okay, I'll stop listening";
    await offlineTTS.speak(message);
    
    // Emit event to stop voice processing
    this.eventEmitter.emit({
      type: 'stop_listening_requested',
      payload: {},
      timestamp: new Date()
    });
    
    return {
      success: true,
      message,
      action: {
        type: 'system_control',
        target: 'voice_processor',
        parameters: { action: 'stop' }
      }
    };
  }

  /**
   * Handle unknown intents
   */
  private async handleUnknownIntent(intent: IntentObject): Promise<CommandResult> {
    const responses = [
      "I'm not sure what you want me to do.",
      "Could you try saying that differently?",
      "I didn't understand that command.",
      "Sorry, I don't know how to do that yet."
    ];
    
    const message = responses[Math.floor(Math.random() * responses.length)];
    await offlineTTS.speak(message);
    
    return {
      success: false,
      message
    };
  }

  /**
   * Subscribe to command events
   */
  public onEvent(callback: (event: AppEvent) => void): () => void {
    return this.eventEmitter.subscribe(callback);
  }

  /**
   * Get available commands
   */
  public getAvailableCommands(): string[] {
    return [
      'open_app',
      'toggle_flashlight',
      'adjust_volume',
      'toggle_wifi',
      'make_call',
      'create_reminder',
      'get_time',
      'get_date',
      'play_music',
      'show_activity',
      'stop_listening'
    ];
  }
}

export const commandRouter = new CommandRouter();
