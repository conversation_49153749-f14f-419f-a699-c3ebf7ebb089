import { NativeEventEmitter, NativeModules, PermissionsAndroid, Platform } from 'react-native';

const { VoiceProcessorModule } = NativeModules;

if (!VoiceProcessorModule && Platform.OS === 'android') {
  console.warn(
    "VoiceProcessorModule: Native module not found. Did you forget to link it?"
  );
}

/**
 * Defines the possible states of the voice processor.
 */
export enum VoiceProcessorState {
  IDLE = 'idle',
  LISTENING = 'listening',
  PROCESSING = 'processing', // Optional: if there's a distinct processing phase
  ERROR = 'error',
}

/**
 * Structure for the speech recognition result.
 */
export interface SpeechResult {
  text: string;          // The transcribed text
  isFinal: boolean;      // True if this is the final recognized segment
  confidence?: number;   // Optional: confidence score (0.0 to 1.0)
}

/**
 * Structure for error details.
 */
export interface VoiceProcessorError {
  code: string;          // Error code (e.g., 'permission_denied', 'stt_init_failed')
  message: string;       // Human-readable error message
}

/**
 * Defines the events emitted by the VoiceProcessor.
 */
export type VoiceProcessorEvent =
  | { type: 'onSpeechResult'; payload: SpeechResult }
  | { type: 'onSpeechStart'; payload?: undefined }
  | { type: 'onSpeechEnd'; payload?: undefined } // End of utterance detected
  | { type: 'onSpeechPartialResult'; payload: SpeechResult } // For real-time partial results
  | { type: 'onError'; payload: VoiceProcessorError }
  | { type: 'onStateChanged'; payload: VoiceProcessorState };

type EventCallback = (event: VoiceProcessorEvent) => void;

class VoiceProcessor {
  private eventEmitter: NativeEventEmitter;
  private listeners: Set<EventCallback> = new Set();
  private currentState: VoiceProcessorState = VoiceProcessorState.IDLE;

  constructor() {
    this.eventEmitter = new NativeEventEmitter(VoiceProcessorModule);

    // Subscribe to native events
    this.eventEmitter.addListener('onSpeechResult', (data: SpeechResult) => this.emit({ type: 'onSpeechResult', payload: data }));
    this.eventEmitter.addListener('onSpeechStart', () => this.emit({ type: 'onSpeechStart' }));
    this.eventEmitter.addListener('onSpeechEnd', () => this.emit({ type: 'onSpeechEnd' }));
    this.eventEmitter.addListener('onSpeechPartialResult', (data: SpeechResult) => this.emit({ type: 'onSpeechPartialResult', payload: data }));
    this.eventEmitter.addListener('onError', (error: VoiceProcessorError) => {
      this.updateState(VoiceProcessorState.ERROR);
      this.emit({ type: 'onError', payload: error });
    });
    // Potentially a native event for state changes if STT engine reports them
    // this.eventEmitter.addListener('onNativeStateChanged', (state: VoiceProcessorState) => this.updateState(state));
  }

  private updateState(newState: VoiceProcessorState) {
    if (this.currentState !== newState) {
      this.currentState = newState;
      this.emit({ type: 'onStateChanged', payload: this.currentState });
    }
  }

  private emit(event: VoiceProcessorEvent) {
    this.listeners.forEach(listener => listener(event));
  }

  public onEvent(callback: EventCallback): () => void {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback); // Unsubscribe function
  }

  async initialize(): Promise<boolean> {
    // TODO: Call native initialize method. Handle permissions here or in startListening.
    // For now, let's assume it's called and returns success.
    console.log('VoiceProcessor: Initializing...');
    // const success = await VoiceProcessorModule.initializeEngine();
    // if (success) this.updateState(VoiceProcessorState.IDLE); else this.updateState(VoiceProcessorState.ERROR);
    return true;
  }

  async startListening(continuous: boolean = false): Promise<void> {
    // TODO: Request mic permission if not granted
    // TODO: Call native startListening method
    console.log(`VoiceProcessor: Start listening (continuous: ${continuous})`);
    this.updateState(VoiceProcessorState.LISTENING);
    // await VoiceProcessorModule.startListening(continuous);
  }

  async stopListening(): Promise<void> {
    // TODO: Call native stopListening method
    console.log('VoiceProcessor: Stop listening');
    this.updateState(VoiceProcessorState.IDLE); // Or PROCESSING if STT takes time
    // await VoiceProcessorModule.stopListening();
  }

  getCurrentState(): VoiceProcessorState {
    return this.currentState;
  }

  // TODO: Add methods for checking permissions, engine status, etc.
}

export const voiceProcessor = new VoiceProcessor();