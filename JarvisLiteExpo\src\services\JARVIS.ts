/**
 * JARVIS.ts
 * Main orchestrator that ties all modules together
 * Handles the complete voice command pipeline
 */

import {
  initializeVoskModel,
  startListening,
  stopListening,
  isListening
} from './VoiceProcessor';
import { parseIntent } from './IntentParser';
import {
  toggleFlashlight,
  openApp,
  adjustBrightness,
  adjustVolume,
  makePhoneCall
} from './SystemControlAndroid';
import {
  initializeTTS,
  speak,
  stop as stopTTS,
  setVoiceProfile
} from './OfflineTTS';
import {
  initLogger,
  logInteraction,
  InteractionLogEntry
} from './InteractionLogger';
import {
  analyzePatterns,
  getSuggestions
} from './LocalMemory';
import {
  initializeLLM,
  generateAndSpeak
} from './LLMEngine';
import {
  initializeAutomationEngine
} from './AutomationEngine';
import {
  initReminderManager
} from './ReminderManager';
import {
  initializeWakeWordDetector,
  startWakeWordDetection
} from './WakeWordDetector';
import {
  initializeBackgroundAutomation
} from './BackgroundAutomation';
import {
  speakDailySummary
} from './DailySummary';

let isJarvisReady = false;

export async function initializeJARVIS(): Promise<boolean> {
  try {
    console.log('[JARVIS] Initializing all modules...');

    // Initialize all modules
    await Promise.all([
      initializeVoskModel(),
      initLogger(),
      initializeLLM(),
      initReminderManager(),
      initializeWakeWordDetector()
    ]);

    initializeTTS();
    setVoiceProfile('jarvis');
    initializeAutomationEngine();
    initializeBackgroundAutomation();

    // Analyze existing patterns
    await analyzePatterns();

    // Start wake word detection
    await startWakeWordDetection(() => {
      console.log('[JARVIS] Wake word detected - starting voice listening');
      // This would trigger voice listening in the UI
    });

    isJarvisReady = true;
    console.log('[JARVIS] All modules initialized successfully!');

    await speak("JARVIS Lite is now online and ready to assist you.");
    return true;
  } catch (error) {
    console.error('[JARVIS] Initialization failed:', error);
    return false;
  }
}

export async function processVoiceCommand(command: string): Promise<void> {
  if (!isJarvisReady) {
    console.warn('[JARVIS] System not ready');
    return;
  }

  const startTime = Date.now();
  console.log(`[JARVIS] Processing command: "${command}"`);

  try {
    // Parse intent
    const intentResult = parseIntent(command);
    console.log(`[JARVIS] Intent: ${intentResult.intent}`);

    // Execute action based on intent
    let actionTaken = 'unknown';

    switch (intentResult.intent) {
      case 'TOGGLE_FLASHLIGHT':
        await toggleFlashlight();
        await speak('Flashlight toggled');
        actionTaken = 'flashlight_toggled';
        break;

      case 'OPEN_APP_CAMERA':
        await openApp('camera');
        await speak('Opening camera');
        actionTaken = 'opened_camera';
        break;

      case 'OPEN_APP_GALLERY':
        await openApp('gallery');
        await speak('Opening gallery');
        actionTaken = 'opened_gallery';
        break;

      case 'ADJUST_BRIGHTNESS':
        const brightnessLevel = extractNumber(command) || 0.5;
        await adjustBrightness(brightnessLevel);
        actionTaken = `brightness_${brightnessLevel}`;
        break;

      case 'ADJUST_VOLUME':
        const volumeLevel = extractNumber(command) || 0.5;
        await adjustVolume(volumeLevel);
        actionTaken = `volume_${volumeLevel}`;
        break;

      case 'MAKE_CALL':
        const contact = intentResult.parameters?.contact;
        if (contact) {
          await makePhoneCall(contact);
          await speak(`Calling ${contact}`);
          actionTaken = `called_${contact}`;
        } else {
          await speak('Please specify who to call');
          actionTaken = 'call_failed_no_contact';
        }
        break;

      case 'SET_REMINDER':
        const reminder = intentResult.parameters?.reminder;
        if (reminder) {
          // TODO: Implement reminder system
          await speak(`Reminder set: ${reminder}`);
          actionTaken = `reminder_${reminder}`;
        } else {
          await speak('Please specify what to remind you about');
          actionTaken = 'reminder_failed';
        }
        break;

      case 'PLAY_MUSIC':
        await openApp('youtube'); // or music app
        await speak('Playing music');
        actionTaken = 'music_started';
        break;

      case 'GET_TIME':
        const time = new Date().toLocaleTimeString();
        await speak(`The time is ${time}`);
        actionTaken = 'time_provided';
        break;

      case 'GET_DATE':
        const date = new Date().toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
        await speak(`Today is ${date}`);
        actionTaken = 'date_provided';
        break;

      case 'DAILY_SUMMARY':
        await speakDailySummary();
        actionTaken = 'daily_summary_provided';
        break;

      case 'UNKNOWN':
      default:
        // Use LLM for conversational response
        await generateAndSpeak(command);
        actionTaken = 'llm_response';
        break;
    }

    // Log the interaction
    const logEntry: InteractionLogEntry = {
      timestamp: new Date().toISOString(),
      command,
      intent: intentResult.intent,
      parameters: JSON.stringify(intentResult.parameters),
      actionTaken,
      duration: Date.now() - startTime
    };

    await logInteraction(logEntry);

    // Re-analyze patterns periodically
    if (Math.random() < 0.1) { // 10% chance
      await analyzePatterns();
    }

  } catch (error) {
    console.error('[JARVIS] Error processing command:', error);
    await speak('Sorry, I encountered an error processing that command');
  }
}

export async function startVoiceListening(): Promise<void> {
  if (!isJarvisReady) {
    console.warn('[JARVIS] System not ready');
    return;
  }

  if (isListening()) {
    console.log('[JARVIS] Already listening');
    return;
  }

  try {
    await startListening(processVoiceCommand);
    console.log('[JARVIS] Voice listening started');
  } catch (error) {
    console.error('[JARVIS] Failed to start listening:', error);
    await speak('Sorry, I cannot start voice listening right now');
  }
}

export async function stopVoiceListening(): Promise<void> {
  try {
    await stopListening();
    console.log('[JARVIS] Voice listening stopped');
  } catch (error) {
    console.error('[JARVIS] Failed to stop listening:', error);
  }
}

export function isJARVISReady(): boolean {
  return isJarvisReady;
}

export async function getSmartSuggestions(): Promise<string[]> {
  const currentHour = new Date().getHours().toString().padStart(2, '0') + ':00';
  const suggestions = getSuggestions(currentHour);

  return suggestions.map(s =>
    `Based on your patterns, you might want to: ${s.resultAction}`
  );
}

export async function emergencyStop(): Promise<void> {
  try {
    await stopListening();
    stopTTS();
    console.log('[JARVIS] Emergency stop executed');
  } catch (error) {
    console.error('[JARVIS] Emergency stop failed:', error);
  }
}

// Helper function to extract numbers from text
function extractNumber(text: string): number | null {
  const match = text.match(/\d+/);
  return match ? parseInt(match[0]) / 100 : null; // Convert percentage to 0-1
}
