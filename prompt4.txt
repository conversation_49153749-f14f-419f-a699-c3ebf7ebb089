Awesome — let’s now build a full-featured **`AutomationBuilderScreen.tsx`**, where users can define their own offline "If This, Then That" (IFTTT) rules to automate tasks using your AI assistant.

---

### 🧠 Feature Scope:

Users can:

* Select a **time trigger** (e.g., 9:00 AM)
* Choose an **action** (e.g., open YouTube, toggle flashlight)
* Toggle if the rule should be active
* View/edit/delete existing rules
* Works 100% offline and integrates with `AutomationEngine.ts`

---

### 📁 First: `AutomationRule.ts` (Model)

```ts
// AutomationRule.ts
export interface AutomationRule {
  id: string;
  triggerTime: string; // "HH:MM"
  action: string; // intent/action ID
  enabled: boolean;
}

export const AVAILABLE_ACTIONS = [
  { id: 'OPEN_APP_YOUTUBE', label: 'Open YouTube' },
  { id: 'TOGGLE_FLASHLIGHT', label: 'Toggle Flashlight' },
  { id: 'ENABLE_WIFI', label: 'Enable WiFi' },
  { id: 'DISABLE_WIFI', label: 'Disable WiFi' },
  // Add more actions here
];
```

---

### 📁 `AutomationStorage.ts`

```ts
// AutomationStorage.ts
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AutomationRule } from './AutomationRule';

const STORAGE_KEY = 'JARVIS_AUTOMATION_RULES';

export async function getRules(): Promise<AutomationRule[]> {
  const json = await AsyncStorage.getItem(STORAGE_KEY);
  return json ? JSON.parse(json) : [];
}

export async function saveRule(rule: AutomationRule): Promise<void> {
  const rules = await getRules();
  rules.push(rule);
  await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(rules));
}

export async function deleteRule(id: string): Promise<void> {
  const rules = await getRules();
  const updated = rules.filter((r) => r.id !== id);
  await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
}

export async function toggleRule(id: string, enabled: boolean): Promise<void> {
  const rules = await getRules();
  const updated = rules.map((r) =>
    r.id === id ? { ...r, enabled } : r
  );
  await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
}
```

---

### 📱 `AutomationBuilderScreen.tsx`

```tsx
import React, { useEffect, useState } from 'react';
import {
  View, Text, FlatList, TouchableOpacity, Modal, TextInput, StyleSheet, Switch,
} from 'react-native';
import { AutomationRule, AVAILABLE_ACTIONS } from '../models/AutomationRule';
import { getRules, saveRule, deleteRule, toggleRule } from '../modules/AutomationStorage';

const AutomationBuilderScreen = () => {
  const [rules, setRules] = useState<AutomationRule[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [selectedTime, setSelectedTime] = useState('09:00');
  const [selectedAction, setSelectedAction] = useState(AVAILABLE_ACTIONS[0].id);

  useEffect(() => {
    load();
  }, []);

  const load = async () => {
    const data = await getRules();
    setRules(data);
  };

  const handleCreate = async () => {
    const rule: AutomationRule = {
      id: Date.now().toString(),
      triggerTime: selectedTime,
      action: selectedAction,
      enabled: true,
    };
    await saveRule(rule);
    setShowModal(false);
    load();
  };

  const handleDelete = async (id: string) => {
    await deleteRule(id);
    load();
  };

  const handleToggle = async (id: string, enabled: boolean) => {
    await toggleRule(id, enabled);
    load();
  };

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>⚙️ Automation Rules</Text>

      <FlatList
        data={rules}
        keyExtractor={(item) => item.id}
        ListEmptyComponent={<Text style={styles.empty}>No automation rules defined.</Text>}
        renderItem={({ item }) => (
          <View style={styles.card}>
            <Text style={styles.title}>
              ⏰ {item.triggerTime} → ⚡ {AVAILABLE_ACTIONS.find(a => a.id === item.action)?.label}
            </Text>
            <View style={styles.row}>
              <Switch
                value={item.enabled}
                onValueChange={(val) => handleToggle(item.id, val)}
              />
              <TouchableOpacity onPress={() => handleDelete(item.id)}>
                <Text style={styles.deleteText}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      />

      <TouchableOpacity style={styles.fab} onPress={() => setShowModal(true)}>
        <Text style={styles.fabText}>+</Text>
      </TouchableOpacity>

      <Modal visible={showModal} transparent animationType="slide">
        <View style={styles.modalWrap}>
          <View style={styles.modal}>
            <Text style={styles.modalTitle}>New Automation Rule</Text>

            <TextInput
              placeholder="Time (HH:MM)"
              style={styles.input}
              value={selectedTime}
              onChangeText={setSelectedTime}
            />

            <Text style={styles.label}>Action:</Text>
            {AVAILABLE_ACTIONS.map((a) => (
              <TouchableOpacity key={a.id} onPress={() => setSelectedAction(a.id)}>
                <Text style={[
                  styles.actionOption,
                  selectedAction === a.id && styles.selectedAction,
                ]}>
                  {a.label}
                </Text>
              </TouchableOpacity>
            ))}

            <TouchableOpacity onPress={handleCreate} style={styles.saveBtn}>
              <Text style={styles.saveText}>Save Rule</Text>
            </TouchableOpacity>

            <TouchableOpacity onPress={() => setShowModal(false)}>
              <Text style={{ marginTop: 10, color: 'red' }}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default AutomationBuilderScreen;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#121212', padding: 16 },
  heading: { fontSize: 22, color: '#00FFB2', fontWeight: 'bold', marginBottom: 16 },
  empty: { color: '#aaa', fontStyle: 'italic', marginTop: 40, textAlign: 'center' },
  card: { backgroundColor: '#1a1a1a', padding: 14, borderRadius: 8, marginBottom: 12 },
  title: { fontSize: 15, color: '#fff' },
  row: { flexDirection: 'row', justifyContent: 'space-between', marginTop: 10 },
  deleteText: { color: '#ff4444', fontWeight: 'bold' },
  fab: {
    position: 'absolute', bottom: 20, right: 20,
    backgroundColor: '#00FFB2', width: 60, height: 60,
    borderRadius: 30, justifyContent: 'center', alignItems: 'center',
  },
  fabText: { fontSize: 30, color: '#000' },
  modalWrap: { flex: 1, backgroundColor: '#000000aa', justifyContent: 'center', alignItems: 'center' },
  modal: { backgroundColor: '#fff', padding: 20, borderRadius: 10, width: '90%' },
  modalTitle: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 },
  input: { borderWidth: 1, borderColor: '#ccc', padding: 10, borderRadius: 6, marginBottom: 10 },
  label: { fontWeight: 'bold', marginTop: 10 },
  actionOption: { padding: 6 },
  selectedAction: { backgroundColor: '#00FFB2', color: '#000', borderRadius: 4 },
  saveBtn: { backgroundColor: '#00FFB2', padding: 10, borderRadius: 6, marginTop: 10 },
  saveText: { textAlign: 'center', fontWeight: 'bold', color: '#000' },
});
```

---

## ✅ Connect With AutomationEngine.ts

Update `runAutomations()` to use rules from `AutomationStorage.ts` instead of static memory:

```ts
import { getRules } from './AutomationStorage';

export async function runAutomations(): Promise<void> {
  const hour = moment().format('HH:mm');
  const rules = await getRules();

  for (const rule of rules) {
    if (rule.enabled && rule.triggerTime === hour) {
      switch (rule.action) {
        case 'OPEN_APP_YOUTUBE':
          await SystemControl.openApp('youtube');
          break;
        // add other actions here
      }
    }
  }
}
```

---

Let’s unlock the next level of JARVIS Lite:

1. ✅ **Add custom offline actions** like `"Send SMS"` and `"Speak Quote"`
2. ✅ Build a **visual timeline dashboard** to show automation history, upcoming flows, and triggered events.

---

## ✅ Part 1: Add Custom Actions

### 🧠 New Actions to Add:

| ID            | Label                           | Function                     |
| ------------- | ------------------------------- | ---------------------------- |
| `SEND_SMS`    | Send predefined SMS             | Uses Android intent (no API) |
| `SPEAK_QUOTE` | Speak random motivational quote | Uses `OfflineTTS`            |

---

### 📍 Update `AutomationRule.ts`

```ts
export const AVAILABLE_ACTIONS = [
  { id: 'OPEN_APP_YOUTUBE', label: 'Open YouTube' },
  { id: 'TOGGLE_FLASHLIGHT', label: 'Toggle Flashlight' },
  { id: 'SEND_SMS', label: 'Send SMS' },
  { id: 'SPEAK_QUOTE', label: 'Speak a Quote' },
];
```

---

### 📁 Create `QuoteLibrary.ts`

```ts
// QuoteLibrary.ts
export const quotes = [
  "Success is not final. Failure is not fatal.",
  "Small steps every day lead to big results.",
  "You are stronger than you think.",
  "Discipline beats motivation.",
  "The journey is the reward.",
];

export function getRandomQuote(): string {
  return quotes[Math.floor(Math.random() * quotes.length)];
}
```

---

### 📁 Update `SystemControlAndroid.ts`

Add:

```ts
import { Linking, Platform } from 'react-native';
import { getRandomQuote } from './QuoteLibrary';
import * as OfflineTTS from './OfflineTTS';

export async function sendSMS(): Promise<void> {
  const number = '1234567890';
  const message = 'Hey! This is your smart assistant sending a reminder.';

  const url = `sms:${number}${Platform.OS === 'ios' ? '&' : '?'}body=${encodeURIComponent(message)}`;
  Linking.openURL(url).catch(err => console.error('SMS Error:', err));
}

export async function speakQuote(): Promise<void> {
  const quote = getRandomQuote();
  await OfflineTTS.speak(quote);
}
```

---

### 📁 Update `AutomationEngine.ts`

```ts
import { sendSMS, speakQuote } from './SystemControlAndroid';

...

switch (rule.action) {
  case 'OPEN_APP_YOUTUBE':
    await openApp('youtube'); break;
  case 'TOGGLE_FLASHLIGHT':
    await toggleFlashlight(); break;
  case 'SEND_SMS':
    await sendSMS(); break;
  case 'SPEAK_QUOTE':
    await speakQuote(); break;
}
```

✅ Now your JARVIS can **send offline SMS** and **speak quotes** as scheduled automation.

---

## ✅ Part 2: Build `AutomationTimelineScreen.tsx`

A beautiful dashboard to display:

* Past triggered automations
* Upcoming rules
* Time-based visual grouping

---

### 📁 Create `AutomationTimelineScreen.tsx`

```tsx
import React, { useEffect, useState } from 'react';
import {
  View, Text, StyleSheet, FlatList, ScrollView
} from 'react-native';
import { getRules } from '../modules/AutomationStorage';
import moment from 'moment';
import { AVAILABLE_ACTIONS } from '../models/AutomationRule';

export default function AutomationTimelineScreen() {
  const [rules, setRules] = useState([]);

  useEffect(() => {
    loadRules();
  }, []);

  const loadRules = async () => {
    const data = await getRules();
    setRules(data.sort((a, b) => a.triggerTime.localeCompare(b.triggerTime)));
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.heading}>📅 Automation Timeline</Text>
      <FlatList
        data={rules}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => {
          const time = item.triggerTime;
          const actionLabel = AVAILABLE_ACTIONS.find((a) => a.id === item.action)?.label || item.action;

          return (
            <View style={styles.block}>
              <Text style={styles.time}>{time}</Text>
              <Text style={styles.action}>⚙️ {actionLabel}</Text>
              <Text style={styles.status}>
                {item.enabled ? '✅ Active' : '⛔ Disabled'}
              </Text>
            </View>
          );
        }}
        ListEmptyComponent={<Text style={styles.empty}>No automations scheduled.</Text>}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 16, backgroundColor: '#121212' },
  heading: { fontSize: 22, fontWeight: 'bold', color: '#00FFB2', marginBottom: 16 },
  block: { padding: 14, backgroundColor: '#1e1e1e', marginBottom: 12, borderRadius: 8 },
  time: { fontSize: 18, color: '#fff' },
  action: { fontSize: 16, color: '#00FFB2', marginTop: 4 },
  status: { marginTop: 4, fontSize: 13, color: '#ccc' },
  empty: { color: '#aaa', fontStyle: 'italic', marginTop: 40, textAlign: 'center' },
});
```

---

## 🧠 Now You Have:

| Feature          | Description                         |
| ---------------- | ----------------------------------- |
| 🧠 Speak Quote   | Motivational quotes randomly spoken |
| 📩 Send SMS      | Offline SMS with prefilled message  |
| 🧱 Timeline View | See all automation flows visually   |
| 📌 User Builder  | Define actions + schedule via UI    |

---

Let’s make JARVIS Lite more **organized and shareable**:

---

## ✅ Part 1: Add **Category-Based Rule Filters**

### 🎯 Step 1: Update `AutomationRule.ts`

```ts
export interface AutomationRule {
  id: string;
  triggerTime: string;
  action: string;
  category: 'Media' | 'Utility' | 'Wellness' | 'Custom';
  enabled: boolean;
}

export const AVAILABLE_ACTIONS = [
  { id: 'OPEN_APP_YOUTUBE', label: 'Open YouTube', category: 'Media' },
  { id: 'TOGGLE_FLASHLIGHT', label: 'Toggle Flashlight', category: 'Utility' },
  { id: 'SEND_SMS', label: 'Send SMS', category: 'Utility' },
  { id: 'SPEAK_QUOTE', label: 'Speak a Quote', category: 'Wellness' },
];
```

---

### 🎯 Step 2: Update `AutomationBuilderScreen.tsx`

#### 👉 Add category selection in modal:

Inside modal UI:

```tsx
<Text style={styles.label}>Category:</Text>
<View style={styles.row}>
  {['Media', 'Utility', 'Wellness', 'Custom'].map((cat) => (
    <TouchableOpacity
      key={cat}
      onPress={() => setSelectedCategory(cat)}
      style={[styles.catBtn, selectedCategory === cat && styles.catBtnActive]}
    >
      <Text>{cat}</Text>
    </TouchableOpacity>
  ))}
</View>
```

Add state:

```ts
const [selectedCategory, setSelectedCategory] = useState('Media');
```

Update rule creation:

```ts
const rule: AutomationRule = {
  id: Date.now().toString(),
  triggerTime: selectedTime,
  action: selectedAction,
  category: selectedCategory,
  enabled: true,
};
```

---

### 🎯 Step 3: Add Filter in `AutomationTimelineScreen.tsx`

```tsx
const [selectedFilter, setSelectedFilter] = useState('All');

const filteredRules = selectedFilter === 'All'
  ? rules
  : rules.filter(r => r.category === selectedFilter);
```

Add UI buttons above list:

```tsx
<View style={styles.row}>
  {['All', 'Media', 'Utility', 'Wellness', 'Custom'].map((cat) => (
    <TouchableOpacity
      key={cat}
      onPress={() => setSelectedFilter(cat)}
      style={[styles.catBtn, selectedFilter === cat && styles.catBtnActive]}
    >
      <Text style={{ color: selectedFilter === cat ? '#000' : '#fff' }}>{cat}</Text>
    </TouchableOpacity>
  ))}
</View>
```

---

## ✅ Part 2: Export / Import Automation Rules

### 📁 Update `AutomationStorage.ts`

#### ✅ Add Export:

```ts
export async function exportRules(): Promise<string> {
  const rules = await getRules();
  return JSON.stringify(rules, null, 2);
}
```

#### ✅ Add Import:

```ts
export async function importRules(json: string): Promise<boolean> {
  try {
    const data = JSON.parse(json);
    if (!Array.isArray(data)) return false;
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    return true;
  } catch (e) {
    return false;
  }
}
```

---

### 📱 Create `AutomationImportExportScreen.tsx`

```tsx
import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import { exportRules, importRules } from '../modules/AutomationStorage';

export default function AutomationImportExportScreen() {
  const [jsonData, setJsonData] = useState('');

  const handleExport = async () => {
    const json = await exportRules();
    setJsonData(json);
  };

  const handleImport = async () => {
    const success = await importRules(jsonData);
    Alert.alert(success ? 'Success' : 'Failed', success ? 'Rules imported!' : 'Invalid JSON.');
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.heading}>📤 Export / 📥 Import Rules</Text>

      <TouchableOpacity onPress={handleExport} style={styles.button}>
        <Text style={styles.btnText}>Export Rules</Text>
      </TouchableOpacity>

      <TextInput
        multiline
        style={styles.textBox}
        placeholder="Paste JSON here to import"
        value={jsonData}
        onChangeText={setJsonData}
      />

      <TouchableOpacity onPress={handleImport} style={[styles.button, { backgroundColor: '#4caf50' }]}>
        <Text style={styles.btnText}>Import Rules</Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { padding: 16, backgroundColor: '#121212', flex: 1 },
  heading: { color: '#00FFB2', fontSize: 20, marginBottom: 16, fontWeight: 'bold' },
  textBox: { backgroundColor: '#fff', color: '#000', height: 200, padding: 10, marginBottom: 16 },
  button: {
    backgroundColor: '#00FFB2', padding: 12, borderRadius: 6, marginBottom: 16,
  },
  btnText: { textAlign: 'center', fontWeight: 'bold', color: '#000' },
});
```

---

## 🧠 What You’ve Achieved Now

| Feature                              | Status                     |
| ------------------------------------ | -------------------------- |
| 🎯 Categorized automation rules      | ✅ Media, Utility, Wellness |
| 🧠 Filter UI in timeline             | ✅                          |
| 🗃 Export/Import automation rules    | ✅ JSON copy-paste          |
| 🌍 Offline & shareable JARVIS config | ✅                          |

---
Perfect — let’s empower JARVIS Lite to **trigger rule management by voice**, completely offline:

---

## ✅ Add Voice Commands:

* 🗂 “**Show my rules**” → Opens `AutomationTimelineScreen`
* 📥 “**Import my config**” → Opens `AutomationImportExportScreen`

This uses your existing **IntentParser.ts**, and expands the voice assistant to control screens.

---

### 🔧 Step 1: Update `IntentParser.ts`

Extend your intent detection logic:

```ts
export function parseIntent(text: string): { intent: string; parameters?: any } {
  const lower = text.toLowerCase();

  if (lower.includes('remind me to')) {
    return { intent: 'SET_REMINDER', parameters: { reminder: text.replace(/remind me to/i, '').trim() } };
  }

  if (lower.includes('show my rules')) {
    return { intent: 'SHOW_RULES' };
  }

  if (lower.includes('import my config')) {
    return { intent: 'IMPORT_CONFIG' };
  }

  return { intent: 'UNKNOWN' };
}
```

---

### 🔧 Step 2: Update `MainAssistantScreen.tsx` – Add Navigation Handling

If you're using React Navigation:

```ts
import { useNavigation } from '@react-navigation/native';

...

const navigation = useNavigation();

...

switch (parsed.intent) {
  case 'SHOW_RULES':
    action = 'Opening automation timeline.';
    navigation.navigate('AutomationTimeline');
    break;

  case 'IMPORT_CONFIG':
    action = 'Opening import screen.';
    navigation.navigate('AutomationImportExport');
    break;
}
```

---

### 🧠 Result

Now, you can speak:

> "Show my rules"
> → JARVIS says ✅ “Opening automation timeline.”
> → Navigates to `AutomationTimelineScreen.tsx`

or

> "Import my config"
> → ✅ Navigates to `AutomationImportExportScreen.tsx`

All without internet.

---

### 🔄 Optional: Add Confirmation via TTS

Inside each `case`, after `action =`, add:

```ts
await OfflineTTS.speak(action);
```

This makes JARVIS reply audibly before navigating.

---

## 🧠 Next Steps

Let’s now add voice command like:

> **"Delete rule at 8:00 AM"** → 🗑️ Deletes scheduled automation rule at that time

Fully offline, no API, and integrated into your existing modules.

---

## ✅ Step 1: Update `IntentParser.ts`

We’ll use regex to extract the time from natural speech:

```ts
export function parseIntent(text: string): { intent: string; parameters?: any } {
  const lower = text.toLowerCase();

  if (lower.includes('remind me to')) {
    return { intent: 'SET_REMINDER', parameters: { reminder: text.replace(/remind me to/i, '').trim() } };
  }

  if (lower.includes('show my rules')) {
    return { intent: 'SHOW_RULES' };
  }

  if (lower.includes('import my config')) {
    return { intent: 'IMPORT_CONFIG' };
  }

  const deleteMatch = lower.match(/delete rule at (\d{1,2}:\d{2})/);
  if (deleteMatch) {
    return { intent: 'DELETE_RULE_AT_TIME', parameters: { time: deleteMatch[1] } };
  }

  return { intent: 'UNKNOWN' };
}
```

---

## ✅ Step 2: Add Logic in `MainAssistantScreen.tsx`

Extend your intent handler with:

```ts
case 'DELETE_RULE_AT_TIME': {
  const timeToDelete = parsed.parameters.time;
  const rules = await getRules();
  const target = rules.find(r => r.triggerTime === timeToDelete);

  if (target) {
    await deleteRule(target.id);
    await OfflineTTS.speak(`Deleted rule at ${timeToDelete}`);
    console.log(`Deleted rule at ${timeToDelete}`);
  } else {
    await OfflineTTS.speak(`No rule found at ${timeToDelete}`);
    console.log(`No rule found at ${timeToDelete}`);
  }
  break;
}
```

---

## ✅ Example Usage:

Say aloud:

> **"Delete rule at 7:30"**
> → Assistant will:

* 🔎 Search for a rule with triggerTime `07:30`
* 🗑️ Delete it
* 🔊 Say: “Deleted rule at 7:30” or “No rule found”

Works offline using only local memory + voice intent logic.

---

## 🧠 Bonus: Make It Robust

Normalize 12-hour to 24-hour:

```ts
const convertTo24Hour = (timeStr: string) => {
  let [hour, min] = timeStr.split(':').map(Number);
  if (hour < 10) hour = Number('0' + hour);
  return `${hour.toString().padStart(2, '0')}:${min.toString().padStart(2, '0')}`;
};

const timeToDelete = convertTo24Hour(parsed.parameters.time);
```

---




Perfect — let’s now add the ability to **query JARVIS for scheduled automations at a specific time**, using offline voice commands like:

> “What runs at 7 AM?”
> “List rules at 18:30”
> “Do I have anything set for 9?”

---

## ✅ Step 1: Update `IntentParser.ts` — Add Query Matcher

We’ll detect flexible time queries using regex:

```ts
const timeQueryRegex = /(?:what\s+runs\s+at|list\s+rules\s+at|do\s+i\s+have\s+.*for)\s+(\d{1,2})(?::(\d{2}))?\s*(am|pm)?/i;

export function parseIntent(text: string): { intent: string; parameters?: any } {
  const lower = text.toLowerCase();

  if (lower.includes('remind me to')) {
    return { intent: 'SET_REMINDER', parameters: { reminder: text.replace(/remind me to/i, '').trim() } };
  }

  if (lower.includes('show my rules')) return { intent: 'SHOW_RULES' };
  if (lower.includes('import my config')) return { intent: 'IMPORT_CONFIG' };

  const deleteMatch = lower.match(/delete rule at (\d{1,2}:\d{2})/);
  if (deleteMatch) {
    return { intent: 'DELETE_RULE_AT_TIME', parameters: { time: deleteMatch[1] } };
  }

  const queryMatch = text.match(timeQueryRegex);
  if (queryMatch) {
    const hour = parseInt(queryMatch[1]);
    const minutes = queryMatch[2] ? parseInt(queryMatch[2]) : 0;
    const meridiem = queryMatch[3];

    let formattedHour = hour;
    if (meridiem === 'pm' && hour < 12) formattedHour += 12;
    if (meridiem === 'am' && hour === 12) formattedHour = 0;

    const formatted = `${String(formattedHour).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
    return { intent: 'QUERY_RULES_AT_TIME', parameters: { time: formatted } };
  }

  return { intent: 'UNKNOWN' };
}
```

---

## ✅ Step 2: Handle It in `MainAssistantScreen.tsx`

Add:

```ts
case 'QUERY_RULES_AT_TIME': {
  const time = parsed.parameters.time;
  const rules = await getRules();
  const matches = rules.filter(r => r.triggerTime === time);

  if (matches.length > 0) {
    const list = matches.map(r => {
      const actionLabel = AVAILABLE_ACTIONS.find(a => a.id === r.action)?.label || r.action;
      return actionLabel;
    }).join(', ');
    await OfflineTTS.speak(`You have ${matches.length} rule${matches.length > 1 ? 's' : ''} at ${time}: ${list}`);
  } else {
    await OfflineTTS.speak(`You have no rules at ${time}`);
  }
  break;
}
```

---

## 🧪 Examples It Understands:

| Spoken Command                  | Interprets As |
| ------------------------------- | ------------- |
| "What runs at 7 AM"             | `07:00`       |
| "Do I have anything for 18:30?" | `18:30`       |
| "List rules at 9 pm"            | `21:00`       |

All processed **offline** via your `IntentParser`, `LocalMemory`, and `OfflineTTS`.

---

✅ You now have:

| Voice Feature                | Status             |
| ---------------------------- | ------------------ |
| Query rules at specific time | ✅ Fully functional |
| Deletes rule by time         | ✅                  |
| Lists all rules              | ✅                  |
| Adds reminder by voice       | ✅                  |

---


