/**
 * App.tsx
 * Main application component for JARVIS Lite
 * Handles navigation, initialization, and global state
 */

import React, { useEffect, useState } from 'react';
import {
  StatusBar,
  StyleSheet,
  useColorScheme,
  Alert,
  BackHandler,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';

// Import types
import { RootStackParamList } from '@/types';

// Import services
import { interactionLogger } from '@/services/InteractionLogger';
import { offlineTTS } from '@/services/OfflineTTS';
import { systemControlAndroid } from '@/services/SystemControlAndroid';

// Import screens
import FirstTimeSetupScreen from '@/screens/FirstTimeSetupScreen';
import StartupGreetingScreen from '@/screens/StartupGreetingScreen';
import MainAssistantScreen from '@/screens/MainAssistantScreen';
import PlaceholderScreens from '@/screens/PlaceholderScreens';

// Import theme
import { ThemeProvider, useTheme } from '@/utils/ThemeContext';
import { AppConfigProvider, useAppConfig } from '@/utils/AppConfigContext';

const Stack = createStackNavigator<RootStackParamList>();

interface AppState {
  isLoading: boolean;
  isFirstLaunch: boolean;
  hasPermissions: boolean;
  initializationError: string | null;
}

const AppNavigator: React.FC = () => {
  const { theme } = useTheme();
  const { config, isLoading: configLoading } = useAppConfig();
  const [appState, setAppState] = useState<AppState>({
    isLoading: true,
    isFirstLaunch: true,
    hasPermissions: false,
    initializationError: null,
  });

  useEffect(() => {
    initializeApp();
  }, []);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    return () => backHandler.remove();
  }, []);

  /**
   * Initialize the application
   */
  const initializeApp = async (): Promise<void> => {
    try {
      console.log('JARVIS Lite: Starting initialization...');

      // Check if this is first launch
      const isFirstLaunch = config?.isFirstLaunch ?? true;

      // Check permissions
      const permissions = await systemControlAndroid.checkPermissions();
      const hasRequiredPermissions = permissions.microphone; // Minimum required

      // Initialize core services
      await Promise.all([
        // Database and logging are initialized in their constructors
        offlineTTS.testVoice(), // Test TTS
      ]);

      setAppState({
        isLoading: false,
        isFirstLaunch,
        hasPermissions: hasRequiredPermissions,
        initializationError: null,
      });

      console.log('JARVIS Lite: Initialization completed successfully');
    } catch (error) {
      console.error('JARVIS Lite: Initialization failed:', error);
      setAppState({
        isLoading: false,
        isFirstLaunch: true,
        hasPermissions: false,
        initializationError: error.message,
      });
    }
  };

  /**
   * Handle Android back button
   */
  const handleBackPress = (): boolean => {
    // Custom back button handling
    Alert.alert(
      'Exit JARVIS Lite',
      'Are you sure you want to exit?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Exit', onPress: () => BackHandler.exitApp() },
      ],
      { cancelable: true }
    );
    return true;
  };

  /**
   * Determine initial route based on app state
   */
  const getInitialRouteName = (): keyof RootStackParamList => {
    if (appState.initializationError) {
      return 'SystemStatus';
    }

    if (appState.isFirstLaunch || !appState.hasPermissions) {
      return 'FirstTimeSetup';
    }

    return 'StartupGreeting';
  };

  if (appState.isLoading || configLoading) {
    // We'll create a loading screen component later
    return <StartupGreetingScreen />;
  }

  return (
    <NavigationContainer
      theme={{
        dark: theme.isDark,
        colors: {
          primary: theme.colors.primary,
          background: theme.colors.background,
          card: theme.colors.surface,
          text: theme.colors.text,
          border: theme.colors.border,
          notification: theme.colors.accent,
        },
      }}
    >
      <Stack.Navigator
        initialRouteName={getInitialRouteName()}
        screenOptions={{
          headerStyle: {
            backgroundColor: theme.colors.surface,
          },
          headerTintColor: theme.colors.text,
          headerTitleStyle: {
            fontWeight: 'bold',
          },
          gestureEnabled: true,
          cardStyleInterpolator: ({ current, layouts }) => {
            return {
              cardStyle: {
                transform: [
                  {
                    translateX: current.progress.interpolate({
                      inputRange: [0, 1],
                      outputRange: [layouts.screen.width, 0],
                    }),
                  },
                ],
              },
            };
          },
        }}
      >
        <Stack.Screen
          name="FirstTimeSetup"
          component={FirstTimeSetupScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="StartupGreeting"
          component={StartupGreetingScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="MainAssistant"
          component={MainAssistantScreen}
          options={{
            title: 'JARVIS Lite',
            headerShown: false
          }}
        />
        <Stack.Screen
          name="ActivityTimeline"
          component={PlaceholderScreens.ActivityTimelineScreen}
          options={{ title: 'Activity Timeline' }}
        />
        <Stack.Screen
          name="Settings"
          component={PlaceholderScreens.SettingsScreen}
          options={{ title: 'Settings' }}
        />
        <Stack.Screen
          name="AssistantPersonality"
          component={PlaceholderScreens.AssistantPersonalityScreen}
          options={{ title: 'Personality' }}
        />
        <Stack.Screen
          name="ReminderSchedule"
          component={PlaceholderScreens.ReminderScheduleScreen}
          options={{ title: 'Reminders' }}
        />
        <Stack.Screen
          name="AutomationBuilder"
          component={PlaceholderScreens.AutomationBuilderScreen}
          options={{ title: 'Automations' }}
        />
        <Stack.Screen
          name="LearningInsights"
          component={PlaceholderScreens.LearningInsightsScreen}
          options={{ title: 'Learning Insights' }}
        />
        <Stack.Screen
          name="DebugAndLogs"
          component={PlaceholderScreens.DebugAndLogsScreen}
          options={{ title: 'Debug & Logs' }}
        />
        <Stack.Screen
          name="HelpAndTutorial"
          component={PlaceholderScreens.HelpAndTutorialScreen}
          options={{ title: 'Help & Tutorial' }}
        />
        <Stack.Screen
          name="SystemStatus"
          component={PlaceholderScreens.SystemStatusScreen}
          options={{ title: 'System Status' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const App: React.FC = () => {
  const isDarkMode = useColorScheme() === 'dark';

  return (
    <SafeAreaProvider>
      <AppConfigProvider>
        <ThemeProvider initialTheme={isDarkMode ? 'dark' : 'light'}>
          <StatusBar
            barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            backgroundColor="transparent"
            translucent
          />
          <AppNavigator />
        </ThemeProvider>
      </AppConfigProvider>
    </SafeAreaProvider>
  );
};

export default App;
