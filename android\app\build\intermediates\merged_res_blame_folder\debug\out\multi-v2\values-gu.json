{"logs": [{"outputFile": "com.jarvislite.app-mergeDebugResources-19:/values-gu/values-gu.xml", "map": [{"source": "F:\\gradle-cache-new\\caches\\transforms-3\\1b52c21370a38b025fe38599105dc471\\transformed\\appcompat-1.4.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}}, {"source": "F:\\gradle-cache-new\\caches\\transforms-3\\a8962bb5790e2a717af128f173a274ed\\transformed\\core-1.7.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2869", "endColumns": "100", "endOffsets": "2965"}}]}]}