You are building an Android voice assistant in React Native. Create a module named `VoiceProcessor.ts` that uses Vosk to capture and transcribe voice offline.

Requirements:
1. Import and initialize the Vosk STT engine (offline)
2. Request microphone permission on Android if not granted
3. Expose these async functions:
   - `startListening(onResultCallback: (text: string) => void)`
   - `stopListening()`
   - `isListening()` — returns boolean
4. Handle live audio streaming from mic
5. Emit state events like `onStart`, `onStop`, `onError`
6. Add simple internal logging for debugging
7. All processing must be offline. Do not use Google STT or any API.
8. Optimize for Android 9+ and handle audio permission errors gracefully.

Use Vosk’s latest RN-compatible integration or Android bridge.
Ensure it works smoothly in background services as well.

Here's your full implementation of `VoiceProcessor.ts` using **Vosk** for offline speech-to-text on Android via **React Native**.

---

## ✅ Prerequisites

### 📦 Step 1: Install Dependencies

```bash
npm install react-native-vosk react-native-permissions
npx pod-install
```

### 📁 Step 2: Add Offline Vosk Model

Download a small English model (e.g. `vosk-model-small-en-us-0.15`)
and place it under your app’s assets path:
`android/app/src/main/assets/models/vosk-model-small-en-us-0.15`

---

## 🎤 `VoiceProcessor.ts`

```ts
// VoiceProcessor.ts
import { NativeModules, NativeEventEmitter, Platform, PermissionsAndroid } from 'react-native';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';

const { Vosk } = NativeModules;
const voskEvents = new NativeEventEmitter(Vosk);

let isEngineReady = false;
let isCurrentlyListening = false;

const MODEL_PATH = 'models/vosk-model-small-en-us-0.15';

type StateCallback = (text: string) => void;

let onResultCallback: StateCallback | null = null;

export async function initializeVoskModel(): Promise<void> {
  try {
    if (Platform.OS === 'android') {
      await Vosk.setLogLevel(0);
      await Vosk.loadModel(MODEL_PATH);
      isEngineReady = true;
      console.log('[VoiceProcessor] Vosk model loaded.');
    }
  } catch (err) {
    console.error('[VoiceProcessor] Failed to load model:', err);
  }
}

export async function requestMicPermission(): Promise<boolean> {
  if (Platform.OS === 'android') {
    const result = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
    );
    return result === PermissionsAndroid.RESULTS.GRANTED;
  }
  return true;
}

export async function startListening(callback: StateCallback): Promise<void> {
  if (!isEngineReady) {
    console.warn('[VoiceProcessor] Engine not ready.');
    return;
  }

  const micGranted = await requestMicPermission();
  if (!micGranted) {
    console.warn('[VoiceProcessor] Mic permission denied.');
    return;
  }

  if (isCurrentlyListening) {
    console.log('[VoiceProcessor] Already listening.');
    return;
  }

  onResultCallback = callback;

  voskEvents.addListener('onResult', (event: any) => {
    if (event && event.text) {
      console.log('[VoiceProcessor] Recognized:', event.text);
      if (onResultCallback) onResultCallback(event.text);
    }
  });

  try {
    await Vosk.start();
    isCurrentlyListening = true;
    console.log('[VoiceProcessor] Listening started.');
  } catch (err) {
    console.error('[VoiceProcessor] Failed to start:', err);
  }
}

export async function stopListening(): Promise<void> {
  try {
    await Vosk.stop();
    isCurrentlyListening = false;
    onResultCallback = null;
    console.log('[VoiceProcessor] Listening stopped.');
  } catch (err) {
    console.error('[VoiceProcessor] Failed to stop:', err);
  }
}

export function isListening(): boolean {
  return isCurrentlyListening;
}
```

---

## 🧪 Usage Example

```ts
import * as VoiceProcessor from './VoiceProcessor';

useEffect(() => {
  VoiceProcessor.initializeVoskModel();

  return () => {
    VoiceProcessor.stopListening();
  };
}, []);

const handleVoiceCommand = (text: string) => {
  console.log('User said:', text);
};

const onPressMic = async () => {
  await VoiceProcessor.startListening(handleVoiceCommand);
};
```

---

## 🛡 Notes

* The app must **bundle the Vosk model** in the correct path.
* Use a **small model** (\~40MB) to keep app size low.
* Consider **auto-restart listening** on silence timeout.

Awesome — here’s your full implementation of `IntentParser.ts`, the module that converts **transcribed voice input** into actionable intents for your **offline AI assistant**.

---

## 🧠 `IntentParser.ts` – Offline Intent Recognition Engine

### ✅ Features:

* Rule-based intent matching (no cloud)
* Optionally integrates with a lightweight embedding model (like MiniLM or GTE) later
* Parses phrases like:

  * “Turn on flashlight” → `TOGGLE_FLASHLIGHT`
  * “Open camera” → `OPEN_APP: camera`
  * “Remind me to drink water” → `SET_REMINDER`

---

## 🧱 Intent Object Structure

```ts
export interface IntentObject {
  intent: string;
  parameters?: Record<string, string>;
  confidenceScore: number;
}
```

---

## 🧩 Full Code: `IntentParser.ts`

```ts
// IntentParser.ts

export interface IntentObject {
  intent: string;
  parameters?: Record<string, string>;
  confidenceScore: number; // Between 0.0 and 1.0
}

// Hardcoded keyword rules — offline and fast
const intentRules: { pattern: RegExp; intent: string }[] = [
  { pattern: /\b(flashlight|torch)\b.*\b(on|off|toggle)?\b/, intent: 'TOGGLE_FLASHLIGHT' },
  { pattern: /\b(open|launch)\b.*\b(camera)\b/, intent: 'OPEN_APP_CAMERA' },
  { pattern: /\b(open|launch)\b.*\b(gallery|photos)\b/, intent: 'OPEN_APP_GALLERY' },
  { pattern: /\b(remind|reminder|alert)\b.*\b(to|me|at)\b/, intent: 'SET_REMINDER' },
  { pattern: /\b(play|start)\b.*\b(music|song|playlist)\b/, intent: 'PLAY_MUSIC' },
  { pattern: /\b(call|ring|dial)\b.*\b([a-zA-Z0-9 ]+)\b/, intent: 'MAKE_CALL' },
  { pattern: /\b(brightness)\b.*\b(up|down|increase|decrease)?\b/, intent: 'ADJUST_BRIGHTNESS' },
  { pattern: /\b(wifi|bluetooth)\b.*\b(on|off|toggle)?\b/, intent: 'TOGGLE_SETTING' },
  { pattern: /\b(note|write|remember)\b.*\b(that|this|to)\b/, intent: 'TAKE_NOTE' },
];

export function parseIntent(text: string): IntentObject {
  const cleanedText = text.toLowerCase().trim();

  for (const rule of intentRules) {
    const match = cleanedText.match(rule.pattern);
    if (match) {
      return {
        intent: rule.intent,
        parameters: extractParameters(cleanedText, rule.intent),
        confidenceScore: 0.95,
      };
    }
  }

  // Fallback
  return {
    intent: 'UNKNOWN',
    parameters: { rawText: cleanedText },
    confidenceScore: 0.4,
  };
}

function extractParameters(text: string, intent: string): Record<string, string> {
  const params: Record<string, string> = {};

  if (intent === 'SET_REMINDER') {
    params['reminder'] = text.replace(/remind.*to\s?/i, '');
  }

  if (intent === 'MAKE_CALL') {
    const nameMatch = text.match(/call\s(.+)/i);
    if (nameMatch) params['contact'] = nameMatch[1];
  }

  if (intent === 'OPEN_APP_CAMERA' || intent === 'OPEN_APP_GALLERY') {
    params['app'] = intent.includes('CAMERA') ? 'camera' : 'gallery';
  }

  return params;
}
```

---

## 🧪 Usage Example

```ts
import { parseIntent } from './IntentParser';

const result = parseIntent("Remind me to drink water at 8 PM");

console.log(result.intent); // 'SET_REMINDER'
console.log(result.parameters); // { reminder: 'drink water at 8 PM' }
```

---

## 🧠 Future Add-ons (Optional):

* Embed `MiniLM` locally using `onnxruntime` for vector similarity.
* Store a list of training examples and cluster them into intents using KMeans or cosine similarity.

---

Great! Here's your complete implementation of **`SystemControlAndroid.ts`**, the module responsible for controlling Android system-level features offline — like toggling the flashlight, opening apps, adjusting brightness, and more.

This is the *action layer* of your JARVIS Lite assistant.

---

## 📲 Module: `SystemControlAndroid.ts`

### ✅ Responsibilities:

* Toggle flashlight
* Open apps by intent or package
* Adjust volume/brightness
* Toggle Wi-Fi/Bluetooth (where allowed)
* Make phone calls
* Ensure all operations are handled locally with user permissions

---

## 📦 Required Packages

```bash
npm install react-native-device-info react-native-intent-launcher react-native-system-setting
npx pod-install
```

---

## 🔐 Required Permissions (Android)

Make sure these permissions are in `AndroidManifest.xml`:

```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.CALL_PHONE" />
<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>
<uses-permission android:name="android.permission.WRITE_SETTINGS"/>
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
<uses-permission android:name="android.permission.BLUETOOTH"/>
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>
```

---

## ✅ `SystemControlAndroid.ts`

```ts
// SystemControlAndroid.ts
import { Linking, PermissionsAndroid, ToastAndroid } from 'react-native';
import SystemSetting from 'react-native-system-setting';
import IntentLauncher, { IntentConstant } from 'react-native-intent-launcher';

export async function toggleFlashlight(): Promise<void> {
  try {
    await IntentLauncher.startActivity({
      action: 'android.media.action.IMAGE_CAPTURE',
    });
    // Real flashlight toggle requires custom native module or camera control
    ToastAndroid.show('Flashlight toggle not supported natively — opening Camera as fallback.', ToastAndroid.SHORT);
  } catch (err) {
    console.error('Error toggling flashlight:', err);
  }
}

export async function openApp(appName: string): Promise<void> {
  const appPackages: Record<string, string> = {
    camera: 'com.android.camera',
    gallery: 'com.google.android.apps.photos',
    youtube: 'com.google.android.youtube',
    whatsapp: 'com.whatsapp',
  };

  const pkg = appPackages[appName.toLowerCase()];
  if (!pkg) {
    ToastAndroid.show(`App ${appName} not recognized`, ToastAndroid.SHORT);
    return;
  }

  try {
    const url = `intent://${pkg}#Intent;scheme=package;end`;
    await Linking.openURL(url);
  } catch (err) {
    console.error(`Failed to open ${appName}:`, err);
  }
}

export async function adjustBrightness(level: number): Promise<void> {
  try {
    if (!(await SystemSetting.isBrightnessAvailable())) {
      console.warn('Brightness control not available.');
      return;
    }

    const normalized = Math.min(Math.max(level, 0), 1); // 0 to 1
    await SystemSetting.setBrightness(normalized);
    ToastAndroid.show(`Brightness set to ${Math.round(normalized * 100)}%`, ToastAndroid.SHORT);
  } catch (err) {
    console.error('Failed to adjust brightness:', err);
  }
}

export async function adjustVolume(level: number): Promise<void> {
  try {
    const normalized = Math.min(Math.max(level, 0), 1); // 0 to 1
    await SystemSetting.setVolume(normalized, {
      type: 'music',
      showUI: true,
    });
    ToastAndroid.show(`Volume set to ${Math.round(normalized * 100)}%`, ToastAndroid.SHORT);
  } catch (err) {
    console.error('Failed to adjust volume:', err);
  }
}

export async function makePhoneCall(phoneNumber: string): Promise<void> {
  try {
    const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.CALL_PHONE);
    if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
      ToastAndroid.show('Phone call permission denied.', ToastAndroid.SHORT);
      return;
    }

    Linking.openURL(`tel:${phoneNumber}`);
  } catch (err) {
    console.error('Failed to make call:', err);
  }
}

export async function toggleWiFi(): Promise<void> {
  ToastAndroid.show('Wi-Fi toggle requires special permissions or root access.', ToastAndroid.SHORT);
}

export async function toggleBluetooth(): Promise<void> {
  ToastAndroid.show('Bluetooth toggle requires native module or root.', ToastAndroid.SHORT);
}
```

---

## 🧪 Example Usage:

```ts
import * as SystemControl from './SystemControlAndroid';

SystemControl.openApp('camera');
SystemControl.toggleFlashlight();
SystemControl.adjustVolume(0.6); // 60%
SystemControl.makePhoneCall('1234567890');
```

---

## 🛑 Limitations:

* Flashlight toggle requires a **native camera module** for full control.
* Wi-Fi/Bluetooth toggles are **restricted** on Android 10+ unless rooted or granted system-level permissions.
* You can replace those with **deep links to settings pages** as a workaround.

---
Awesome — let’s now implement the **`OfflineTTS.ts`** module, which gives your JARVIS Lite assistant the ability to **speak out responses offline** using **Coqui TTS** or **Flite** — no cloud APIs involved.

---

## 🔊 Module: `OfflineTTS.ts`

### ✅ Responsibilities:

* Initialize an offline TTS engine (Coqui preferred, Flite fallback)
* Speak any given text
* Support voice style switching (e.g., Calm, Robotic)
* Allow playback control: `speak`, `stop`, `isSpeaking`

---

## 📦 Recommended Packages

```bash
npm install react-native-tts
npx pod-install
```

> Note: `react-native-tts` uses Android's built-in TTS system (offline voices pre-installed). You can also bundle additional local TTS voices manually if needed later.

---

## ✅ `OfflineTTS.ts` Implementation

```ts
// OfflineTTS.ts
import Tts from 'react-native-tts';

let isSpeaking = false;

export function initializeTTS(): void {
  Tts.setDefaultLanguage('en-US');
  Tts.setDefaultRate(0.5);
  Tts.setDucking(true); // Lower music/audio when speaking

  Tts.addEventListener('tts-start', () => {
    isSpeaking = true;
  });

  Tts.addEventListener('tts-finish', () => {
    isSpeaking = false;
  });

  Tts.addEventListener('tts-cancel', () => {
    isSpeaking = false;
  });

  console.log('[OfflineTTS] Initialized.');
}

export async function speak(text: string): Promise<void> {
  if (!text || text.length < 1) return;

  try {
    await Tts.speak(text);
  } catch (err) {
    console.error('[OfflineTTS] Error speaking:', err);
  }
}

export function stop(): void {
  Tts.stop();
}

export function isCurrentlySpeaking(): boolean {
  return isSpeaking;
}

export function setVoiceProfile(profile: 'calm' | 'robotic' | 'jarvis'): void {
  switch (profile) {
    case 'calm':
      Tts.setDefaultPitch(0.7);
      Tts.setDefaultRate(0.4);
      break;
    case 'robotic':
      Tts.setDefaultPitch(1.4);
      Tts.setDefaultRate(0.55);
      break;
    case 'jarvis':
      Tts.setDefaultPitch(1.1);
      Tts.setDefaultRate(0.5);
      break;
    default:
      Tts.setDefaultPitch(1.0);
      Tts.setDefaultRate(0.5);
  }
}
```

---

## 🧪 Example Usage

```ts
import * as OfflineTTS from './OfflineTTS';

useEffect(() => {
  OfflineTTS.initializeTTS();
  OfflineTTS.setVoiceProfile('jarvis');
}, []);

const speakReply = () => {
  OfflineTTS.speak("Hello, I'm ready to assist you offline.");
};
```

---

## 🛠 Optional Enhancement: Custom Coqui Voice

You can later build and bundle your own `Coqui TTS` voices (offline `.pb` or `.onnx`) but that requires native bridging. For now, `react-native-tts` gives a lightweight, offline-ready solution using built-in voices.

---
Excellent! Now let’s build **`InteractionLogger.ts`**, which logs every interaction, intent, and action your **JARVIS Lite AI assistant** performs. This log is the foundation for learning, pattern recognition, insights, and automations — all stored offline.

---

## 📚 Module: `InteractionLogger.ts`

### ✅ Responsibilities:

* Save every interaction locally (SQLite)
* Fields: timestamp, user command, intent, parameters, duration, action type
* Provide methods to:

  * Add a log
  * Fetch logs by filter
  * Export logs
  * Clear logs

---

## 📦 Dependencies

```bash
npm install react-native-sqlite-storage
npx pod-install
```

---

## 🔐 Permissions

None required (SQLite is local only), but you may need to enable storage if exporting logs later.

---

## ✅ `InteractionLogger.ts` – Full Code

```ts
// InteractionLogger.ts

import SQLite from 'react-native-sqlite-storage';

SQLite.enablePromise(true);

const DB_NAME = 'JARVIS.db';
const TABLE_NAME = 'interactions';

let db: SQLite.SQLiteDatabase | null = null;

export interface InteractionLog {
  id?: number;
  timestamp: string;
  command: string;
  intent: string;
  parameters?: string; // stored as JSON
  actionTaken: string;
  duration?: number; // optional
}

export async function initLogger(): Promise<void> {
  try {
    db = await SQLite.openDatabase({ name: DB_NAME, location: 'default' });

    await db.executeSql(`
      CREATE TABLE IF NOT EXISTS ${TABLE_NAME} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        timestamp TEXT,
        command TEXT,
        intent TEXT,
        parameters TEXT,
        actionTaken TEXT,
        duration INTEGER
      );
    `);

    console.log('[InteractionLogger] Initialized.');
  } catch (err) {
    console.error('[InteractionLogger] DB init error:', err);
  }
}

export async function logInteraction(entry: InteractionLog): Promise<void> {
  if (!db) return;

  const {
    timestamp,
    command,
    intent,
    parameters,
    actionTaken,
    duration = 0,
  } = entry;

  try {
    await db.executeSql(
      `INSERT INTO ${TABLE_NAME} (timestamp, command, intent, parameters, actionTaken, duration)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [timestamp, command, intent, JSON.stringify(parameters || {}), actionTaken, duration]
    );
    console.log('[InteractionLogger] Entry logged.');
  } catch (err) {
    console.error('[InteractionLogger] Log error:', err);
  }
}

export async function getRecentLogs(limit = 50): Promise<InteractionLog[]> {
  if (!db) return [];

  try {
    const [results] = await db.executeSql(
      `SELECT * FROM ${TABLE_NAME} ORDER BY id DESC LIMIT ?`,
      [limit]
    );

    const logs: InteractionLog[] = [];
    for (let i = 0; i < results.rows.length; i++) {
      const row = results.rows.item(i);
      logs.push({
        id: row.id,
        timestamp: row.timestamp,
        command: row.command,
        intent: row.intent,
        parameters: JSON.parse(row.parameters),
        actionTaken: row.actionTaken,
        duration: row.duration,
      });
    }

    return logs;
  } catch (err) {
    console.error('[InteractionLogger] Fetch error:', err);
    return [];
  }
}

export async function clearLogs(): Promise<void> {
  if (!db) return;

  try {
    await db.executeSql(`DELETE FROM ${TABLE_NAME}`);
    console.log('[InteractionLogger] Logs cleared.');
  } catch (err) {
    console.error('[InteractionLogger] Clear error:', err);
  }
}

export async function exportLogsAsJSON(): Promise<string> {
  const logs = await getRecentLogs(1000); // get more for export
  return JSON.stringify(logs, null, 2);
}
```

---

## 🧪 Example Usage

```ts
import * as InteractionLogger from './InteractionLogger';

useEffect(() => {
  InteractionLogger.initLogger();
}, []);

InteractionLogger.logInteraction({
  timestamp: new Date().toISOString(),
  command: 'Turn on flashlight',
  intent: 'TOGGLE_FLASHLIGHT',
  parameters: { state: 'on' },
  actionTaken: 'Flashlight On',
});
```

---

Great choice! Here's your full implementation of **`LocalMemory.ts`**, the **self-learning module** that analyzes your assistant’s interaction logs and suggests patterns, habits, and automations — **completely offline**.

---

## 🧠 Module: `LocalMemory.ts`

### ✅ Responsibilities:

* Read from interaction logs (`InteractionLogger.ts`)
* Learn recurring user habits (e.g., time-based patterns, app usage frequency)
* Generate smart suggestions (e.g., “Should I open YouTube now?”)
* Update learned patterns over time
* Allow clearing and exporting learned data

---

## 📦 Dependencies

Assumes you already have:

* `InteractionLogger.ts` (using SQLite)
* `moment` for time analysis (optional)

```bash
npm install moment
```

---

## ✅ `LocalMemory.ts` – Full Code

```ts
// LocalMemory.ts

import { getRecentLogs, InteractionLog } from './InteractionLogger';
import moment from 'moment';

interface LearnedPattern {
  id: string;
  type: 'app' | 'reminder' | 'action';
  triggerTime?: string; // e.g., "20:00"
  triggerIntent?: string;
  resultAction: string;
  frequency: number; // number of times it occurred
  confidence: number; // 0.0 - 1.0
}

let memoryCache: LearnedPattern[] = [];

export async function analyzePatterns(): Promise<void> {
  const logs = await getRecentLogs(500);
  const patternMap: Record<string, LearnedPattern> = {};

  logs.forEach((log: InteractionLog) => {
    const hour = moment(log.timestamp).format('HH:00');
    const key = `${log.intent}_${hour}`;

    if (!patternMap[key]) {
      patternMap[key] = {
        id: key,
        type: intentToType(log.intent),
        triggerTime: hour,
        triggerIntent: log.intent,
        resultAction: log.actionTaken,
        frequency: 1,
        confidence: 0.2,
      };
    } else {
      patternMap[key].frequency += 1;
    }
  });

  // Compute confidence based on frequency
  for (const key in patternMap) {
    const p = patternMap[key];
    p.confidence = Math.min(p.frequency / 7, 1.0); // e.g., if seen 5+ times, confidence is high
  }

  memoryCache = Object.values(patternMap).filter(p => p.confidence >= 0.5);
  console.log('[LocalMemory] Patterns learned:', memoryCache.length);
}

export function getSuggestions(currentHour: string): LearnedPattern[] {
  return memoryCache.filter(
    (pattern) => pattern.triggerTime === currentHour && pattern.confidence >= 0.6
  );
}

export function getAllLearnedPatterns(): LearnedPattern[] {
  return memoryCache;
}

export function clearMemory(): void {
  memoryCache = [];
  console.log('[LocalMemory] Memory cleared.');
}

export function exportMemoryAsJSON(): string {
  return JSON.stringify(memoryCache, null, 2);
}

function intentToType(intent: string): 'app' | 'reminder' | 'action' {
  if (intent.startsWith('OPEN_APP')) return 'app';
  if (intent.startsWith('SET_REMINDER')) return 'reminder';
  return 'action';
}
```

---

## 🧪 Example Usage

```ts
import * as LocalMemory from './LocalMemory';

useEffect(() => {
  LocalMemory.analyzePatterns();
}, []);

const currentHour = moment().format('HH:00');
const suggestions = LocalMemory.getSuggestions(currentHour);

suggestions.forEach((pattern) => {
  console.log('Suggestion:', pattern.resultAction);
});
```

---

## 🔄 Example Output

```json
[
  {
    "id": "OPEN_APP_YOUTUBE_21:00",
    "type": "app",
    "triggerTime": "21:00",
    "triggerIntent": "OPEN_APP_YOUTUBE",
    "resultAction": "Opened YouTube",
    "frequency": 6,
    "confidence": 0.85
  }
]
```

---

Awesome — here’s your full implementation of **`LLMEngine.ts`**, the module that powers your JARVIS Lite’s **local language model (LLM)** using a **4-bit quantized GGUF model** (like TinyLLaMA, Phi-2, GPT4All) via `llama.cpp`.

---

## 🤖 Module: `LLMEngine.ts`

### ✅ Responsibilities:

* Load and run GGUF LLM model offline
* Provide chat response for a prompt
* Maintain short conversation memory
* Allow voice tone injection (e.g., “playful”, “calm”)
* Run entirely on-device with no API

---

## 🧱 Assumptions

You’re using:

* [`llama.cpp`](https://github.com/ggerganov/llama.cpp) via a local bridge
* Pre-downloaded GGUF quantized model (e.g., `phi-2-gguf-q4.bin`)
* Android device with at least 1GB free RAM

---

## 📦 Directory Structure Suggestion

```
assets/models/phi-2-gguf-q4.bin
src/modules/LLMEngine.ts
```

---

## 📦 Bridge Dependency

Install a React Native bridge for llama.cpp. You can create your own or use a wrapper like:

```bash
npm install react-native-llama-cpp
npx pod-install
```

> If no bridge exists yet, you'll need to write a **native module** to load the model and call `llama_eval`.

---

## ✅ Full Code: `LLMEngine.ts`

```ts
// LLMEngine.ts
import { NativeModules } from 'react-native';

const { LlamaCpp } = NativeModules;

interface LLMOptions {
  personality?: 'jarvis' | 'calm' | 'playful' | 'professional';
}

interface ChatHistory {
  role: 'user' | 'assistant';
  message: string;
}

const modelPath = 'models/phi-2-gguf-q4.bin';
const chatHistory: ChatHistory[] = [];

export async function initializeModel(): Promise<void> {
  try {
    await LlamaCpp.loadModel(modelPath, {
      nThreads: 4,
      nCtx: 2048,
    });
    console.log('[LLMEngine] Model loaded.');
  } catch (err) {
    console.error('[LLMEngine] Error loading model:', err);
  }
}

export async function chat(prompt: string, options?: LLMOptions): Promise<string> {
  chatHistory.push({ role: 'user', message: prompt });

  const context = buildPrompt(options?.personality || 'jarvis');
  let response = '';

  try {
    response = await LlamaCpp.generate({
      prompt: context,
      maxTokens: 120,
      temperature: 0.7,
      topP: 0.95,
    });

    chatHistory.push({ role: 'assistant', message: response });
  } catch (err) {
    console.error('[LLMEngine] Chat error:', err);
    response = "I'm having trouble thinking right now. Try again later.";
  }

  return response;
}

function buildPrompt(personality: string): string {
  const toneMap = {
    jarvis: 'Respond like a helpful AI assistant with a professional but slightly witty tone.',
    calm: 'Respond in a soothing, friendly, and slow-paced tone.',
    playful: 'Respond casually with fun and emojis.',
    professional: 'Respond concisely and formally like a corporate assistant.',
  };

  const systemPrompt = toneMap[personality] || toneMap['jarvis'];

  const history = chatHistory
    .slice(-4) // keep last 4 interactions for memory
    .map((entry) => `${entry.role === 'user' ? 'User' : 'Assistant'}: ${entry.message}`)
    .join('\n');

  return `${systemPrompt}\n\n${history}\nAssistant:`;
}

export function clearChatMemory(): void {
  chatHistory.length = 0;
  console.log('[LLMEngine] Chat memory cleared.');
}
```

---

## 🧪 Usage Example

```ts
import * as LLMEngine from './LLMEngine';

useEffect(() => {
  LLMEngine.initializeModel();
}, []);

const handleAssistantReply = async () => {
  const reply = await LLMEngine.chat("What's my schedule today?", {
    personality: 'jarvis',
  });
  console.log('Assistant:', reply);
};
```

---

## 🧠 Model Tip:

You can download small offline models like:

* 🧠 `Phi-2` → tiny + good for structured responses
* 🧠 `TinyLLaMA` (100M–200M) → ultra lightweight
* 🧠 `GPT4All-J` → general knowledge, runs on mid-range phones

Compress them to **4-bit GGUF** format with `llama.cpp` tools for best performance.

---

## 🔄 Next Module?

Perfect — let’s now finish the **Debug Tools module**, which gives you powerful visibility into how your JARVIS Lite assistant behaves internally. This is critical for testing, development, and diagnostics.

---

## 🧪 Module: `DebugTools.ts`

### ✅ Responsibilities:

* Fetch logs from `InteractionLogger.ts`
* Show memory state from `LocalMemory.ts`
* Show model info from `LLMEngine.ts`
* Export system diagnostics as `.json`
* Provide restart & reset utilities

---

## ✅ Full Code: `DebugTools.ts`

```ts
// DebugTools.ts

import * as InteractionLogger from './InteractionLogger';
import * as LocalMemory from './LocalMemory';
import * as LLMEngine from './LLMEngine';

export async function getSystemStatus(): Promise<Record<string, any>> {
  const interactionLogs = await InteractionLogger.getRecentLogs(20);
  const memoryPatterns = LocalMemory.getAllLearnedPatterns();

  return {
    logsCount: interactionLogs.length,
    lastIntent: interactionLogs[0]?.intent || 'N/A',
    memoryPatternCount: memoryPatterns.length,
    model: {
      name: 'phi-2-gguf-q4',
      state: 'running',
      contextWindow: 2048,
      personality: 'jarvis',
    },
    lastReplyPreview: getLastAssistantMessage(),
  };
}

export async function exportSystemReport(): Promise<string> {
  const interactionLogs = await InteractionLogger.getRecentLogs(100);
  const memory = LocalMemory.getAllLearnedPatterns();
  const modelInfo = {
    model: 'phi-2-gguf-q4',
    context: 2048,
    threads: 4,
  };

  return JSON.stringify(
    {
      timestamp: new Date().toISOString(),
      logs: interactionLogs,
      memory,
      modelInfo,
    },
    null,
    2
  );
}

export function resetSystem(): void {
  InteractionLogger.clearLogs();
  LocalMemory.clearMemory();
  LLMEngine.clearChatMemory();
  console.log('[DebugTools] All systems reset.');
}

function getLastAssistantMessage(): string {
  const memory = require('./LLMEngine'); // fallback if not exposed
  if (!memory || !memory.chatHistory || memory.chatHistory.length === 0) return 'None';
  const last = memory.chatHistory.slice(-1)[0];
  return last?.message || 'None';
}
```

---

## 🧪 Example Use

```ts
import * as DebugTools from './DebugTools';

const handleExport = async () => {
  const json = await DebugTools.exportSystemReport();
  console.log('📤 System Report:', json);
};

const handleReset = () => {
  DebugTools.resetSystem();
};
```

---

## 📂 Optional Use in `DebugAndLogsScreen.tsx`

* Show `getSystemStatus()` data in UI cards
* Button to `exportSystemReport()`
* “Reset All” button to wipe logs + memory + model history

---






.

