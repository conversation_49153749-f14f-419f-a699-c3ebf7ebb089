# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 201ms
    create-module-model completed in 212ms
    [gap of 23ms]
    create-X86-model 10ms
    create-X86_64-model 12ms
    create-module-model
      [gap of 11ms]
      create-cmake-model 132ms
    create-module-model completed in 144ms
    [gap of 14ms]
    create-ARM64_V8A-model 10ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 448ms
create_cxx_tasks completed in 451ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 85ms
    create-module-model completed in 94ms
    create-module-model
      create-cmake-model 65ms
    create-module-model completed in 71ms
    [gap of 29ms]
  create-initial-cxx-model completed in 229ms
create_cxx_tasks completed in 232ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 58ms
    create-module-model completed in 63ms
    create-module-model
      create-cmake-model 59ms
    create-module-model completed in 65ms
    [gap of 21ms]
  create-initial-cxx-model completed in 170ms
create_cxx_tasks completed in 172ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 62ms
    create-module-model completed in 70ms
    create-module-model
      create-cmake-model 49ms
    create-module-model completed in 53ms
    [gap of 25ms]
  create-initial-cxx-model completed in 167ms
create_cxx_tasks completed in 169ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 80ms
    create-module-model completed in 89ms
    create-module-model
      create-cmake-model 79ms
    create-module-model completed in 85ms
    [gap of 29ms]
  create-initial-cxx-model completed in 235ms
create_cxx_tasks completed in 237ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 86ms
    create-module-model completed in 93ms
    create-module-model
      create-cmake-model 94ms
    create-module-model completed in 102ms
    [gap of 25ms]
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 267ms
create_cxx_tasks completed in 269ms

