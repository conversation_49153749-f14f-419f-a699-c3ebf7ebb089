/**
 * QuoteLibrary.ts
 * Collection of motivational quotes for wellness automation
 */

export const quotes = [
  "Success is not final. Failure is not fatal. It's the courage to continue that counts.",
  "Small steps every day lead to big results over time.",
  "You are stronger than you think and more capable than you imagine.",
  "Discipline beats motivation every single time.",
  "The journey of a thousand miles begins with a single step.",
  "Progress, not perfection, is the goal.",
  "Every expert was once a beginner. Every pro was once an amateur.",
  "The only impossible journey is the one you never begin.",
  "Your limitation is only your imagination.",
  "Great things never come from comfort zones.",
  "Dream it. Wish it. Do it.",
  "Success doesn't just find you. You have to go out and get it.",
  "The harder you work for something, the greater you'll feel when you achieve it.",
  "Don't stop when you're tired. Stop when you're done.",
  "Wake up with determination. Go to bed with satisfaction.",
  "Do something today that your future self will thank you for.",
  "Little things make big days.",
  "It's going to be hard, but hard does not mean impossible.",
  "Don't wait for opportunity. Create it.",
  "Sometimes we're tested not to show our weaknesses, but to discover our strengths.",
  "The key to success is to focus on goals, not obstacles.",
  "Dream bigger. Do bigger.",
  "Don't be afraid to give up the good to go for the great.",
  "The difference between ordinary and extraordinary is that little extra.",
  "Success is what happens after you have survived all your failures.",
  "If you want to achieve greatness, stop asking for permission.",
  "The way to get started is to quit talking and begin doing.",
  "Innovation distinguishes between a leader and a follower.",
  "Life is what happens to you while you're busy making other plans.",
  "The future belongs to those who believe in the beauty of their dreams.",
  "It is during our darkest moments that we must focus to see the light.",
  "Whoever is happy will make others happy too.",
  "Do not go where the path may lead, go instead where there is no path and leave a trail.",
  "You have been assigned this mountain to show others it can be moved.",
  "What lies behind us and what lies before us are tiny matters compared to what lies within us.",
  "Believe you can and you're halfway there.",
  "The only person you are destined to become is the person you decide to be.",
  "Go confidently in the direction of your dreams. Live the life you have imagined.",
  "Twenty years from now you will be more disappointed by the things you didn't do than by the ones you did do.",
  "Life is 10% what happens to you and 90% how you react to it.",
  "The most common way people give up their power is by thinking they don't have any.",
  "If you look at what you have in life, you'll always have more.",
  "If you want to lift yourself up, lift up someone else.",
  "The greatest glory in living lies not in never falling, but in rising every time we fall.",
  "You can't use up creativity. The more you use, the more you have.",
  "If you want to be happy, be.",
  "Life is really simple, but we insist on making it complicated.",
  "May you live all the days of your life.",
  "Life itself is the most wonderful fairy tale.",
  "Do not let making a living prevent you from making a life.",
];

export const morningQuotes = [
  "Good morning! Today is a new beginning, a fresh start, and a chance to make it amazing.",
  "Rise and shine! Every morning is a gift, that's why it's called the present.",
  "Today is full of possibilities. Make the most of every moment.",
  "Wake up with determination, go to bed with satisfaction.",
  "The morning sun reminds us that we too can rise again from the darkness.",
  "Each morning we are born again. What we do today matters most.",
  "Morning is an important time of day because how you spend your morning can often tell you what kind of day you are going to have.",
  "Every morning starts a new page in your story. Make it a great one today.",
];

export const eveningQuotes = [
  "As the day ends, remember that you did your best, and that's enough.",
  "Tonight, rest well knowing you've made progress, no matter how small.",
  "Every sunset brings the promise of a new dawn.",
  "Reflect on today's victories, learn from today's challenges, and prepare for tomorrow's opportunities.",
  "The day is over, the night has come. Tomorrow is another chance to shine.",
  "End your day with gratitude. There's always something to be thankful for.",
  "Sleep peacefully knowing you've given your all today.",
  "As you close your eyes tonight, remember that tomorrow is a fresh start.",
];

export const workQuotes = [
  "Success is the sum of small efforts repeated day in and day out.",
  "The expert in anything was once a beginner.",
  "Focus on being productive instead of busy.",
  "Excellence is not a skill, it's an attitude.",
  "The way to get started is to quit talking and begin doing.",
  "Don't watch the clock; do what it does. Keep going.",
  "Opportunities don't happen. You create them.",
  "Success is walking from failure to failure with no loss of enthusiasm.",
];

export const wellnessQuotes = [
  "Take care of your body. It's the only place you have to live.",
  "Health is not about the weight you lose, but about the life you gain.",
  "A healthy outside starts from the inside.",
  "Your body can stand almost anything. It's your mind you have to convince.",
  "Wellness is not a destination, it's a journey.",
  "Self-care is not selfish. You cannot serve from an empty vessel.",
  "The groundwork for all happiness is good health.",
  "To keep the body in good health is a duty, otherwise we shall not be able to keep our mind strong and clear.",
];

export function getRandomQuote(): string {
  return quotes[Math.floor(Math.random() * quotes.length)];
}

export function getMorningQuote(): string {
  return morningQuotes[Math.floor(Math.random() * morningQuotes.length)];
}

export function getEveningQuote(): string {
  return eveningQuotes[Math.floor(Math.random() * eveningQuotes.length)];
}

export function getWorkQuote(): string {
  return workQuotes[Math.floor(Math.random() * workQuotes.length)];
}

export function getWellnessQuote(): string {
  return wellnessQuotes[Math.floor(Math.random() * wellnessQuotes.length)];
}

export function getQuoteByCategory(category: 'morning' | 'evening' | 'work' | 'wellness' | 'random'): string {
  switch (category) {
    case 'morning':
      return getMorningQuote();
    case 'evening':
      return getEveningQuote();
    case 'work':
      return getWorkQuote();
    case 'wellness':
      return getWellnessQuote();
    case 'random':
    default:
      return getRandomQuote();
  }
}

export function getQuoteOfTheDay(): string {
  // Use date as seed for consistent daily quote
  const today = new Date();
  const dayOfYear = Math.floor((today.getTime() - new Date(today.getFullYear(), 0, 0).getTime()) / 86400000);
  const index = dayOfYear % quotes.length;
  return quotes[index];
}

export function searchQuotes(keyword: string): string[] {
  const searchTerm = keyword.toLowerCase();
  return quotes.filter(quote => 
    quote.toLowerCase().includes(searchTerm)
  );
}

export function getQuoteStats(): {
  totalQuotes: number;
  morningQuotes: number;
  eveningQuotes: number;
  workQuotes: number;
  wellnessQuotes: number;
} {
  return {
    totalQuotes: quotes.length,
    morningQuotes: morningQuotes.length,
    eveningQuotes: eveningQuotes.length,
    workQuotes: workQuotes.length,
    wellnessQuotes: wellnessQuotes.length,
  };
}
