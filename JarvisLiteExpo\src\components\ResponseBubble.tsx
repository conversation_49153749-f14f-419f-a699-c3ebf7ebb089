/**
 * ResponseBubble.tsx
 * Displays assistant responses with typing animation
 */

import React, { useRef, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';

import { useTheme } from '@/utils/ThemeContext';

interface ResponseBubbleProps {
  text: string;
  isTyping: boolean;
  isProcessing: boolean;
}

const { width: screenWidth } = Dimensions.get('window');

const ResponseBubble: React.FC<ResponseBubbleProps> = ({
  text,
  isTyping,
  isProcessing,
}) => {
  const { theme } = useTheme();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const [displayedText, setDisplayedText] = useState('');
  const [typingIndex, setTypingIndex] = useState(0);

  useEffect(() => {
    if (text) {
      // Animate bubble appearance
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();

      if (isTyping) {
        startTypingAnimation();
      } else {
        setDisplayedText(text);
        setTypingIndex(text.length);
      }
    } else {
      // Hide bubble
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.9,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [text, isTyping]);

  const startTypingAnimation = () => {
    setDisplayedText('');
    setTypingIndex(0);
    
    const typeText = () => {
      let index = 0;
      const interval = setInterval(() => {
        if (index < text.length) {
          setDisplayedText(text.substring(0, index + 1));
          setTypingIndex(index + 1);
          index++;
        } else {
          clearInterval(interval);
        }
      }, 30); // Typing speed
    };

    // Small delay before starting to type
    setTimeout(typeText, 200);
  };

  const renderTypingIndicator = () => {
    if (!isProcessing && !isTyping) return null;

    return (
      <View style={localStyles.typingContainer}>
        <TypingDots />
      </View>
    );
  };

  if (!text && !isProcessing && !isTyping) {
    return null;
  }

  return (
    <Animated.View
      style={[
        localStyles.container,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <View
        style={[
          localStyles.bubble,
          {
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
            shadowColor: theme.colors.text,
          },
        ]}
      >
        {isProcessing || isTyping ? (
          renderTypingIndicator()
        ) : (
          <Text
            style={[
              localStyles.text,
              { color: theme.colors.text },
            ]}
          >
            {displayedText}
          </Text>
        )}
      </View>
      
      {/* Speech bubble tail */}
      <View
        style={[
          localStyles.tail,
          {
            borderTopColor: theme.colors.surface,
          },
        ]}
      />
    </Animated.View>
  );
};

const TypingDots: React.FC = () => {
  const dot1Anim = useRef(new Animated.Value(0)).current;
  const dot2Anim = useRef(new Animated.Value(0)).current;
  const dot3Anim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animateDots = () => {
      const createDotAnimation = (anim: Animated.Value, delay: number) => {
        return Animated.loop(
          Animated.sequence([
            Animated.delay(delay),
            Animated.timing(anim, {
              toValue: 1,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.timing(anim, {
              toValue: 0,
              duration: 400,
              useNativeDriver: true,
            }),
          ])
        );
      };

      Animated.parallel([
        createDotAnimation(dot1Anim, 0),
        createDotAnimation(dot2Anim, 200),
        createDotAnimation(dot3Anim, 400),
      ]).start();
    };

    animateDots();

    return () => {
      dot1Anim.stopAnimation();
      dot2Anim.stopAnimation();
      dot3Anim.stopAnimation();
    };
  }, []);

  return (
    <View style={localStyles.dotsContainer}>
      <Animated.View
        style={[
          localStyles.dot,
          {
            opacity: dot1Anim,
            transform: [
              {
                translateY: dot1Anim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -5],
                }),
              },
            ],
          },
        ]}
      />
      <Animated.View
        style={[
          localStyles.dot,
          {
            opacity: dot2Anim,
            transform: [
              {
                translateY: dot2Anim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -5],
                }),
              },
            ],
          },
        ]}
      />
      <Animated.View
        style={[
          localStyles.dot,
          {
            opacity: dot3Anim,
            transform: [
              {
                translateY: dot3Anim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -5],
                }),
              },
            ],
          },
        ]}
      />
    </View>
  );
};

const localStyles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginVertical: 20,
    paddingHorizontal: 20,
  },
  bubble: {
    maxWidth: screenWidth * 0.8,
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 20,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  text: {
    fontSize: 16,
    lineHeight: 22,
    textAlign: 'center',
  },
  tail: {
    width: 0,
    height: 0,
    borderLeftWidth: 10,
    borderRightWidth: 10,
    borderTopWidth: 10,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    marginTop: -1,
  },
  typingContainer: {
    minHeight: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#999',
    marginHorizontal: 3,
  },
});

export default ResponseBubble;
