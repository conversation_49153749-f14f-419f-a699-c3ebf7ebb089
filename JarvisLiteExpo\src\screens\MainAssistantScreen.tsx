/**
 * MainAssistantScreen.tsx
 * Main interface for JARVIS Lite AI assistant
 * Integrated voice processing pipeline with all core systems
 */

import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator, FlatList } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import * as VoiceProcessor from '../services/VoiceProcessor';
import * as IntentParser from '../services/IntentParser';
import * as SystemControl from '../services/SystemControlAndroid';
import * as InteractionLogger from '../services/InteractionLogger';
import * as OfflineTTS from '../services/OfflineTTS';
import * as LLMEngine from '../services/LLMEngine';
import * as LocalMemory from '../services/LocalMemory';
import { saveReminder } from '../services/ReminderManager';
import { getRules } from '../modules/AutomationStorage';
import { useNavigation } from '@react-navigation/native';
import moment from 'moment';

export default function MainAssistantScreen() {
  const navigation = useNavigation();
  const [transcript, setTranscript] = useState('');
  const [reply, setReply] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [logList, setLogList] = useState([]);
  const [suggestions, setSuggestions] = useState([]);

  useEffect(() => {
    VoiceProcessor.initializeVoskModel();
    InteractionLogger.initLogger();
    OfflineTTS.initializeTTS();
    LLMEngine.initializeLLM();
    LocalMemory.analyzePatterns();
    loadLogs();
    loadSuggestions();
  }, []);

  const loadLogs = async () => {
    const logs = await InteractionLogger.getRecentLogs(10);
    setLogList(logs);
  };

  const loadSuggestions = () => {
    const currentHour = moment().format('HH:00');
    const suggested = LocalMemory.getSuggestions(currentHour);
    setSuggestions(suggested);
  };

  const handleVoiceInput = async () => {
    if (isListening) {
      await VoiceProcessor.stopListening();
      setIsListening(false);
      return;
    }

    setTranscript('');
    setIsListening(true);

    await VoiceProcessor.startListening(async (text: string) => {
      setIsListening(false);
      setIsProcessing(true);
      setTranscript(text);

      // 1. Parse intent
      const parsed = IntentParser.parseIntent(text);

      // 2. Execute action (only handle a few intents here)
      let action = 'No action';
      switch (parsed.intent) {
        case 'TOGGLE_FLASHLIGHT':
          await SystemControl.toggleFlashlight();
          action = 'Flashlight toggled';
          break;
        case 'OPEN_APP_CAMERA':
          await SystemControl.openApp('camera');
          action = 'Opened Camera';
          break;
        case 'OPEN_APP_GALLERY':
          await SystemControl.openApp('gallery');
          action = 'Opened Gallery';
          break;
        case 'ADJUST_VOLUME':
          await SystemControl.adjustVolume(0.7);
          action = 'Volume adjusted';
          break;
        case 'ADJUST_BRIGHTNESS':
          await SystemControl.adjustBrightness(0.7);
          action = 'Brightness adjusted';
          break;
        case 'MAKE_CALL':
          const contact = parsed.parameters?.contact;
          if (contact) {
            await SystemControl.makePhoneCall(contact);
            action = `Called ${contact}`;
          } else {
            action = 'No contact specified';
          }
          break;
        case 'SET_REMINDER':
          const now = new Date();
          now.setMinutes(now.getMinutes() + 1); // Default to 1 minute later

          await saveReminder({
            id: Date.now().toString(),
            title: parsed.parameters?.reminder || 'Unnamed Reminder',
            timestamp: now.toISOString(),
            repeat: 'none',
            ttsEnabled: true,
          });

          action = `Reminder set: ${parsed.parameters?.reminder}`;
          break;
        case 'GET_TIME':
          const time = new Date().toLocaleTimeString();
          action = `Current time: ${time}`;
          break;
        case 'GET_DATE':
          const date = new Date().toLocaleDateString();
          action = `Today's date: ${date}`;
          break;
        case 'COUNT_TODAYS_TASKS': {
          const rules = await getRules();
          const now = new Date();
          const currentHour = now.getHours();
          const currentMin = now.getMinutes();

          const todayRules = rules.filter((r) => {
            const [h, m] = r.triggerTime.split(':').map(Number);
            return r.enabled && (h > currentHour || (h === currentHour && m >= currentMin));
          });

          if (todayRules.length === 0) {
            action = "You have no remaining tasks today.";
          } else if (todayRules.length === 1) {
            action = "You have one task remaining today.";
          } else {
            action = `You have ${todayRules.length} tasks remaining today.`;
          }
          break;
        }
        case 'OPEN_SETTINGS':
          action = 'Opening settings';
          navigation.navigate('Settings' as never);
          break;
        default:
          action = 'No matching action';
      }

      // 3. Log interaction
      await InteractionLogger.logInteraction({
        timestamp: new Date().toISOString(),
        command: text,
        intent: parsed.intent,
        parameters: parsed.parameters,
        actionTaken: action,
      });

      loadLogs();

      // 4. Get reply from LLM
      const assistantReply = await LLMEngine.generateResponse(`The user said: "${text}". You responded with: "${action}"`, {
        personality: 'jarvis',
      });
      setReply(assistantReply.text);

      // 5. Speak reply
      OfflineTTS.speak(assistantReply.text);

      setIsProcessing(false);
    });
  };

  const handleSuggestionPress = (suggestion: any) => {
    // Simulate voice input for the suggestion
    const text = suggestion.resultAction;
    setTranscript(text);

    // Process the suggestion directly instead of calling handleVoiceInput
    console.log('[MainAssistant] Processing suggestion:', text);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🤖 JARVIS Lite</Text>

      {/* Voice Input Button */}
      <TouchableOpacity
        style={[styles.micButton, isListening && styles.micButtonActive]}
        onPress={handleVoiceInput}
        disabled={isProcessing}
      >
        {isProcessing ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Icon name={isListening ? 'microphone' : 'microphone-outline'} size={30} color="#fff" />
        )}
      </TouchableOpacity>

      {/* Transcript */}
      {transcript ? (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>You said:</Text>
          <Text style={styles.transcript}>"{transcript}"</Text>
        </View>
      ) : null}

      {/* Reply */}
      {reply ? (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>JARVIS:</Text>
          <Text style={styles.reply}>{reply}</Text>
        </View>
      ) : null}

      {/* Smart Suggestions */}
      {suggestions.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Smart Suggestions:</Text>
          <FlatList
            data={suggestions}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <TouchableOpacity style={styles.suggestionItem} onPress={() => handleSuggestionPress(item)}>
                <Text style={styles.suggestionText}>{item.resultAction}</Text>
                <Text style={styles.suggestionMeta}>
                  {item.frequency}x at {item.triggerTime}
                </Text>
              </TouchableOpacity>
            )}
          />
        </View>
      )}

      {/* Recent Logs */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Recent Activity:</Text>
        <FlatList
          data={logList}
          keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
          renderItem={({ item }) => (
            <View style={styles.logItem}>
              <Text style={styles.logCommand}>"{item.command}"</Text>
              <Text style={styles.logAction}>{item.actionTaken}</Text>
              <Text style={styles.logTime}>{new Date(item.timestamp).toLocaleTimeString()}</Text>
            </View>
          )}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  micButton: {
    backgroundColor: '#007AFF',
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  micButtonActive: {
    backgroundColor: '#FF3B30',
  },
  section: {
    marginBottom: 20,
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  transcript: {
    fontSize: 14,
    fontStyle: 'italic',
    color: '#666',
  },
  reply: {
    fontSize: 14,
    color: '#333',
  },
  suggestionItem: {
    padding: 10,
    backgroundColor: '#e8f4fd',
    borderRadius: 8,
    marginBottom: 8,
  },
  suggestionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#007AFF',
  },
  suggestionMeta: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  logItem: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  logCommand: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  logAction: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  logTime: {
    fontSize: 10,
    color: '#999',
    marginTop: 2,
  },
});
