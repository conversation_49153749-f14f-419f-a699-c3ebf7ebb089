@echo off
echo ========================================
echo JARVIS Lite - Expo Go Solution
echo ========================================
echo.

echo 🚨 DISK SPACE ISSUE DETECTED
echo Your C: drive doesn't have enough space for NDK installation.
echo Using Expo Go instead - no additional disk space needed!
echo.

echo Step 1: Navigate to Expo project
cd /d "F:\jarvis\JarvisLiteExpo"
echo Current directory: %CD%

echo.
echo Step 2: Check project setup
if exist "package.json" (
    echo ✅ Expo project found
) else (
    echo ❌ Expo project not found
    goto :error
)

echo.
echo Step 3: Install missing dependencies (if any)
npm install --no-optional

echo.
echo Step 4: Start Expo development server
echo.
echo 📱 INSTRUCTIONS FOR YOUR PHONE:
echo 1. Install "Expo Go" app from Google Play Store
echo 2. Make sure your phone and computer are on the same WiFi
echo 3. When QR code appears, scan it with Expo Go
echo 4. JARVIS Lite will load on your phone!
echo.
echo Starting Expo server...

npx expo start --clear

goto :end

:error
echo ❌ Error: Could not find Expo project
echo Please make sure you're in the correct directory
pause
exit /b 1

:end
echo.
echo ========================================
echo If Expo doesn't start, try these alternatives:
echo 1. npm start
echo 2. expo start --tunnel (for network issues)
echo ========================================
pause
