# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 78ms
    create-module-model completed in 87ms
    create-module-model
      create-cmake-model 62ms
    create-module-model completed in 71ms
    [gap of 24ms]
  create-initial-cxx-model completed in 216ms
create_cxx_tasks completed in 219ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 75ms
    create-module-model completed in 82ms
    create-ARMEABI_V7A-model 24ms
    create-X86-model 10ms
    create-X86_64-model 10ms
    create-module-model
      create-cmake-model 115ms
    create-module-model completed in 125ms
    [gap of 26ms]
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 301ms
create_cxx_tasks completed in 304ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 89ms
    create-module-model completed in 96ms
    [gap of 28ms]
    create-X86_64-model 17ms
    create-module-model
      create-cmake-model 493ms
    create-module-model completed in 501ms
    [gap of 26ms]
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 683ms
create_cxx_tasks completed in 686ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 78ms
    create-module-model completed in 84ms
    create-module-model
      create-cmake-model 66ms
    create-module-model completed in 72ms
    [gap of 35ms]
  create-initial-cxx-model completed in 223ms
create_cxx_tasks completed in 226ms

