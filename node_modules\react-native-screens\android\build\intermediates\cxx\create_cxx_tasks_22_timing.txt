# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 14ms]
      create-cmake-model 92ms
    create-module-model completed in 110ms
    create-ARMEABI_V7A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 11ms
    create-module-model
      create-cmake-model 82ms
    create-module-model completed in 91ms
    [gap of 37ms]
  create-initial-cxx-model completed in 288ms
  [gap of 21ms]
create_cxx_tasks completed in 309ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 21ms]
      create-cmake-model 79ms
    create-module-model completed in 102ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-module-model
      create-cmake-model 78ms
    create-module-model completed in 88ms
    [gap of 16ms]
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
  create-initial-cxx-model completed in 286ms
  [gap of 28ms]
create_cxx_tasks completed in 315ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 14ms]
      create-cmake-model 71ms
    create-module-model completed in 88ms
    [gap of 24ms]
    create-X86-model 12ms
    create-X86_64-model 10ms
    create-module-model
      [gap of 16ms]
      create-cmake-model 61ms
    create-module-model completed in 79ms
    [gap of 24ms]
  create-initial-cxx-model completed in 238ms
  [gap of 13ms]
create_cxx_tasks completed in 251ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 72ms
    create-module-model completed in 79ms
    create-module-model
      create-cmake-model 75ms
    create-module-model completed in 83ms
    [gap of 31ms]
  create-initial-cxx-model completed in 225ms
create_cxx_tasks completed in 228ms

