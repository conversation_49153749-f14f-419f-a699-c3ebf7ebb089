Let’s complete JARVIS Lite’s awareness by adding:

> 🧠 **"How many tasks today?"**
> → Responds with the number of automations set for the current day.

---

## ✅ Step 1: Update `IntentParser.ts`

Add this detection:

```ts
if (
  lower.includes('how many tasks') ||
  lower.includes('how many rules') ||
  lower.includes('tasks today') ||
  lower.includes('scheduled today')
) {
  return { intent: 'COUNT_TODAYS_TASKS' };
}
```

---

## ✅ Step 2: Handle `COUNT_TODAYS_TASKS` in `MainAssistantScreen.tsx`

Add this in your intent switch:

```ts
case 'COUNT_TODAYS_TASKS': {
  const rules = await getRules();
  const now = new Date();
  const currentHour = now.getHours();
  const currentMin = now.getMinutes();

  const todayRules = rules.filter((r) => {
    const [h, m] = r.triggerTime.split(':').map(Number);
    return r.enabled && (h > currentHour || (h === currentHour && m >= currentMin));
  });

  if (todayRules.length === 0) {
    await OfflineTTS.speak("You have no remaining tasks today.");
  } else if (todayRules.length === 1) {
    await OfflineTTS.speak("You have one task remaining today.");
  } else {
    await OfflineTTS.speak(`You have ${todayRules.length} tasks remaining today.`);
  }

  break;
}
```

---

## 🧪 Sample Voice Inputs Supported:

| User Says                              | AI Responds                          |
| -------------------------------------- | ------------------------------------ |
| "How many tasks today?"                | “You have 3 tasks remaining today.”  |
| "Do I have any rules scheduled today?" | “You have no remaining tasks today.” |
| "How many rules?"                      | “You have one task remaining today.” |

All voice commands now work **offline**, using your local memory and custom automation rules.

---

## 🔥 JARVIS Lite Voice Intent Coverage (100% Offline)

| Intent        | Trigger Phrase Example  |
| ------------- | ----------------------- |
| Add reminder  | "Remind me to..."       |
| Delete rule   | "Delete rule at 7:00"   |
| Show rules    | "Show my rules"         |
| Import config | "Import my config"      |
| Query time    | "List rules at 8 AM"    |
| Next task     | "What's my next task?"  |
| Count today   | "How many tasks today?" |

---

Awesome — let’s give JARVIS Lite a **polished, pro-level feel** with:

1. ✅ **Custom splash screen** (with animated logo & loading line)
2. ✅ **First-time onboarding tips screen** (voice tips, usage guide, "Get Started" button)

---

## ✅ 1. Splash Screen Setup (with animation)

### 📁 Create `SplashScreen.tsx`

```tsx
import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { useNavigation } from '@react-navigation/native';

export default function SplashScreen() {
  const navigation = useNavigation();
  const fade = new Animated.Value(0);

  useEffect(() => {
    Animated.timing(fade, {
      toValue: 1,
      duration: 1500,
      useNativeDriver: true,
    }).start(() => {
      setTimeout(() => {
        navigation.replace('Onboarding');
      }, 1500);
    });
  }, []);

  return (
    <View style={styles.container}>
      <Animated.Text style={[styles.title, { opacity: fade }]}>
        JΛRVIS Lite
      </Animated.Text>
      <Animated.View style={[styles.loader, { opacity: fade }]} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#000' },
  title: { fontSize: 36, color: '#00FFB2', fontWeight: 'bold', letterSpacing: 2 },
  loader: {
    marginTop: 20,
    height: 4,
    width: 100,
    backgroundColor: '#00FFB2',
    borderRadius: 2,
  },
});
```

---

## ✅ 2. Onboarding Tips Screen

### 📁 Create `OnboardingScreen.tsx`

```tsx
import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';

export default function OnboardingScreen() {
  const navigation = useNavigation();

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.heading}>🧠 Welcome to JARVIS Lite</Text>

      <Text style={styles.tip}>🎙 Say: "Remind me to drink water"</Text>
      <Text style={styles.tip}>🔍 Say: "What runs at 9?" or "Show my rules"</Text>
      <Text style={styles.tip}>🗑 Say: "Delete rule at 8:30"</Text>
      <Text style={styles.tip}>🕘 Say: "When is my next task?"</Text>
      <Text style={styles.tip}>📊 Track automations in real time</Text>

      <TouchableOpacity
        style={styles.button}
        onPress={() => navigation.replace('MainAssistant')}
      >
        <Text style={styles.buttonText}>🚀 Get Started</Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flexGrow: 1, justifyContent: 'center', alignItems: 'center', padding: 24, backgroundColor: '#111' },
  heading: { fontSize: 24, fontWeight: 'bold', color: '#00FFB2', marginBottom: 20, textAlign: 'center' },
  tip: { fontSize: 16, color: '#fff', marginBottom: 12, textAlign: 'center' },
  button: { marginTop: 30, padding: 14, backgroundColor: '#00FFB2', borderRadius: 8 },
  buttonText: { fontWeight: 'bold', color: '#000', fontSize: 16 },
});
```

---

## ✅ 3. Navigation Stack Example

In `App.tsx` or `Navigator.tsx`, define screens like:

```tsx
<Stack.Navigator initialRouteName="Splash">
  <Stack.Screen name="Splash" component={SplashScreen} options={{ headerShown: false }} />
  <Stack.Screen name="Onboarding" component={OnboardingScreen} options={{ headerShown: false }} />
  <Stack.Screen name="MainAssistant" component={MainAssistantScreen} options={{ headerShown: false }} />
  ...
</Stack.Navigator>
```

---

## 🧠 Bonus: First-Time Detection (Optional)

In `SplashScreen.tsx`, check `AsyncStorage`:

```ts
const hasLaunched = await AsyncStorage.getItem('launchedBefore');
if (hasLaunched) {
  navigation.replace('MainAssistant');
} else {
  await AsyncStorage.setItem('launchedBefore', 'true');
  navigation.replace('Onboarding');
}
```

---

✅ You now have:

* 🎬 Branded splash intro
* 🎓 Friendly onboarding guide
* 🧠 Voice tips and offline assistant intro

---
Let’s now **polish your JARVIS Lite** with:

---

## ✅ 1. Branding Integration

## ✅ 2. Full Dark/Light Theme Switcher

With persistent settings and dynamic styles throughout the app.

---

## ✅ PART 1: Branding Setup

### 🧠 Brand Assets (suggested)

* App name: **JΛRVIS Lite**
* Logo: Optional — text-based glowing title
* Theme color: `#00FFB2` (Teal Neon)

### 📁 Create `/theme/branding.ts`

```ts
export const AppBrand = {
  name: 'JΛRVIS Lite',
  primaryColor: '#00FFB2',
  darkColor: '#000',
  lightColor: '#ffffff',
  slogan: 'Offline AI. Always with you.',
};
```

You can import this into splash screen, onboarding, or anywhere.

---

## ✅ PART 2: Dark/Light Theme Toggle

### 📁 1. Create `/theme/colors.ts`

```ts
export const lightTheme = {
  background: '#ffffff',
  text: '#000000',
  card: '#f0f0f0',
  primary: '#00FFB2',
};

export const darkTheme = {
  background: '#000000',
  text: '#ffffff',
  card: '#1c1c1c',
  primary: '#00FFB2',
};
```

---

### 📁 2. Create `/context/ThemeContext.tsx`

```tsx
import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { lightTheme, darkTheme } from '../theme/colors';

const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const [themeName, setThemeName] = useState('dark');
  const theme = themeName === 'light' ? lightTheme : darkTheme;

  useEffect(() => {
    (async () => {
      const saved = await AsyncStorage.getItem('theme');
      if (saved) setThemeName(saved);
    })();
  }, []);

  const toggleTheme = async () => {
    const next = themeName === 'light' ? 'dark' : 'light';
    setThemeName(next);
    await AsyncStorage.setItem('theme', next);
  };

  return (
    <ThemeContext.Provider value={{ theme, themeName, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => useContext(ThemeContext);
```

Wrap your entire `App.tsx` in:

```tsx
<ThemeProvider>
  <NavigationContainer>{/* stack here */}</NavigationContainer>
</ThemeProvider>
```

---

### 📁 3. Add Theme Switch Button Anywhere

Example (in `MainAssistantScreen.tsx` or `SettingsScreen.tsx`):

```tsx
import { useTheme } from '../context/ThemeContext';

const { theme, toggleTheme, themeName } = useTheme();

<TouchableOpacity onPress={toggleTheme} style={{ padding: 8 }}>
  <Text style={{ color: theme.primary }}>
    {themeName === 'light' ? '🌙 Dark Mode' : '☀️ Light Mode'}
  </Text>
</TouchableOpacity>
```

---

### ✅ Example Themed UI Usage

```tsx
<View style={{ backgroundColor: theme.background, flex: 1 }}>
  <Text style={{ color: theme.text }}>Hello, {AppBrand.name}</Text>
</View>
```

✅ All colors are now adaptive
✅ Theme setting is saved and restored
✅ Brand identity is consistent across UI

---

## 🧠 Recap

| Feature                  | Status |
| ------------------------ | ------ |
| Brand name & slogan      | ✅      |
| Neon green primary color | ✅      |
| Dark/Light mode toggle   | ✅      |
| Persistent theme storage | ✅      |
| Reusable `theme` context | ✅      |

---

Let’s complete your **Settings Screen** for JΛRVIS Lite with:

---

## ✅ Features:

1. 🎨 Theme Toggle (Dark/Light)
2. 🎤 Voice Test Button (speak + listen loop)
3. 📴 Offline Mode Status (always on by default)
4. 🔄 Reset Assistant (clear memory, logs, reminders)

---

## 📁 `SettingsScreen.tsx`

```tsx
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from '../context/ThemeContext';
import { AppBrand } from '../theme/branding';
import { speak } from '../modules/OfflineTTS';
import { clearMemory } from '../modules/LocalMemory';
import { clearReminders } from '../modules/ReminderService';

export default function SettingsScreen() {
  const { theme, toggleTheme, themeName } = useTheme();

  const handleVoiceTest = async () => {
    await speak("This is a voice test of JARVIS Lite. I am ready.");
  };

  const handleReset = async () => {
    Alert.alert(
      "Reset Assistant?",
      "This will delete all reminders, rules, and local memory.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Reset",
          style: "destructive",
          onPress: async () => {
            await clearMemory();
            await clearReminders();
            await AsyncStorage.clear();
            await speak("All data has been reset.");
          },
        },
      ]
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <Text style={[styles.heading, { color: theme.text }]}>
        ⚙️ Settings
      </Text>

      <TouchableOpacity style={styles.button} onPress={toggleTheme}>
        <Text style={[styles.buttonText, { color: theme.text }]}>
          Theme: {themeName === 'light' ? '☀️ Light' : '🌙 Dark'} (Tap to toggle)
        </Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={handleVoiceTest}>
        <Text style={[styles.buttonText, { color: theme.text }]}>
          🎤 Voice Test
        </Text>
      </TouchableOpacity>

      <View style={styles.status}>
        <Text style={[styles.statusText, { color: theme.text }]}>
          📡 Offline Mode: <Text style={{ color: theme.primary }}>Enabled</Text>
        </Text>
      </View>

      <TouchableOpacity style={[styles.resetBtn]} onPress={handleReset}>
        <Text style={{ color: '#fff', fontWeight: 'bold' }}>
          🔄 Reset JΛRVIS Lite
        </Text>
      </TouchableOpacity>

      <Text style={[styles.footer, { color: theme.text }]}>
        {AppBrand.name} · {AppBrand.slogan}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 24, justifyContent: 'center' },
  heading: { fontSize: 24, fontWeight: 'bold', marginBottom: 20 },
  button: { marginBottom: 16 },
  buttonText: { fontSize: 18 },
  status: { marginTop: 24, marginBottom: 24 },
  statusText: { fontSize: 16 },
  resetBtn: {
    padding: 12,
    backgroundColor: '#FF4444',
    borderRadius: 8,
    alignItems: 'center',
  },
  footer: { textAlign: 'center', marginTop: 40, opacity: 0.6 },
});
```

---

## 🧠 Supporting Functions (Modules)

Make sure these exist:

### 🔄 `/modules/LocalMemory.ts`

```ts
export async function clearMemory() {
  await AsyncStorage.removeItem('learned_patterns');
}
```

### 🗓️ `/modules/ReminderService.ts`

```ts
export async function clearReminders() {
  await AsyncStorage.removeItem('reminders');
}
```

### 🗣️ `/modules/OfflineTTS.ts`

```ts
export async function speak(text: string) {
  // use your TTS wrapper
}
```

---

## ✅ Navigation Stack

Add to your stack:

```tsx
<Stack.Screen name="Settings" component={SettingsScreen} />
```

---

## ✅ Final Settings Screen Features

| Feature                | Done |
| ---------------------- | ---- |
| Dark/Light Mode Toggle | ✅    |
| Voice Test             | ✅    |
| Offline Mode Indicator | ✅    |
| Reset All Data         | ✅    |
| Branding Footer        | ✅    |

---

Let’s now make JΛRVIS Lite **feel magical** by letting users say:

> 🗣 “Open settings” or use a gesture (e.g., **2-finger double tap**) to open the **Settings screen** — all offline.

---

## ✅ OPTION 1: Voice Hotword → “Open Settings”

### 🔧 In `IntentParser.ts`

Add:

```ts
if (lower.includes('open settings') || lower.includes('settings menu')) {
  return { intent: 'OPEN_SETTINGS' };
}
```

---

### 🔧 In `MainAssistantScreen.tsx`

Add to the intent switch:

```ts
case 'OPEN_SETTINGS':
  await OfflineTTS.speak("Opening settings.");
  navigation.navigate('Settings');
  break;
```

Done ✅ — works with voice only, even offline.

---

## ✅ OPTION 2: 2-Finger Double Tap Gesture

You can use [react-native-gesture-handler](https://docs.swmansion.com/react-native-gesture-handler/docs/) or just fallback to basic `onTouchStart`/`onTouchEnd` logic if you're keeping it simple.

Here’s a minimal version:

### 📁 Add gesture in `MainAssistantScreen.tsx`

```tsx
import { GestureDetector, Gesture } from 'react-native-gesture-handler';

const doubleTap = Gesture.Tap()
  .numberOfTaps(2)
  .maxDelay(300)
  .onEnd((e, success) => {
    if (e.numberOfPointers === 2) {
      navigation.navigate('Settings');
    }
  });

return (
  <GestureDetector gesture={doubleTap}>
    <View style={{ flex: 1, backgroundColor: theme.background }}>
      {/* rest of assistant UI here */}
    </View>
  </GestureDetector>
);
```

Make sure to wrap the root view with `GestureHandlerRootView` in your `App.tsx`.

---

## ✅ Option 3: Hidden Settings Command via Tap

If gesture lib is not available, fallback:

```tsx
<TouchableOpacity
  onLongPress={() => navigation.navigate('Settings')}
  style={{ position: 'absolute', bottom: 10, right: 10, padding: 12 }}
>
  <Text style={{ color: theme.primary, fontSize: 12 }}>⚙️</Text>
</TouchableOpacity>
```

---

## 🧠 Final Summary

| Access Method              | Description           | Status |
| -------------------------- | --------------------- | ------ |
| 🎙️ Voice: “Open settings” | Via intent parser     | ✅      |
| ✌️ 2-finger double tap     | Via gesture detection | ✅      |
| 🕹️ Long press hidden icon | Fallback option       | ✅      |



Let’s now lock your **JΛRVIS Lite Settings screen** with biometric security 🔐 — so **only the device owner** can access it.

---

## ✅ Features We’ll Add:

1. 🔒 Fingerprint/FaceID authentication on entering Settings
2. ✅ Secure fallback (PIN or deny access)
3. 💾 Local flag to enable/disable biometric locking (optional toggle)

---

## 🔧 1. Install Biometric Auth Module

Install [expo-local-authentication](https://docs.expo.dev/versions/latest/sdk/local-authentication/):

```bash
npx expo install expo-local-authentication
```

---

## 📁 2. Secure `SettingsScreen.tsx` with Biometric Prompt

Wrap the entire screen logic with authentication check:

```tsx
import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as LocalAuthentication from 'expo-local-authentication';
import { useTheme } from '../context/ThemeContext';
import { AppBrand } from '../theme/branding';
import { speak } from '../modules/OfflineTTS';
import { clearMemory } from '../modules/LocalMemory';
import { clearReminders } from '../modules/ReminderService';

export default function SettingsScreen({ navigation }) {
  const { theme, toggleTheme, themeName } = useTheme();
  const [authenticated, setAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    (async () => {
      const isSupported = await LocalAuthentication.hasHardwareAsync();
      const enrolled = await LocalAuthentication.isEnrolledAsync();

      if (!isSupported || !enrolled) {
        Alert.alert("Biometric not available", "Your device doesn't support biometrics.");
        navigation.goBack();
        return;
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to access settings',
        fallbackLabel: 'Use PIN',
      });

      if (result.success) {
        setAuthenticated(true);
      } else {
        Alert.alert("Access Denied", "Biometric authentication failed.");
        navigation.goBack();
      }

      setLoading(false);
    })();
  }, []);

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.background }]}>
        <ActivityIndicator size="large" color={theme.primary} />
        <Text style={{ color: theme.text, marginTop: 20 }}>Verifying...</Text>
      </View>
    );
  }

  if (!authenticated) return null;

  // 🧠 Place your existing SettingsScreen content below this line 👇

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <Text style={[styles.heading, { color: theme.text }]}>⚙️ Settings</Text>

      <TouchableOpacity style={styles.button} onPress={toggleTheme}>
        <Text style={[styles.buttonText, { color: theme.text }]}>
          Theme: {themeName === 'light' ? '☀️ Light' : '🌙 Dark'} (Tap to toggle)
        </Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={() => speak("This is a voice test.")}>
        <Text style={[styles.buttonText, { color: theme.text }]}>🎤 Voice Test</Text>
      </TouchableOpacity>

      <View style={styles.status}>
        <Text style={[styles.statusText, { color: theme.text }]}>
          📡 Offline Mode: <Text style={{ color: theme.primary }}>Enabled</Text>
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.resetBtn]}
        onPress={async () => {
          await clearMemory();
          await clearReminders();
          await AsyncStorage.clear();
          await speak("All data has been reset.");
        }}
      >
        <Text style={{ color: '#fff', fontWeight: 'bold' }}>🔄 Reset JΛRVIS Lite</Text>
      </TouchableOpacity>

      <Text style={[styles.footer, { color: theme.text }]}>
        {AppBrand.name} · {AppBrand.slogan}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 24, justifyContent: 'center' },
  heading: { fontSize: 24, fontWeight: 'bold', marginBottom: 20 },
  button: { marginBottom: 16 },
  buttonText: { fontSize: 18 },
  status: { marginTop: 24, marginBottom: 24 },
  statusText: { fontSize: 16 },
  resetBtn: {
    padding: 12,
    backgroundColor: '#FF4444',
    borderRadius: 8,
    alignItems: 'center',
  },
  footer: { textAlign: 'center', marginTop: 40, opacity: 0.6 },
});
```

---

## ✅ Biometric Flow Summary

| Step                             | Result                         |
| -------------------------------- | ------------------------------ |
| Device supports fingerprint/face | Prompt shown ✅                 |
| User authenticates               | Access granted to settings ✅   |
| Auth fails or skipped            | Redirects to previous screen ❌ |

---
Let’s now make **biometric locking user-controllable** — by adding a toggle:

> ✅ **“Lock Settings”** toggle
> → When ON, biometric auth is required to access Settings.
> → When OFF, user enters freely.

---

## ✅ Step 1: Save Lock Preference in Storage

### 🔧 Create helper: `/utils/SettingsLock.ts`

```ts
import AsyncStorage from '@react-native-async-storage/async-storage';

const LOCK_KEY = 'jarvis_settings_locked';

export async function isSettingsLocked(): Promise<boolean> {
  const value = await AsyncStorage.getItem(LOCK_KEY);
  return value === 'true';
}

export async function setSettingsLock(enabled: boolean): Promise<void> {
  await AsyncStorage.setItem(LOCK_KEY, enabled ? 'true' : 'false');
}
```

---

## ✅ Step 2: Add Lock Toggle to `SettingsScreen.tsx`

### 📦 Update imports:

```tsx
import { isSettingsLocked, setSettingsLock } from '../utils/SettingsLock';
```

### 🔄 Add State + Toggle Button in `SettingsScreen`

Right after the theme toggle and voice test:

```tsx
const [locked, setLocked] = useState(false);

useEffect(() => {
  (async () => {
    const stored = await isSettingsLocked();
    setLocked(stored);
  })();
}, []);

const toggleLock = async () => {
  const newState = !locked;
  setLocked(newState);
  await setSettingsLock(newState);
  await speak(newState ? "Settings are now locked." : "Settings lock disabled.");
};
```

Then add this below the theme and voice test buttons:

```tsx
<TouchableOpacity style={styles.button} onPress={toggleLock}>
  <Text style={[styles.buttonText, { color: theme.text }]}>
    🔒 Lock Settings: {locked ? 'Enabled' : 'Disabled'}
  </Text>
</TouchableOpacity>
```

---

## ✅ Step 3: Enforce Lock in Settings Entry

### In `SettingsScreen.tsx` **top-level `useEffect()`**, replace with:

```tsx
useEffect(() => {
  (async () => {
    const locked = await isSettingsLocked();

    if (!locked) {
      setAuthenticated(true);
      setLoading(false);
      return;
    }

    const isSupported = await LocalAuthentication.hasHardwareAsync();
    const enrolled = await LocalAuthentication.isEnrolledAsync();

    if (!isSupported || !enrolled) {
      Alert.alert("Biometric not available", "Your device doesn't support biometrics.");
      navigation.goBack();
      return;
    }

    const result = await LocalAuthentication.authenticateAsync({
      promptMessage: 'Authenticate to access settings',
      fallbackLabel: 'Use PIN',
    });

    if (result.success) {
      setAuthenticated(true);
    } else {
      Alert.alert("Access Denied", "Biometric authentication failed.");
      navigation.goBack();
    }

    setLoading(false);
  })();
}, []);
```

---

## ✅ Final Settings UI

| Feature                 | Status |
| ----------------------- | ------ |
| 🔄 Theme Toggle         | ✅      |
| 🎤 Voice Test           | ✅      |
| 📴 Offline Mode         | ✅      |
| 🔒 Settings Lock Toggle | ✅      |
| 👆 Respects Lock State  | ✅      |

---

## 🔐 Experience

| Lock Toggle  | Behavior                          |
| ------------ | --------------------------------- |
| **Enabled**  | Fingerprint/Face required on open |
| **Disabled** | Opens instantly                   |

---



