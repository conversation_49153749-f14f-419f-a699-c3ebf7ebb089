/**
 * OfflineTTS.ts
 * Offline text-to-speech using Coqui TTS, Flite, or system TTS
 * Provides voice output for the AI assistant
 */

import Tts from 'react-native-tts';
import { TTSConfig } from '@/types';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface TTSVoice {
  id: string;
  name: string;
  language: string;
  quality: string;
}

interface TTSStatus {
  isInitialized: boolean;
  isPlaying: boolean;
  currentVoice: string;
  availableVoices: TTSVoice[];
}

class OfflineTTS {
  private isInitialized = false;
  private currentConfig: TTSConfig;
  private availableVoices: TTSVoice[] = [];
  private isPlaying = false;
  private queue: string[] = [];
  private isProcessingQueue = false;

  constructor() {
    this.currentConfig = {
      engine: 'system',
      voice: 'default',
      speed: 1.0,
      pitch: 1.0,
      volume: 1.0
    };
    
    this.initialize();
  }

  /**
   * Initialize TTS engine
   */
  private async initialize(): Promise<void> {
    try {
      // Load saved configuration
      await this.loadConfiguration();

      // Initialize React Native TTS
      await this.initializeSystemTTS();

      // Set up event listeners
      this.setupEventListeners();

      this.isInitialized = true;
      console.log('OfflineTTS: Initialized successfully');
    } catch (error) {
      console.error('OfflineTTS: Failed to initialize:', error);
    }
  }

  /**
   * Initialize system TTS
   */
  private async initializeSystemTTS(): Promise<void> {
    try {
      // Get available voices
      const voices = await Tts.voices();
      this.availableVoices = voices.map((voice: any) => ({
        id: voice.id,
        name: voice.name,
        language: voice.language,
        quality: voice.quality || 'normal'
      }));

      // Set default voice if available
      if (this.availableVoices.length > 0) {
        const defaultVoice = this.availableVoices.find(v => 
          v.language.startsWith('en') && v.name.toLowerCase().includes('female')
        ) || this.availableVoices[0];
        
        await Tts.setDefaultVoice(defaultVoice.id);
        this.currentConfig.voice = defaultVoice.id;
      }

      // Set speech rate and pitch
      await Tts.setDefaultRate(this.currentConfig.speed);
      await Tts.setDefaultPitch(this.currentConfig.pitch);

      console.log(`OfflineTTS: Found ${this.availableVoices.length} voices`);
    } catch (error) {
      console.error('OfflineTTS: Failed to initialize system TTS:', error);
    }
  }

  /**
   * Set up TTS event listeners
   */
  private setupEventListeners(): void {
    Tts.addEventListener('tts-start', () => {
      this.isPlaying = true;
      console.log('OfflineTTS: Started speaking');
    });

    Tts.addEventListener('tts-finish', () => {
      this.isPlaying = false;
      console.log('OfflineTTS: Finished speaking');
      this.processQueue();
    });

    Tts.addEventListener('tts-cancel', () => {
      this.isPlaying = false;
      console.log('OfflineTTS: Speech cancelled');
      this.processQueue();
    });

    Tts.addEventListener('tts-error', (error) => {
      this.isPlaying = false;
      console.error('OfflineTTS: Speech error:', error);
      this.processQueue();
    });
  }

  /**
   * Speak text aloud
   */
  public async speak(text: string, interrupt: boolean = false): Promise<void> {
    if (!this.isInitialized) {
      console.warn('OfflineTTS: Not initialized');
      return;
    }

    if (!text || text.trim().length === 0) {
      console.warn('OfflineTTS: Empty text provided');
      return;
    }

    try {
      // Clean and prepare text
      const cleanText = this.prepareText(text);

      if (interrupt) {
        // Stop current speech and clear queue
        await this.stop();
        this.queue = [];
        await Tts.speak(cleanText);
      } else if (this.isPlaying) {
        // Add to queue if currently speaking
        this.queue.push(cleanText);
      } else {
        // Speak immediately
        await Tts.speak(cleanText);
      }
    } catch (error) {
      console.error('OfflineTTS: Error speaking text:', error);
    }
  }

  /**
   * Stop current speech
   */
  public async stop(): Promise<void> {
    try {
      await Tts.stop();
      this.isPlaying = false;
      this.queue = [];
    } catch (error) {
      console.error('OfflineTTS: Error stopping speech:', error);
    }
  }

  /**
   * Pause current speech
   */
  public async pause(): Promise<void> {
    try {
      // Note: React Native TTS doesn't support pause/resume
      // We'll stop instead
      await this.stop();
    } catch (error) {
      console.error('OfflineTTS: Error pausing speech:', error);
    }
  }

  /**
   * Set voice by ID
   */
  public async setVoice(voiceId: string): Promise<boolean> {
    try {
      const voice = this.availableVoices.find(v => v.id === voiceId);
      if (!voice) {
        console.warn(`OfflineTTS: Voice ${voiceId} not found`);
        return false;
      }

      await Tts.setDefaultVoice(voiceId);
      this.currentConfig.voice = voiceId;
      await this.saveConfiguration();
      
      console.log(`OfflineTTS: Voice set to ${voice.name}`);
      return true;
    } catch (error) {
      console.error('OfflineTTS: Error setting voice:', error);
      return false;
    }
  }

  /**
   * Set speech rate (0.1 - 2.0)
   */
  public async setSpeed(speed: number): Promise<void> {
    try {
      const clampedSpeed = Math.max(0.1, Math.min(2.0, speed));
      await Tts.setDefaultRate(clampedSpeed);
      this.currentConfig.speed = clampedSpeed;
      await this.saveConfiguration();
      
      console.log(`OfflineTTS: Speed set to ${clampedSpeed}`);
    } catch (error) {
      console.error('OfflineTTS: Error setting speed:', error);
    }
  }

  /**
   * Set speech pitch (0.5 - 2.0)
   */
  public async setPitch(pitch: number): Promise<void> {
    try {
      const clampedPitch = Math.max(0.5, Math.min(2.0, pitch));
      await Tts.setDefaultPitch(clampedPitch);
      this.currentConfig.pitch = clampedPitch;
      await this.saveConfiguration();
      
      console.log(`OfflineTTS: Pitch set to ${clampedPitch}`);
    } catch (error) {
      console.error('OfflineTTS: Error setting pitch:', error);
    }
  }

  /**
   * Get available voices
   */
  public getAvailableVoices(): TTSVoice[] {
    return [...this.availableVoices];
  }

  /**
   * Get current configuration
   */
  public getCurrentConfig(): TTSConfig {
    return { ...this.currentConfig };
  }

  /**
   * Get TTS status
   */
  public getStatus(): TTSStatus {
    return {
      isInitialized: this.isInitialized,
      isPlaying: this.isPlaying,
      currentVoice: this.currentConfig.voice,
      availableVoices: this.availableVoices
    };
  }

  /**
   * Test voice with sample text
   */
  public async testVoice(voiceId?: string): Promise<void> {
    const originalVoice = this.currentConfig.voice;
    
    try {
      if (voiceId && voiceId !== originalVoice) {
        await this.setVoice(voiceId);
      }
      
      const sampleTexts = [
        "Hello, I'm JARVIS Lite, your offline AI assistant.",
        "How can I help you today?",
        "I'm ready to assist you with various tasks."
      ];
      
      const randomText = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
      await this.speak(randomText, true);
      
    } catch (error) {
      console.error('OfflineTTS: Error testing voice:', error);
    } finally {
      // Restore original voice if we changed it
      if (voiceId && voiceId !== originalVoice) {
        setTimeout(() => this.setVoice(originalVoice), 3000);
      }
    }
  }

  /**
   * Process speech queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.queue.length === 0 || this.isPlaying) {
      return;
    }

    this.isProcessingQueue = true;
    
    try {
      const nextText = this.queue.shift();
      if (nextText) {
        await Tts.speak(nextText);
      }
    } catch (error) {
      console.error('OfflineTTS: Error processing queue:', error);
    } finally {
      this.isProcessingQueue = false;
    }
  }

  /**
   * Prepare text for speech
   */
  private prepareText(text: string): string {
    return text
      // Remove markdown formatting
      .replace(/\*\*(.*?)\*\*/g, '$1')
      .replace(/\*(.*?)\*/g, '$1')
      .replace(/`(.*?)`/g, '$1')
      // Replace common abbreviations
      .replace(/\bDr\./g, 'Doctor')
      .replace(/\bMr\./g, 'Mister')
      .replace(/\bMrs\./g, 'Missus')
      .replace(/\bMs\./g, 'Miss')
      .replace(/\betc\./g, 'etcetera')
      .replace(/\bi\.e\./g, 'that is')
      .replace(/\be\.g\./g, 'for example')
      // Handle numbers and time
      .replace(/(\d+):(\d+)/g, '$1 $2')
      .replace(/(\d+)%/g, '$1 percent')
      // Clean up extra spaces and punctuation
      .replace(/\s+/g, ' ')
      .replace(/([.!?])\s*([.!?])/g, '$1')
      .trim();
  }

  /**
   * Load configuration from storage
   */
  private async loadConfiguration(): Promise<void> {
    try {
      const configJson = await AsyncStorage.getItem('tts_config');
      if (configJson) {
        const config = JSON.parse(configJson);
        this.currentConfig = { ...this.currentConfig, ...config };
      }
    } catch (error) {
      console.error('OfflineTTS: Error loading configuration:', error);
    }
  }

  /**
   * Save configuration to storage
   */
  private async saveConfiguration(): Promise<void> {
    try {
      await AsyncStorage.setItem('tts_config', JSON.stringify(this.currentConfig));
    } catch (error) {
      console.error('OfflineTTS: Error saving configuration:', error);
    }
  }

  /**
   * Reset to default configuration
   */
  public async resetConfiguration(): Promise<void> {
    this.currentConfig = {
      engine: 'system',
      voice: 'default',
      speed: 1.0,
      pitch: 1.0,
      volume: 1.0
    };
    
    await this.saveConfiguration();
    await this.initializeSystemTTS();
  }

  /**
   * Cleanup resources
   */
  public async cleanup(): Promise<void> {
    try {
      await this.stop();
      Tts.removeAllListeners('tts-start');
      Tts.removeAllListeners('tts-finish');
      Tts.removeAllListeners('tts-cancel');
      Tts.removeAllListeners('tts-error');
      
      this.isInitialized = false;
      console.log('OfflineTTS: Cleanup completed');
    } catch (error) {
      console.error('OfflineTTS: Error during cleanup:', error);
    }
  }
}

export const offlineTTS = new OfflineTTS();
