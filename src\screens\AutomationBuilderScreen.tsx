/**
 * AutomationBuilderScreen.tsx
 * Interface for creating and managing automation rules
 * Allows users to define "If This, Then That" style automations
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Modal,
  TextInput,
  StyleSheet,
  Switch,
  ScrollView,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { AutomationRule, AVAILABLE_ACTIONS, CATEGORIES, getActionById, formatTriggerTime } from '../models/AutomationRule';
import { getRules, saveRule, deleteRule, toggleRule, getStorageStats } from '../modules/AutomationStorage';

const AutomationBuilderScreen: React.FC = () => {
  const [rules, setRules] = useState<AutomationRule[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [selectedTime, setSelectedTime] = useState('09:00');
  const [selectedAction, setSelectedAction] = useState(AVAILABLE_ACTIONS[0].id);
  const [selectedCategory, setSelectedCategory] = useState<'Media' | 'Utility' | 'Wellness' | 'Custom'>('Media');
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({ totalRules: 0, activeRules: 0, categoryCounts: {} });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const [rulesData, statsData] = await Promise.all([
        getRules(),
        getStorageStats()
      ]);
      setRules(rulesData);
      setStats(statsData);
    } catch (error) {
      console.error('[AutomationBuilder] Error loading data:', error);
      Alert.alert('Error', 'Failed to load automation rules');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreate = async () => {
    try {
      const rule: AutomationRule = {
        id: Date.now().toString(),
        triggerTime: selectedTime,
        action: selectedAction,
        category: selectedCategory,
        enabled: true,
        createdAt: new Date().toISOString(),
      };

      const success = await saveRule(rule);
      if (success) {
        setShowModal(false);
        await loadData();
        Alert.alert('Success', 'Automation rule created successfully!');
      } else {
        Alert.alert('Error', 'Failed to create automation rule');
      }
    } catch (error) {
      console.error('[AutomationBuilder] Error creating rule:', error);
      Alert.alert('Error', 'Failed to create automation rule');
    }
  };

  const handleDelete = async (id: string) => {
    Alert.alert(
      'Delete Rule',
      'Are you sure you want to delete this automation rule?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const success = await deleteRule(id);
            if (success) {
              await loadData();
            } else {
              Alert.alert('Error', 'Failed to delete rule');
            }
          },
        },
      ]
    );
  };

  const handleToggle = async (id: string, enabled: boolean) => {
    const success = await toggleRule(id, enabled);
    if (success) {
      await loadData();
    } else {
      Alert.alert('Error', 'Failed to update rule');
    }
  };

  const resetModal = () => {
    setSelectedTime('09:00');
    setSelectedAction(AVAILABLE_ACTIONS[0].id);
    setSelectedCategory('Media');
  };

  const renderRule = ({ item }: { item: AutomationRule }) => {
    const action = getActionById(item.action);
    const actionLabel = action?.label || item.action;

    return (
      <View style={styles.card}>
        <View style={styles.cardHeader}>
          <Text style={styles.timeText}>⏰ {formatTriggerTime(item.triggerTime)}</Text>
          <View style={[styles.categoryBadge, { backgroundColor: getCategoryColor(item.category) }]}>
            <Text style={styles.categoryText}>{item.category}</Text>
          </View>
        </View>

        <Text style={styles.actionText}>⚡ {actionLabel}</Text>

        <View style={styles.cardFooter}>
          <Switch
            value={item.enabled}
            onValueChange={(val) => handleToggle(item.id, val)}
            trackColor={{ false: '#767577', true: '#00FFB2' }}
            thumbColor={item.enabled ? '#fff' : '#f4f3f4'}
          />
          <TouchableOpacity onPress={() => handleDelete(item.id)} style={styles.deleteButton}>
            <Icon name="delete" size={20} color="#ff4444" />
            <Text style={styles.deleteText}>Delete</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const getCategoryColor = (category: string): string => {
    switch (category) {
      case 'Media': return '#FF6B6B';
      case 'Utility': return '#4ECDC4';
      case 'Wellness': return '#45B7D1';
      case 'Custom': return '#96CEB4';
      default: return '#95A5A6';
    }
  };

  const getFilteredActions = () => {
    return AVAILABLE_ACTIONS.filter(action => action.category === selectedCategory);
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.loadingText}>Loading automation rules...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>⚙️ Automation Rules</Text>

      {/* Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.totalRules}</Text>
          <Text style={styles.statLabel}>Total Rules</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.activeRules}</Text>
          <Text style={styles.statLabel}>Active</Text>
        </View>
      </View>

      <FlatList
        data={rules}
        keyExtractor={(item) => item.id}
        renderItem={renderRule}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="robot" size={64} color="#666" />
            <Text style={styles.emptyText}>No automation rules defined.</Text>
            <Text style={styles.emptySubtext}>Tap the + button to create your first rule!</Text>
          </View>
        }
        contentContainerStyle={rules.length === 0 ? styles.emptyList : undefined}
      />

      {/* Floating Action Button */}
      <TouchableOpacity
        style={styles.fab}
        onPress={() => {
          resetModal();
          setShowModal(true);
        }}
      >
        <Icon name="plus" size={24} color="#000" />
      </TouchableOpacity>

      {/* Create Rule Modal */}
      <Modal visible={showModal} transparent animationType="slide">
        <View style={styles.modalOverlay}>
          <View style={styles.modal}>
            <ScrollView>
              <Text style={styles.modalTitle}>New Automation Rule</Text>

              {/* Time Input */}
              <Text style={styles.label}>Trigger Time:</Text>
              <TextInput
                placeholder="HH:MM (e.g., 09:00)"
                style={styles.input}
                value={selectedTime}
                onChangeText={setSelectedTime}
                maxLength={5}
              />

              {/* Category Selection */}
              <Text style={styles.label}>Category:</Text>
              <View style={styles.categoryRow}>
                {CATEGORIES.map((cat) => (
                  <TouchableOpacity
                    key={cat}
                    onPress={() => setSelectedCategory(cat)}
                    style={[
                      styles.categoryButton,
                      selectedCategory === cat && styles.categoryButtonActive,
                      { backgroundColor: selectedCategory === cat ? getCategoryColor(cat) : '#333' }
                    ]}
                  >
                    <Text style={[
                      styles.categoryButtonText,
                      selectedCategory === cat && styles.categoryButtonTextActive
                    ]}>
                      {cat}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Action Selection */}
              <Text style={styles.label}>Action:</Text>
              {getFilteredActions().map((action) => (
                <TouchableOpacity
                  key={action.id}
                  onPress={() => setSelectedAction(action.id)}
                  style={[
                    styles.actionOption,
                    selectedAction === action.id && styles.selectedAction,
                  ]}
                >
                  <Text style={[
                    styles.actionOptionText,
                    selectedAction === action.id && styles.selectedActionText
                  ]}>
                    {action.label}
                  </Text>
                  {action.description && (
                    <Text style={styles.actionDescription}>{action.description}</Text>
                  )}
                </TouchableOpacity>
              ))}

              {/* Buttons */}
              <TouchableOpacity onPress={handleCreate} style={styles.saveButton}>
                <Text style={styles.saveButtonText}>Save Rule</Text>
              </TouchableOpacity>

              <TouchableOpacity onPress={() => setShowModal(false)} style={styles.cancelButton}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
    padding: 16,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  heading: {
    fontSize: 22,
    color: '#00FFB2',
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  loadingText: {
    color: '#aaa',
    fontSize: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
    backgroundColor: '#1a1a1a',
    borderRadius: 8,
    padding: 12,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#00FFB2',
  },
  statLabel: {
    fontSize: 12,
    color: '#aaa',
    marginTop: 4,
  },
  card: {
    backgroundColor: '#1a1a1a',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#00FFB2',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  timeText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: 'bold',
  },
  actionText: {
    fontSize: 14,
    color: '#ccc',
    marginBottom: 12,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deleteText: {
    color: '#ff4444',
    fontWeight: 'bold',
    marginLeft: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyList: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  emptyText: {
    color: '#aaa',
    fontStyle: 'italic',
    marginTop: 16,
    textAlign: 'center',
    fontSize: 16,
  },
  emptySubtext: {
    color: '#666',
    textAlign: 'center',
    fontSize: 14,
    marginTop: 8,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: '#00FFB2',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modal: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  label: {
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 8,
    fontSize: 14,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
    fontSize: 16,
  },
  categoryRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  categoryButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  categoryButtonActive: {
    // Color set dynamically
  },
  categoryButtonText: {
    fontSize: 12,
    color: '#ccc',
  },
  categoryButtonTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  actionOption: {
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
    backgroundColor: '#f5f5f5',
  },
  selectedAction: {
    backgroundColor: '#00FFB2',
  },
  actionOptionText: {
    fontSize: 14,
    color: '#333',
  },
  selectedActionText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  actionDescription: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  saveButton: {
    backgroundColor: '#00FFB2',
    padding: 14,
    borderRadius: 6,
    marginTop: 16,
  },
  saveButtonText: {
    textAlign: 'center',
    fontWeight: 'bold',
    color: '#000',
    fontSize: 16,
  },
  cancelButton: {
    padding: 14,
    marginTop: 8,
  },
  cancelButtonText: {
    textAlign: 'center',
    color: '#666',
    fontSize: 16,
  },
});

export default AutomationBuilderScreen;
