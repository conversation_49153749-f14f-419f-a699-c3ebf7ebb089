/**
 * PlaceholderScreens.tsx
 * Placeholder screens for navigation routes
 * These will be replaced with full implementations later
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';

import { useTheme, getThemedStyles } from '@/utils/ThemeContext';

interface PlaceholderScreenProps {
  title: string;
  description: string;
  features?: string[];
}

const PlaceholderScreen: React.FC<PlaceholderScreenProps> = ({
  title,
  description,
  features = [],
}) => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const styles = getThemedStyles(theme);

  return (
    <SafeAreaView style={[styles.container, localStyles.container]}>
      <ScrollView contentContainerStyle={localStyles.content}>
        <View style={localStyles.header}>
          <Text style={[styles.text, localStyles.title]}>{title}</Text>
          <Text style={[styles.textSecondary, localStyles.description]}>
            {description}
          </Text>
        </View>

        {features.length > 0 && (
          <View style={localStyles.featuresContainer}>
            <Text style={[styles.text, localStyles.featuresTitle]}>
              Coming Soon:
            </Text>
            {features.map((feature, index) => (
              <View key={index} style={localStyles.featureItem}>
                <Text style={localStyles.featureBullet}>•</Text>
                <Text style={[styles.textSecondary, localStyles.featureText]}>
                  {feature}
                </Text>
              </View>
            ))}
          </View>
        )}

        <TouchableOpacity
          style={[styles.button, localStyles.backButton]}
          onPress={() => navigation.goBack()}
        >
          <Text style={[styles.buttonText, localStyles.backButtonText]}>
            Go Back
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

// Individual screen components
export const ActivityTimelineScreen: React.FC = () => (
  <PlaceholderScreen
    title="Activity Timeline 📊"
    description="View your interaction history and usage patterns"
    features={[
      'Daily activity charts',
      'Command frequency analysis',
      'Usage pattern insights',
      'Time-based activity logs',
      'Export activity data',
    ]}
  />
);

export const SettingsScreen: React.FC = () => (
  <PlaceholderScreen
    title="Settings ⚙️"
    description="Customize your JARVIS Lite experience"
    features={[
      'Voice settings configuration',
      'Privacy controls',
      'Theme selection',
      'Model management',
      'Permission settings',
      'Data retention options',
    ]}
  />
);

export const AssistantPersonalityScreen: React.FC = () => (
  <PlaceholderScreen
    title="Assistant Personality 🎭"
    description="Customize how JARVIS Lite responds and behaves"
    features={[
      'Personality mode selection',
      'Custom avatar and emoji',
      'Voice tone preferences',
      'Response style settings',
      'Custom greetings',
      'Behavior patterns',
    ]}
  />
);

export const ReminderScheduleScreen: React.FC = () => (
  <PlaceholderScreen
    title="Reminders ⏰"
    description="Create and manage your reminders and schedules"
    features={[
      'Voice-activated reminders',
      'Recurring schedules',
      'Smart notifications',
      'Location-based reminders',
      'Reminder categories',
      'Snooze and reschedule',
    ]}
  />
);

export const AutomationBuilderScreen: React.FC = () => (
  <PlaceholderScreen
    title="Automation Builder 🔧"
    description="Create custom automations and workflows"
    features={[
      'If-this-then-that rules',
      'Time-based triggers',
      'App launch automations',
      'Device state monitoring',
      'Custom action chains',
      'Smart suggestions',
    ]}
  />
);

export const LearningInsightsScreen: React.FC = () => (
  <PlaceholderScreen
    title="Learning Insights 🧠"
    description="See how JARVIS Lite learns from your behavior"
    features={[
      'Pattern recognition results',
      'Habit analysis',
      'Learning confidence scores',
      'Behavior predictions',
      'Usage optimization tips',
      'Privacy-safe insights',
    ]}
  />
);

export const DebugAndLogsScreen: React.FC = () => (
  <PlaceholderScreen
    title="Debug & Logs 🔍"
    description="Advanced debugging and system logs"
    features={[
      'Real-time system logs',
      'Voice processing debug',
      'Intent parsing analysis',
      'Performance metrics',
      'Error tracking',
      'Export diagnostic data',
    ]}
  />
);

export const HelpAndTutorialScreen: React.FC = () => (
  <PlaceholderScreen
    title="Help & Tutorial 📚"
    description="Learn how to use JARVIS Lite effectively"
    features={[
      'Interactive tutorials',
      'Voice command examples',
      'Feature explanations',
      'Troubleshooting guide',
      'Privacy information',
      'Offline capabilities guide',
    ]}
  />
);

export const SystemStatusScreen: React.FC = () => (
  <PlaceholderScreen
    title="System Status 🔧"
    description="Monitor JARVIS Lite system health and performance"
    features={[
      'Component status monitoring',
      'Performance metrics',
      'Memory usage tracking',
      'Battery optimization',
      'Model health checks',
      'System diagnostics',
    ]}
  />
);

const localStyles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingVertical: 40,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  featuresContainer: {
    marginBottom: 40,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  featureBullet: {
    fontSize: 16,
    marginRight: 8,
    marginTop: 2,
    color: '#666',
  },
  featureText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  backButton: {
    alignSelf: 'center',
    paddingVertical: 16,
    paddingHorizontal: 32,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

// Export all screens as default exports for navigation
export default {
  ActivityTimelineScreen,
  SettingsScreen,
  AssistantPersonalityScreen,
  ReminderScheduleScreen,
  AutomationBuilderScreen,
  LearningInsightsScreen,
  DebugAndLogsScreen,
  HelpAndTutorialScreen,
  SystemStatusScreen,
};
