F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\PointerEventsConfig.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\RNGestureHandlerEvent.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\GestureHandler.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\ViewConfigurationHelper.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\eventbuilders\FlingGestureHandlerEventDataBuilder.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\RNGestureHandlerModule.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\eventbuilders\LongPressGestureHandlerEventDataBuilder.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\Vector.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\RotationGestureHandler.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\eventbuilders\PanGestureHandlerEventDataBuilder.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\ManualGestureHandler.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\Extensions.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\noreanimated\src\main\java\com\swmansion\gesturehandler\ReanimatedEventDispatcher.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\RNGestureHandlerRegistry.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\RNGestureHandlerRootViewManager.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\FlingGestureHandler.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\LongPressGestureHandler.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\GestureHandlerInteractionController.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\eventbuilders\RotationGestureHandlerEventDataBuilder.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\nosvg\src\main\java\com\swmansion\gesturehandler\RNSVGHitTester.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\StylusData.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\HoverGestureHandler.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\RNGestureHandlerRootHelper.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\DiagonalDirections.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\RNViewConfigurationHelper.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\TapGestureHandler.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\RNGestureHandlerButtonViewManager.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\PinchGestureHandler.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\OnTouchEventListener.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\RNGestureHandlerStateChangeEvent.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\eventbuilders\ManualGestureHandlerEventDataBuilder.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\RNGestureHandlerRootInterface.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\eventbuilders\NativeGestureHandlerEventDataBuilder.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\GestureUtils.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\eventbuilders\TapGestureHandlerEventDataBuilder.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\paper77\src\main\java\com\swmansion\gesturehandler\ReactContextExtensions.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\RNGestureHandlerTouchEvent.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\eventbuilders\GestureHandlerEventDataBuilder.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\NativeViewGestureHandler.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\GestureHandlerRegistry.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\PanGestureHandler.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\RNGestureHandlerRootView.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\common\src\main\java\com\swmansion\common\GestureHandlerStateManager.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\RNGestureHandlerInteractionManager.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\packageDeprecated\src\main\java\com\swmansion\gesturehandler\RNGestureHandlerPackage.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\RNGestureHandlerEnabledRootView.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\RotationGestureDetector.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\core\GestureHandlerOrchestrator.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\eventbuilders\HoverGestureHandlerEventDataBuilder.kt
F:\jarvis\node_modules\react-native-gesture-handler\android\src\main\java\com\swmansion\gesturehandler\react\eventbuilders\PinchGestureHandlerEventDataBuilder.kt