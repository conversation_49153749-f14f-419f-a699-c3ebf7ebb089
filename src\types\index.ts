/**
 * Core types and interfaces for JARVIS Lite
 */

// Voice Processing Types
export interface SpeechResult {
  text: string;
  isFinal: boolean;
  confidence?: number;
}

export interface VoiceProcessorError {
  code: string;
  message: string;
}

export enum VoiceProcessorState {
  IDLE = 'idle',
  LISTENING = 'listening',
  PROCESSING = 'processing',
  ERROR = 'error',
}

// Intent Processing Types
export interface IntentObject {
  intent: string;
  parameters: Record<string, any>;
  confidenceScore: number;
  originalText: string;
}

export interface IntentPattern {
  id: string;
  patterns: string[];
  intent: string;
  parameters?: string[];
  examples?: string[];
}

// Interaction Logging Types
export interface InteractionLog {
  id: string;
  timestamp: Date;
  intent: string;
  originalText: string;
  action: string;
  parameters?: Record<string, any>;
  duration?: number;
  context?: {
    timeOfDay: string;
    dayOfWeek: string;
    location?: string;
  };
}

// Learning and Memory Types
export interface LearnedPattern {
  id: string;
  pattern: string;
  confidence: number;
  frequency: number;
  lastSeen: Date;
  context: string[];
  suggestedAction?: string;
}

export interface UserHabit {
  id: string;
  description: string;
  trigger: string;
  action: string;
  confidence: number;
  frequency: number;
  timePattern?: string;
  isActive: boolean;
}

// Assistant Personality Types
export enum PersonalityMode {
  PROFESSIONAL = 'professional',
  PLAYFUL = 'playful',
  CALM = 'calm',
  JARVIS = 'jarvis',
}

export interface PersonalityConfig {
  mode: PersonalityMode;
  emojiAvatar: string;
  voiceProfile: string;
  customGreeting?: string;
  responseStyle: {
    useEmojis: boolean;
    formality: 'formal' | 'casual' | 'friendly';
    verbosity: 'concise' | 'normal' | 'detailed';
  };
}

// System Control Types
export interface SystemAction {
  type: 'app_launch' | 'system_control' | 'notification' | 'reminder' | 'automation';
  target: string;
  parameters?: Record<string, any>;
}

// Automation Types
export interface AutomationRule {
  id: string;
  name: string;
  description: string;
  trigger: AutomationTrigger;
  actions: AutomationAction[];
  isActive: boolean;
  createdAt: Date;
  lastTriggered?: Date;
}

export interface AutomationTrigger {
  type: 'time' | 'app' | 'device_state' | 'voice_command' | 'location';
  condition: Record<string, any>;
  operator?: 'AND' | 'OR';
}

export interface AutomationAction {
  type: 'launch_app' | 'speak' | 'notify' | 'system_toggle' | 'log';
  parameters: Record<string, any>;
}

// Reminder Types
export interface Reminder {
  id: string;
  title: string;
  notes?: string;
  scheduledTime: Date;
  isRecurring: boolean;
  recurringPattern?: 'daily' | 'weekly' | 'monthly' | 'custom';
  isVoiceEnabled: boolean;
  isCompleted: boolean;
  createdAt: Date;
}

// App Configuration Types
export interface AppConfig {
  isFirstLaunch: boolean;
  personality: PersonalityConfig;
  voiceSettings: {
    wakeWordEnabled: boolean;
    continuousListening: boolean;
    voiceEngine: 'vosk' | 'whisper';
    ttsEngine: 'coqui' | 'flite';
  };
  privacySettings: {
    trackingEnabled: boolean;
    learningEnabled: boolean;
    dataRetentionDays: number;
  };
  uiSettings: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    animations: boolean;
  };
}

// Navigation Types
export type RootStackParamList = {
  Splash: undefined;
  Onboarding: undefined;
  FirstTimeSetup: undefined;
  StartupGreeting: undefined;
  MainAssistant: undefined;
  ActivityTimeline: undefined;
  Settings: undefined;
  AssistantPersonality: undefined;
  ReminderSchedule: undefined;
  AutomationBuilder: undefined;
  AutomationTimeline: undefined;
  AutomationImportExport: undefined;
  LearningInsights: undefined;
  DebugAndLogs: undefined;
  HelpAndTutorial: undefined;
  SystemStatus: undefined;
};

// Event System Types
export interface AppEvent {
  type: string;
  payload?: any;
  timestamp: Date;
}

export type EventCallback = (event: AppEvent) => void;

// Database Schema Types
export interface DatabaseSchema {
  interactions: InteractionLog;
  patterns: LearnedPattern;
  habits: UserHabit;
  automations: AutomationRule;
  reminders: Reminder;
  config: AppConfig;
}

// LLM Types
export interface LLMResponse {
  text: string;
  confidence: number;
  tokens: number;
  processingTime: number;
}

export interface LLMConfig {
  modelName: string;
  modelPath: string;
  maxTokens: number;
  temperature: number;
  contextLength: number;
}

// TTS Types
export interface TTSConfig {
  engine: 'coqui' | 'flite' | 'system';
  voice: string;
  speed: number;
  pitch: number;
  volume: number;
}

// System Status Types
export interface SystemStatus {
  voiceEngine: {
    status: 'online' | 'offline' | 'error';
    lastActivity?: Date;
    micPermission: boolean;
  };
  ttsEngine: {
    status: 'online' | 'offline' | 'error';
    currentVoice: string;
  };
  llmEngine: {
    status: 'online' | 'offline' | 'loading' | 'error';
    modelLoaded: boolean;
    memoryUsage: number;
  };
  database: {
    status: 'connected' | 'error';
    lastBackup?: Date;
  };
  automations: {
    activeCount: number;
    nextTrigger?: Date;
  };
}

// Additional types for new features from prompt3.txt
export interface ReminderConfig {
  id: string;
  title: string;
  timestamp: string;
  repeat: 'none' | 'daily' | 'weekly' | 'monthly';
  ttsEnabled: boolean;
  isActive?: boolean;
}

export interface WakeWordSettings {
  enabled: boolean;
  sensitivity: number;
  keywords: string[];
  backgroundMode: boolean;
}

export interface BackgroundTaskConfig {
  id: string;
  name: string;
  schedule: string;
  isActive: boolean;
  lastRun?: Date;
}

export interface DailySummaryStats {
  date: string;
  totalInteractions: number;
  topCommands: Array<{ command: string; count: number }>;
  mostActiveHour: string;
  completedReminders: number;
  newPatternsLearned: number;
  productivityScore: number;
  insights: string[];
  recommendations: string[];
}

export interface LearningPattern {
  id: string;
  type: 'app' | 'reminder' | 'action';
  triggerTime?: string;
  triggerIntent?: string;
  resultAction: string;
  frequency: number;
  confidence: number;
}

// Command Routing Types
export interface CommandResult {
  success: boolean;
  message: string;
  action?: CommandAction;
}

export interface CommandAction {
  type: 'navigation' | 'system' | 'response';
  target?: string;
  parameters?: Record<string, any>;
}

// Voice Settings Types
export interface VoiceSettings {
  wakeWordEnabled: boolean;
  continuousListening: boolean;
  voiceEngine: 'vosk' | 'whisper';
  ttsEngine: 'coqui' | 'flite' | 'system';
}

// Privacy Settings Types
export interface PrivacySettings {
  trackingEnabled: boolean;
  learningEnabled: boolean;
  dataRetentionDays: number;
}

// UI Settings Types
export interface UISettings {
  theme: 'light' | 'dark' | 'system';
  language: string;
  animations: boolean;
}
