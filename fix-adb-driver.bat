@echo off
echo ========================================
echo Fix ADB Interface Driver
echo ========================================
echo.

echo ✅ GOOD NEWS: Your phone is detected!
echo I can see "ADB Interface" in Device Manager
echo The issue is just the driver status.
echo.

echo Step 1: Download and install Universal ADB Drivers
echo.

echo Manual method:
echo 1. Open Device Manager (devmgmt.msc)
echo 2. Look for "ADB Interface" with yellow warning
echo 3. Right-click → "Update driver"
echo 4. Choose "Browse my computer for drivers"
echo 5. Navigate to: C:\Program Files\platform-tools\
echo 6. Check "Include subfolders"
echo 7. Click "Next"
echo.

echo Automatic method:
echo Downloading universal ADB drivers...

echo.
echo Step 2: Alternative - Use Android Studio drivers
echo.

echo If you have Android Studio installed:
echo 1. Open Android Studio
echo 2. Go to Tools → SDK Manager
echo 3. Click "SDK Tools" tab
echo 4. Check "Google USB Driver"
echo 5. Click "Apply" to install
echo.

echo Step 3: Restart ADB after driver fix
adb kill-server
timeout /t 2 /nobreak >nul
adb start-server

echo.
echo Step 4: Test connection
echo.

echo Please do this on your phone NOW:
echo 1. Unplug USB cable
echo 2. Go to Settings → Developer options
echo 3. Turn OFF "USB debugging"
echo 4. Turn ON "USB debugging" again
echo 5. Plug USB cable back in
echo 6. When popup appears: "Allow USB debugging?"
echo 7. Check "Always allow from this computer"
echo 8. Tap "Allow"
echo.

echo Press any key after you've done the above steps...
pause

echo.
echo Testing connection...
adb devices

echo.
echo If you see your device listed as "device" (not "unauthorized"), you're ready!
echo.

echo ========================================
echo NEXT STEPS AFTER CONNECTION SUCCESS
echo ========================================
echo.

echo Once connected, you can:
echo.

echo Option 1: Install JARVIS Lite directly
echo   node "node_modules/@react-native-community/cli/build/bin.js" run-android
echo.

echo Option 2: Use Expo Go
echo   cd JarvisLiteExpo
echo   npx expo start
echo   Scan QR code with Expo Go app
echo.

echo Option 3: Install pre-built APK
echo   adb install -r jarvis-lite.apk
echo.

pause
