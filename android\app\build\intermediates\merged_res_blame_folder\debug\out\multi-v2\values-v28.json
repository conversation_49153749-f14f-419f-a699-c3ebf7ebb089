{"logs": [{"outputFile": "com.jarvislite.app-mergeDebugResources-29:/values-v28/values-v28.xml", "map": [{"source": "F:\\gradle-cache\\caches\\transforms-3\\c572b377194a8a385981f85402606c3e\\transformed\\material-1.6.1\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,248,343,441,529,617,705,793,880,967,1054", "endColumns": "97,94,94,97,87,87,87,87,86,86,86,86", "endOffsets": "148,243,338,436,524,612,700,788,875,962,1049,1136"}}, {"source": "F:\\gradle-cache\\caches\\transforms-3\\ab12076093e54ca9ba43148476352294\\transformed\\appcompat-1.4.2\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,8", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,447", "endLines": "2,3,7,11", "endColumns": "74,86,12,12", "endOffsets": "125,212,442,684"}, "to": {"startLines": "14,15,16,20", "startColumns": "4,4,4,4", "startOffsets": "1141,1216,1303,1483", "endLines": "14,15,19,23", "endColumns": "74,86,12,12", "endOffsets": "1211,1298,1478,1670"}}]}]}