Great! Since you're using **Augment Code**, I’ll give you a **detailed development prompt for each feature**, module-by-module, in a way <PERSON><PERSON> can understand and assist with. Each prompt will be specific, structured, and targeted for maximum AI code generation accuracy.

---

## 🧠 Phase 1: Voice Command Input (STT)

**🎯 Goal:** Capture voice input and convert it to text using an offline speech-to-text engine.

### 🔧 Prompt 1: `VoiceProcessor.ts`

```plaintext
You are developing a React Native module for Android that uses an offline STT engine like Vosk or Whisper.cpp. The goal is to implement a `VoiceProcessor` class that:

- Initializes the STT engine on app startup
- Handles a microphone recording stream
- Converts speech to text in real-time
- Emits events when voice input is complete or interrupted
- Supports continuous listening toggle and manual activation
- Has error fallback if mic permissions are denied

Make the module reusable and expose functions like `startListening()`, `stopListening()`, and `onResult(callback)`.
Ensure this is optimized for Android devices running 9+ and does not rely on internet access.
```

---

## 🧠 Phase 2: Intent Parser (understanding user commands)

### 🔧 Prompt 2: `IntentParser.ts`

```plaintext
Create a lightweight NLP-based IntentParser module in TypeScript for an Android AI assistant app. It should:

- Take raw text from speech input
- Match it to predefined intents like:
  - "open app", "turn on flashlight", "set reminder", "play music"
- Use pattern matching with a fallback to MiniLM (quantized) model for embeddings
- Return an `IntentObject` with:
  - `intent`: string
  - `parameters`: key-value pairs
  - `confidenceScore`: number

Implement it so it works offline without any API, and allow updating or training on new commands dynamically from stored examples.
```

---

## 🧠 Phase 3: Phone Controller (Android native actions)

### 🔧 Prompt 3: `SystemControlAndroid.ts`

```plaintext
Build a React Native bridge module (`SystemControlAndroid.ts`) that can execute native Android actions via shell commands or AccessibilityService. Features should include:

- Launch installed apps by package name
- Turn flashlight on/off
- Adjust volume and brightness
- Toggle Wi-Fi, Bluetooth (if permissions allow)
- Open camera, gallery, contacts
- Make phone call to a given number

Include fallback handlers if permissions are missing and ensure proper permission prompts are triggered. Target Android 9+ and avoid using internet.
```

---

## 🧠 Phase 4: TTS Voice Output

### 🔧 Prompt 4: `OfflineTTS.ts`

```plaintext
Create a module that performs offline text-to-speech using Coqui TTS or Flite for Android inside React Native. It should:

- Initialize the TTS engine on load
- Accept a string and speak it aloud
- Support multiple voice options (if available)
- Expose `speak(text: string)` and `stop()` functions
- Allow voice speed and pitch adjustment
- Fallback gracefully if engine not available

No internet connection or cloud TTS usage allowed. Use system storage for preloaded voice files.
```

---

## 🧠 Phase 5: Logging User Interaction

### 🔧 Prompt 5: `InteractionLogger.ts`

```plaintext
Create a module that logs all user interactions locally in SQLite. It should record:

- Timestamp
- Intent name (from IntentParser)
- App or system action taken
- Duration (if applicable)
- Context (e.g., time of day, location if permitted)

Expose functions:
- `logInteraction(intent, action, meta)`
- `getLogs(filter: IntentFilter)`
- `clearLogs()`

Make this encrypted and offline only. Build functions to analyze frequent actions and export logs to JSON.
```

---

## 🧠 Phase 6: Local Memory + Self Learning

### 🔧 Prompt 6: `LocalMemory.ts`

```plaintext
Develop a self-learning module that analyzes the user’s past interactions (from SQLite logs) to find patterns and habits. It should:

- Use lightweight algorithms (KNN, Decision Trees)
- Learn user behavior like:
  - “Open music app every morning”
  - “User uses flashlight at night”
- Provide suggestions (e.g., “Do you want me to open music now?”)
- Save learned rules in a separate table
- Expose functions:
  - `analyzePatterns()`
  - `getSuggestions(currentContext)`
  - `updateModelWithNewInteraction(logEntry)`

Make this 100% offline and self-contained.
```

---

## 🧠 Phase 7: Dialogue LLM Core (JARVIS personality)

### 🔧 Prompt 7: `LLMEngine.ts`

```plaintext
Create an on-device LLM chat engine wrapper using GGUF quantized models like TinyLLaMA or GPT4All. The engine should:

- Load local 4-bit quantized model using llama.cpp backend
- Provide a `chat(prompt: string)` function with streaming response
- Maintain short conversation history
- Inject user context (usage logs, memory)
- Run inference within RAM constraints (< 1GB)

Ensure this runs offline using local model files bundled with the app. Include instructions to update the model later if needed.
```

---

## 🧠 Phase 8: UI Screens

I’ll generate these as detailed prompts next:

* `MainAssistantScreen`: UI with mic button, response area, suggestions
* `ActivityTimelineScreen`: Daily activity chart, logs, app usage
* `SettingsScreen`: Wake word toggle, model selection, privacy

Would you like to proceed with the **UI prompts next** or continue adding **advanced modules like reminders, schedule triggers, or offline automation builder**?

Let’s build JARVIS Lite layer by layer 💡.

Create a visually clean and responsive `MainAssistantScreen` component in React Native for an Android app. This is the central interface for an offline AI assistant.

Design the following UI layout:

1. **Floating Mic Button**
   - Positioned bottom-center or bottom-right.
   - Tappable to start/stop voice input.
   - Changes state (recording/not recording) with glow animation or color change.

2. **Live Response Bubble**
   - Displays the assistant's response as text after each voice command.
   - Includes a subtle typing animation (e.g., 3 bouncing dots) while waiting.
   - Optional: Text-to-speech playback toggle for the response.

3. **Recent Command History**
   - Scrollable vertical list (flatlist) showing:
     - Time
     - Command spoken
     - Intent matched
     - Icon for action (e.g., 📱 for app launch, 💡 for flashlight)

4. **Quick Action Chips**
   - Row of horizontally scrollable action shortcuts like:
     - “📷 Open Camera”
     - “💡 Toggle Torch”
     - “🎶 Play Music”
   - Each chip triggers a predefined intent.

5. **Assistant Avatar or Emoji**
   - Optional animated avatar or rotating assistant emoji at top-center (for personality).

6. **Dark Mode Friendly**
   - Use styles that adapt to system dark/light mode.
   - High contrast colors for clarity.

7. **No Internet Needed Notice**
   - Subtle banner at bottom: “JARVIS Lite is running offline 🔒”

Add animated transitions between speaking, responding, and idle states.

Use modular component structure:
- `FloatingMicButton.tsx`
- `ResponseBubble.tsx`
- `CommandHistoryList.tsx`
- `QuickActionChips.tsx`

Keep all components mobile-optimized, smooth, and battery-efficient.

Design a UI screen named `ActivityTimelineScreen.tsx` in React Native for an offline Android AI assistant. This screen visually presents user interaction logs, usage patterns, and assistant-suggested habits.

### 🧱 Layout Structure:

1. **📅 Date Header**
   - Sticky date selector or header (e.g., “Today”, “Yesterday”, or calendar picker).
   - Shows logs grouped by selected date.

2. **📈 Usage Summary Card**
   - Top card showing:
     - Total interactions today
     - Top 3 most used commands
     - Assistant actions taken automatically (if any)
   - Use icons and subtle graphs (e.g., mini bar chart or pie)

3. **📜 Scrollable Timeline View**
   - Vertical list of interaction events with timestamp.
   - Each item contains:
     - 🕐 Time
     - 🗣 User Command (spoken text)
     - 🧠 Parsed Intent
     - 🎯 Action Taken (e.g., “Opened Camera”)
     - 🧩 Icon for the type of action (🎵, 📷, 🔊)

4. **🧠 Pattern Detected Alerts**
   - Optional card: “Looks like you often open YouTube at 9 PM — automate this?”
   - Tapable to accept/reject or adjust.

5. **🔎 Filter Button (Top-Right)**
   - Dropdown to filter by intent types:
     - All, App Launch, Utilities, Reminders, Unknown

6. **Dark Mode Ready**
   - Use theme-aware styles for cards, lists, fonts

7. **Offline-Only Banner**
   - Show a badge/icon: “All data stored securely on device 🔐”

### 🧩 Modular Component Suggestions:
- `UsageSummaryCard.tsx`
- `InteractionLogItem.tsx`
- `PatternSuggestionCard.tsx`
- `DateSelector.tsx`

Make the screen:
- Scrollable and responsive
- Snappy with animation when new events are added
- Optimized for long sessions (FlatList with lazy load)

Connect this screen to the local interaction logs stored via SQLite or AsyncStorage.

Create a UI screen named `SettingsScreen.tsx` for a local Android AI assistant built in React Native. This screen allows users to customize the assistant's functionality and privacy preferences.

### 🧱 Layout Structure:

1. **🟢 Wake Word Toggle**
   - Switch to enable/disable background wake word detection.
   - Show real-time status indicator: “Active 🟢” or “Disabled 🔴”
   - Tooltip or info icon: “Wake word allows voice activation when the app is idle.”

2. **🔇 Privacy Mode Toggle**
   - “Do not track interactions” mode.
   - Disables all interaction logging and learning.
   - Add lock icon: “🔒 Privacy Mode On”
   - Warning when turning it off again: “Past data will not be restored.”

3. **🎤 Voice Model Selector**
   - Dropdown or horizontal scroll of available voices (e.g., "Male", "Female", "Jarvis", "Calm")
   - Preview button for each voice: “🔊 Play sample”
   - Option to download more voices from local files (no cloud)

4. **🧠 Model Configuration**
   - Show which LLM is active (e.g., "Phi-2", "TinyLLaMA", etc.)
   - Display:
     - Model name
     - Size (e.g., 250MB)
     - RAM usage estimate
   - Allow user to switch or delete old models

5. **⏲ Learning Reset**
   - Button: “Clear Memory and Learning”
   - Prompts confirmation modal: “This will wipe all local insights and usage logs.”
   - On success, show toast: “Assistant memory reset.”

6. **🌙 Theme Switch**
   - Toggle dark/light mode manually
   - Option to follow system theme (default)

7. **🔒 Local Data Storage Info**
   - Small footer section: “All your data is stored securely on your device. No cloud sync. No ads.”

---

### 🧩 Component Suggestions:
- `ToggleItem.tsx` (reusable switch row)
- `VoiceSelector.tsx`
- `ModelCard.tsx`
- `ResetConfirmationModal.tsx`
- `StorageInfoBanner.tsx`

Style the screen using native Android design guidelines, with:
- Smooth transitions for toggles
- Animated confirmation prompts
- Toasts/snackbars for settings saved

All actions must affect **local configuration only** (no internet or cloud).

Design a UI screen named `AssistantPersonalityScreen.tsx` in React Native that lets users customize the personality, tone, and appearance of their offline AI assistant (JARVIS Lite).

### 🧱 Layout Structure:

1. **🧠 Personality Mode Selector**
   - Horizontally scrollable cards or radio buttons with predefined modes:
     - 💼 Professional (Concise, formal)
     - 🎉 Playful (Casual, emoji-heavy)
     - 🧘 Calm (Soothing, minimal)
     - 🦾 Jarvis (Witty, semi-formal, techy tone)
   - Selecting a mode updates stored preferences in local memory.
   - Optionally preview sample reply (e.g., “How can I help you today?”)

2. **🎭 Emoji Avatar Picker**
   - Let users pick or change the assistant’s avatar emoji:
     - 🤖, 🧠, 🐱, 👾, 🎩, 🐉, etc.
   - Show selected emoji as avatar on Main screen.

3. **🎤 Voice Personality Toggle**
   - If multiple voices are available, show pairs matched to the personality:
     - Calm voice, energetic voice, robotic voice, etc.
   - Play sample per selection.

4. **📝 Custom Catchphrase Input**
   - Text input: “Give your assistant a custom greeting or catchphrase.”
   - Example: “Let’s do this, Commander!” → shown on startup.

5. **📦 Save Settings**
   - Button at bottom: “Save Personality”
   - Confirmation toast: “Assistant personality updated.”

6. **🌙 Visual Style Preview**
   - Optional mini preview of how text responses will look under this personality (emoji use, tone, color variation in response bubble).

---

### 🧩 Suggested Components:
- `PersonalityCard.tsx`
- `EmojiPicker.tsx`
- `VoicePersonalityPlayer.tsx`
- `CustomGreetingInput.tsx`
- `PreviewResponseBubble.tsx`

Use local storage (AsyncStorage or SQLite) to persist:
- `selectedPersonality`
- `emojiAvatar`
- `voiceProfile`
- `customGreeting`

All personality adjustments should affect:
- How responses are formatted
- Which emojis/voice tones are used
- Optional personality-specific behaviors (e.g., Jarvis mode shows tips)

Design a React Native screen named `ReminderScheduleScreen.tsx` for an offline Android AI assistant. This screen allows users to create, view, manage, and trigger reminders without cloud or internet.

### 🧱 Layout Structure:

1. **➕ Add New Reminder Button**
   - FAB (floating action button) or top-right "+" icon
   - Opens modal or new screen with:
     - Text input (reminder title)
     - Optional notes
     - Date/time picker
     - Repeat frequency: None, Daily, Weekly, Custom
     - Optional voice reminder toggle (assistant will speak reminder)

2. **📜 Reminders List**
   - Scrollable list of all saved reminders.
   - Each item shows:
     - 🕐 Time
     - 📋 Title
     - 🔁 Repeat info
     - ✅ Mark as done / ⏰ Reschedule
   - Icons for:
     - 🔈 Voice reminder
     - 📅 Repeating task
   - Swipe left/right to delete or snooze.

3. **📂 Filter/Sort Controls**
   - Filter: Today, Upcoming, All, Completed
   - Sort: Time, Title, Created On

4. **🔁 Missed Reminder Recovery**
   - Show badge for missed reminders (offline only)
   - Option to auto-reschedule missed ones (based on last pattern)

5. **🔔 Local Notification Handler**
   - Schedule reminders using Android’s local alarm manager or `react-native-push-notification`.
   - Reminder triggers should fire even if app is in background or device is offline.
   - Voice reminders should trigger with text-to-speech output if enabled.

6. **🗂 Persistent Offline Storage**
   - Save reminders in SQLite (with fields: title, time, notes, isRecurring, ttsEnabled, createdAt, etc.)
   - Ensure expired/completed reminders are archived, not deleted permanently.

---

### 🧩 Suggested Components:
- `ReminderCard.tsx`
- `NewReminderModal.tsx`
- `ReminderFilterChips.tsx`
- `RepeatSelector.tsx`

Use animated transitions for opening/closing reminders and confirmation prompts.

Voice commands like:
> “Remind me to drink water at 8 PM”  
should also route to this screen and store reminder automatically.

Design a UI screen called `AutomationBuilderScreen.tsx` in React Native for an Android offline AI assistant. This screen allows users to create and manage “if-this-then-that” style automations that work fully offline without APIs.

### 🧱 Layout Structure:

1. **➕ Add New Automation**
   - FAB or top-right “+” icon
   - Opens modal or dedicated builder view with:
     - “IF” section: Choose a trigger
     - “THEN” section: Choose an action
     - Save & Activate toggle

2. **📥 IF (Trigger Conditions)**
   - Dropdown or chips for common triggers:
     - Time-based: At 9:00 AM / After sunrise
     - App-based: When WhatsApp is opened
     - Device state: Wi-Fi connected, charger plugged
     - Location-based: Arriving at Home (optional)
     - Custom voice command detected
   - Optional condition chaining: AND / OR logic

3. **📤 THEN (Actions to Perform)**
   - Action menu with:
     - Launch app
     - Speak a message via TTS
     - Send a notification
     - Toggle flashlight, Wi-Fi, Bluetooth
     - Play a sound
     - Log a message
   - Support for multiple chained actions per rule

4. **📜 List of Existing Automations**
   - Scrollable list showing:
     - Automation title
     - Summary: “When charger plugged → Play welcome sound”
     - Status: Active/Inactive toggle
     - Edit / Delete icons

5. **📎 Smart Suggestions Section (Optional)**
   - “You usually open YouTube at 8 PM — create auto-launch?”
   - Tap to auto-fill automation builder

6. **📂 Categories / Folders**
   - Let users organize automations by purpose:
     - Routine, Device, Apps, Reminders

7. **🔒 Works Offline**
   - Use local SQLite or JSON to store automation definitions
   - Use background service or periodic checks to run conditions
   - Leverage Android's `BroadcastReceiver`, `AlarmManager`, and file observers

---

### 🧩 Component Suggestions:
- `AutomationCard.tsx`
- `AutomationBuilderModal.tsx`
- `TriggerConditionSelector.tsx`
- `ActionBlock.tsx`
- `SmartSuggestionBanner.tsx`

Animations: Slide-in modals, success toast “Automation Activated ✅”, undo on delete

Accessibility: All automations must be functional even if the device has no data connection. No cloud sync.

Voice Command Integration:
> If the user says: “Make my phone say ‘Welcome’ when I plug in charger,” route this to the automation builder.

Create a UI screen called `LearningInsightsScreen.tsx` in React Native for an Android-based offline AI assistant. This screen displays insights into how the assistant is learning from user behavior over time, without sending any data to the cloud.

### 🧱 Layout Structure:

1. **📊 Weekly Pattern Summary**
   - Card showing:
     - “Most used app this week: YouTube”
     - “Common command: Turn on flashlight”
     - “Peak interaction time: 9–11 PM”
   - Display with icons, bar charts, or text highlights

2. **🧠 Learned Rules / Habits**
   - Scrollable list of patterns the assistant has detected, like:
     - “You open Spotify after 8 PM on weekdays.”
     - “You turn on the flashlight every night.”
     - “You use WhatsApp more on weekends.”
   - Each rule should include:
     - Rule text summary
     - Confidence score
     - Toggle to enable/disable auto-action suggestion
     - Option to convert it into an automation rule

3. **🌀 Learning Graph**
   - Minimal graph or donut chart showing:
     - How many interactions per category (Apps, Reminders, Commands, etc.)
     - Growth of assistant memory over time

4. **🔁 Feedback Controls**
   - “Is this insight correct?” → 👍 / 👎
   - “Forget this pattern” → Deletes that insight from memory

5. **🗂 Learning Log View**
   - Collapsible timeline showing key learning checkpoints:
     - When the assistant updated a habit
     - When it created or suggested an automation

6. **🧩 Raw Insight Export (Optional)**
   - Button: “Export Learnings as JSON”
   - Useful for offline debugging or showing the user what’s stored

7. **🔐 Privacy Assurance Banner**
   - Bottom note: “These learnings are stored only on your device and are never sent online. You control what I remember.”

---

### 🧩 Suggested Components:
- `WeeklySummaryCard.tsx`
- `LearnedRuleItem.tsx`
- `LearningGraph.tsx`
- `FeedbackChip.tsx`
- `InsightLogItem.tsx`

Design should feel like an interactive dashboard, using soft shadows, neutral colors, and rounded cards.

Optional: If using LLMs locally, allow the assistant to describe patterns in natural language:  
> “I noticed you prefer using music apps at night. Shall I remind you tomorrow?”

Build a screen named `DebugAndLogsScreen.tsx` in React Native for an offline Android AI assistant app. This screen is meant for developers or advanced users to inspect internal logs, triggers, models, and assistant behaviors in real-time.

### 🧱 Layout Structure:

1. **🧾 Real-Time Command Log Viewer**
   - Scrollable terminal-style log feed (FlatList or ScrollView)
   - Log entry structure:
     - Timestamp
     - Type: Command | System | Error
     - Message content (e.g., “Intent matched: open_camera”)
   - Log entries should have color-coded tags:
     - 🟢 Info, 🟡 Warning, 🔴 Error

2. **🔍 Filter Bar**
   - Filter logs by type:
     - [ ] All
     - [ ] Intent
     - [ ] Model
     - [ ] STT
     - [ ] TTS
     - [ ] Storage
   - Toggle buttons or checkboxes

3. **📥 Raw Data Viewer**
   - Section to view raw SQLite logs or assistant memory as JSON.
   - Buttons:
     - “View Interaction Logs”
     - “View Learned Patterns”
     - “Open Current Config”

4. **📤 Export / Clear Logs**
   - Button: “Export All Logs (.txt or .json)” → Saves locally or shares via Android intent
   - Button: “Clear Logs” → Wipes logs after confirmation

5. **📦 Model Info Section**
   - Card with:
     - Current model: Name, size, tokens loaded
     - Status: Running | Idle | Error
     - Memory usage estimation
     - Token output stats (last response length)

6. **⚙️ Test Trigger Tool**
   - Manually simulate events:
     - Run STT on test string
     - Parse intent manually
     - Trigger action like `toggleTorch()`
   - Useful for debugging flow in real-time

7. **🔄 Assistant Restart**
   - Button: “Restart Core Engine”
   - Stops and reinitializes modules (STT, TTS, LLM)

---

### 🧩 Suggested Components:
- `LogEntryItem.tsx`
- `RawJsonViewer.tsx`
- `ModelInfoCard.tsx`
- `TestTriggerPanel.tsx`

Display should use monospace font for logs, support dark mode, and allow expanding/collapsing log entries.

Use native performance-optimized methods to read log files or SQLite tables and display safely without crashing the UI for long files.

**Only accessible in developer mode or with a long-press gesture from the main screen.**
Design a screen named `HelpAndTutorialScreen.tsx` in React Native for an offline Android AI assistant. This screen provides onboarding steps, feature explanations, voice command examples, and usage tips — all accessible without internet.

### 🧱 Layout Structure:

1. **🎬 Getting Started Guide (Top Section)**
   - Card or expandable section:
     - “How to activate voice input”
     - “How to give your first command”
     - “How to create a reminder or automation”
   - Visual icons and brief text
   - Optional step-by-step progress bar for onboarding checklist

2. **🎤 Voice Command Examples**
   - Categorized list with sample phrases:
     - 🧭 Navigation:
       - “Open camera”
       - “Turn on flashlight”
     - 🗓 Reminders:
       - “Remind me to call dad at 7 PM”
     - 🎵 Media:
       - “Play my music”
     - 🧠 Learning:
       - “What did I do yesterday?”
   - Tappable list — shows what the assistant would interpret (intent + action)

3. **📚 Features Library**
   - Expandable cards or list items explaining:
     - Offline Voice Recognition
     - Self-Learning Engine
     - Local Storage & Privacy
     - Assistant Personality Modes
     - Automation Builder
   - Icons + short readable descriptions
   - Toggle: “Hide Advanced Features”

4. **🎨 Assistant Personality Tips**
   - Small section on how to personalize the assistant
   - Suggest combos like:
     - Emoji Avatar: 👾
     - Tone: Playful
     - Greeting: “What’s cooking, boss?”

5. **🔐 Privacy & Offline Mode FAQ**
   - Answer questions like:
     - “Is anything uploaded online?” → No
     - “Where is my data stored?” → Device only
     - “Can I delete my history?” → Yes, from Settings

6. **📹 Video / Animation Placeholder**
   - Space to later embed onboarding video or looped animation (local asset only, not streamed)

7. **❓Need Help Panel**
   - Last card: “Need Help?”
     - Option to open `DebugAndLogsScreen` (dev mode)
     - Export logs if needed
     - Contact placeholder (no internet used)

---

### 🧩 Suggested Components:
- `TutorialStepCard.tsx`
- `VoiceCommandExamples.tsx`
- `FeatureAccordion.tsx`
- `FAQItem.tsx`

Style:
- Minimal, light and dark mode ready
- Clear section dividers
- High readability
- Suitable for users of all ages

Make sure all data and assets are **pre-bundled in the app**, and absolutely no web calls are made.

Create a screen named `SystemStatusScreen.tsx` in React Native for an offline Android AI assistant app. This screen displays the current health and diagnostic status of all major assistant components (voice, LLM, automation, etc.).

### 🧱 Layout Structure:

1. **🧠 Assistant Core Status Card**
   - Status: ✅ Online | 🔴 Offline | 🟡 Initializing
   - Sub-statuses:
     - Last command processed: [timestamp]
     - LLM Ready: Yes/No
     - Memory: Active / Cleared
   - Restart Core Engine → [Restart Button]

2. **🎤 Voice Engine Status**
   - STT Engine: Whisper / Vosk
   - Listening Mode: Idle | Recording | Wake Active
   - Microphone Permission: ✅ Granted / ❌ Denied
   - Wake Word Enabled: On/Off

3. **🗣 TTS Voice System**
   - TTS Engine: Coqui / Flite
   - Current voice profile: Jarvis / Calm / Robotic
   - Last spoken phrase: [log or preview]
   - TTS Health: ✅ OK | ⚠️ Delayed | ❌ Broken

4. **🧾 Logging & Learning**
   - Interaction Logger: ✅ Running
   - Logged entries today: [count]
   - Learning Engine: Active / Paused
   - Last pattern learned: [summary]

5. **🔁 Automation Engine**
   - Total automations: [#]
   - Active: [#] | Inactive: [#]
   - Next trigger scheduled: [timestamp] or “None”
   - Test Trigger: [Button to simulate one]

6. **📦 Model Engine Info**
   - Model: TinyLLaMA / GPT4All / Phi-2
   - Quantization: 4-bit GGUF
   - Inference time avg: [ms/token]
   - Model file size: [MB]
   - RAM used: [est MB]

7. **🔋 Battery & Background Info**
   - Battery level: [%]
   - Background Mode: Allowed / Restricted
   - Data Saver: On/Off
   - Android Doze Mode: Active/Not Active

8. **📤 Export System Report**
   - Button: “Export Diagnostics (.json)”
   - Saves to local storage or shares via Android intent

---

### 🧩 Component Suggestions:
- `StatusCard.tsx`
- `VoiceEngineMonitor.tsx`
- `ModelHealthCard.tsx`
- `RestartEngineButton.tsx`
- `ExportReportButton.tsx`

Use icons, color codes (green/yellow/red), and soft UI transitions. Keep the layout modular and scrollable. Group by core system area for clarity.

Optional:
- Use collapsible sections
- Show blinking indicator for modules currently processing

Create a screen called `FirstTimeSetupScreen.tsx` in React Native. This screen should function as an onboarding wizard for an offline Android AI assistant. It appears only during first launch or after a full reset.

### 🧱 Layout: Step-by-Step Wizard

Design it as a paginated onboarding flow (carousel or step progress).

---

### 📍 Step 1: Welcome Screen

- Title: “Welcome to JARVIS Lite 🔧”
- Brief intro:
  - “Your personal, offline AI assistant.”
  - “No internet. No cloud. All on your phone.”
- Button: “Get Started”

---

### 🎤 Step 2: Microphone Permission

- Ask for mic permission:
  - Explain: “Used for voice commands only. No internet access or recordings saved.”
- Button: `Request Microphone Access`
- Show ✅ or ❌ based on current permission

---

### 🗣 Step 3: Choose Voice + Personality

- Voice options: Calm, Jarvis, Robotic (sample button for each)
- Emoji avatar picker: 🤖, 🧠, 👾, 🐱, 🎩
- Tone mode picker:
  - 🎩 Jarvis (witty)
  - 🧘 Calm (soft)
  - 💼 Professional (formal)
  - 🎉 Playful (casual)

> Preview personality style dynamically in a response bubble:  
> “I’m ready to serve, Commander 👾”

---

### 🔄 Step 4: Default Automations (Optional)

- Offer to auto-enable helpful defaults:
  - “Auto-launch YouTube at 8 PM?”
  - “Remind you to hydrate every 3 hours?”
  - User can toggle On/Off for each

---

### 🔐 Step 5: Privacy & Consent

- Show: “This assistant works 100% offline.”
- Explain:
  - No internet needed
  - No data is ever uploaded
  - Logs are stored securely on-device
- Checkbox: ✅ “I agree to use offline assistant features”
- Button: “Continue”

---

### ✅ Final Step: Assistant Ready

- Animation: JARVIS Lite face, glow, or emoji
- Text: “Setup complete! Tap the mic to begin.”

- Button: “Start Using JARVIS Lite”

---

### Additional Details:

- Save progress in local state so setup can resume if interrupted
- Use `AsyncStorage` or SQLite to store `isFirstLaunch = false`
- Modular screen: consider `SetupStep1.tsx`, `SetupStep2.tsx`, etc.
- Disable swipe to skip steps

Create a screen named `StartupGreetingScreen.tsx` in React Native. This screen is shown immediately on app launch (after first-time setup is complete) and displays a warm, personalized greeting from the offline assistant.

### 🧱 Layout Structure:

1. **👋 Greeting Text (Dynamic)**
   - Based on time of day:
     - “Good morning, Commander 👾”
     - “Welcome back! Ready for action?”
   - Optional use of emoji avatar from personality setup
   - Greeting tone adjusts based on selected assistant personality

2. **📅 Daily Summary (Optional Section)**
   - “You have 2 reminders today.”
   - “It’s been 3 days since your last automation.”
   - “Would you like to hear your routine?” [Button: Play Summary]

3. **🔄 Motivational Quote / Tip of the Day**
   - Rotate local quotes daily from stored JSON or SQLite:
     - “Discipline is choosing between what you want now and what you want most.”
     - “Small routines make big results.”
   - Display quote author (optional)

4. **🎨 Background Animation or Theme**
   - Simple gradient fade or animated particles
   - Changes based on time or selected theme (e.g., dark/calm vs bright/energetic)

5. **🟢 Continue Button**
   - “Let’s Begin” or “Enter Dashboard”
   - Tapping transitions to `MainAssistantScreen`

6. **🔁 Auto-skip Logic**
   - After 5 seconds, automatically transition unless user taps or holds screen
   - Option in Settings to disable this screen on future launches

---

### 🧩 Component Suggestions:
- `GreetingText.tsx`
- `DailySummaryCard.tsx`
- `QuoteOfTheDay.tsx`
- `EnterButton.tsx`

Persistent Data Sources:
- Pull name/avatar/personality from local config
- Load quotes from offline JSON file or SQLite
- Calculate time-based greeting on the fly

All assets and animations should be local (no internet required).

