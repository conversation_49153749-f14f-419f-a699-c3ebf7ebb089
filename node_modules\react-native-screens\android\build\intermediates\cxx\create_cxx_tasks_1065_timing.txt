# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 11ms]
      create-cmake-model 64ms
    create-module-model completed in 77ms
    create-module-model
      create-cmake-model 67ms
    create-module-model completed in 73ms
    create-ARMEABI_V7A-model 10ms
    create-X86-model 13ms
    create-X86_64-model 29ms
  create-initial-cxx-model completed in 249ms
  [gap of 56ms]
create_cxx_tasks completed in 306ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 75ms
    create-module-model completed in 81ms
    create-ARMEABI_V7A-model 18ms
    create-module-model
      create-cmake-model 62ms
    create-module-model completed in 70ms
    [gap of 33ms]
  create-initial-cxx-model completed in 225ms
  [gap of 14ms]
create_cxx_tasks completed in 239ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 75ms
    create-module-model completed in 83ms
    create-module-model
      create-cmake-model 141ms
    create-module-model completed in 148ms
    [gap of 38ms]
  create-initial-cxx-model completed in 299ms
create_cxx_tasks completed in 302ms

