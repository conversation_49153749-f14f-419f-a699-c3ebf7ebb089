/**
 * IntentParser.ts
 * Lightweight NLP-based intent parsing for offline AI assistant
 * Uses rule-based pattern matching with keyword fallback
 */

import { IntentObject, IntentPattern } from '@/types';

interface IntentMatch {
  intent: string;
  confidence: number;
  parameters: Record<string, any>;
}

// Hardcoded keyword rules — offline and fast
const intentRules: { pattern: RegExp; intent: string }[] = [
  { pattern: /\b(flashlight|torch)\b.*\b(on|off|toggle)?\b/, intent: 'TOGGLE_FLASHLIGHT' },
  { pattern: /\b(open|launch)\b.*\b(camera)\b/, intent: 'OPEN_APP_CAMERA' },
  { pattern: /\b(open|launch)\b.*\b(gallery|photos)\b/, intent: 'OPEN_APP_GALLERY' },
  { pattern: /\b(remind|reminder|alert)\b.*\b(to|me|at)\b/, intent: 'SET_REMINDER' },
  { pattern: /\b(play|start)\b.*\b(music|song|playlist)\b/, intent: 'PLAY_MUSIC' },
  { pattern: /\b(call|ring|dial)\b.*\b([a-zA-Z0-9 ]+)\b/, intent: 'MAKE_CALL' },
  { pattern: /\b(brightness)\b.*\b(up|down|increase|decrease)?\b/, intent: 'ADJUST_BRIGHTNESS' },
  { pattern: /\b(wifi|bluetooth)\b.*\b(on|off|toggle)?\b/, intent: 'TOGGLE_SETTING' },
  { pattern: /\b(note|write|remember)\b.*\b(that|this|to)\b/, intent: 'TAKE_NOTE' },
  { pattern: /\b(volume)\b.*\b(up|down|increase|decrease)?\b/, intent: 'ADJUST_VOLUME' },
  { pattern: /\b(time)\b/, intent: 'GET_TIME' },
  { pattern: /\b(date)\b/, intent: 'GET_DATE' },
  { pattern: /\b(summary|report|daily)\b.*\b(summary|report|today)?\b/, intent: 'DAILY_SUMMARY' },
  { pattern: /\b(wake|hey|ok)\b.*\b(jarvis)\b/, intent: 'WAKE_WORD' },
  { pattern: /\b(stop|quit|exit)\b.*\b(listening|jarvis)?\b/, intent: 'STOP_LISTENING' },
  { pattern: /\b(show|list|view)\b.*\b(rules|automations)\b/, intent: 'SHOW_RULES' },
  { pattern: /\b(import|load)\b.*\b(config|rules|automations)\b/, intent: 'IMPORT_CONFIG' },
  { pattern: /\b(delete|remove)\b.*\b(rule|automation)\b.*\b(at|time)\b.*(\d{1,2}:\d{2}|\d{1,2}\s*(am|pm))/i, intent: 'DELETE_RULE_AT_TIME' },
  { pattern: /\b(what|list|show)\b.*\b(runs|scheduled|automation)\b.*\b(at|time)\b.*(\d{1,2}:\d{2}|\d{1,2}\s*(am|pm))/i, intent: 'QUERY_RULES_AT_TIME' },
  { pattern: /\b(how many tasks|how many rules|tasks today|scheduled today)\b/, intent: 'COUNT_TODAYS_TASKS' },
  { pattern: /\b(open settings|settings menu)\b/, intent: 'OPEN_SETTINGS' },
];

class IntentParser {
  private patterns: IntentPattern[] = [];
  private isInitialized = false;

  constructor() {
    this.initializePatterns();
  }

  /**
   * Initialize predefined intent patterns
   */
  private initializePatterns(): void {
    this.patterns = [
      // App Launch Intents
      {
        id: 'open_app',
        patterns: [
          'open {app}',
          'launch {app}',
          'start {app}',
          'run {app}',
          'open the {app}',
          'launch the {app} app'
        ],
        intent: 'open_app',
        parameters: ['app'],
        examples: ['open camera', 'launch youtube', 'start spotify']
      },

      // System Control Intents
      {
        id: 'toggle_flashlight',
        patterns: [
          'turn on flashlight',
          'turn off flashlight',
          'toggle flashlight',
          'flashlight on',
          'flashlight off',
          'turn on torch',
          'turn off torch'
        ],
        intent: 'toggle_flashlight',
        examples: ['turn on flashlight', 'flashlight off']
      },

      {
        id: 'adjust_volume',
        patterns: [
          'volume up',
          'volume down',
          'increase volume',
          'decrease volume',
          'turn up volume',
          'turn down volume',
          'set volume to {level}',
          'volume {level}'
        ],
        intent: 'adjust_volume',
        parameters: ['level', 'direction'],
        examples: ['volume up', 'set volume to 50']
      },

      {
        id: 'toggle_wifi',
        patterns: [
          'turn on wifi',
          'turn off wifi',
          'enable wifi',
          'disable wifi',
          'wifi on',
          'wifi off'
        ],
        intent: 'toggle_wifi',
        examples: ['turn on wifi', 'disable wifi']
      },

      // Communication Intents
      {
        id: 'make_call',
        patterns: [
          'call {contact}',
          'phone {contact}',
          'dial {contact}',
          'call {number}',
          'phone {number}'
        ],
        intent: 'make_call',
        parameters: ['contact', 'number'],
        examples: ['call john', 'phone 555-1234']
      },

      // Reminder Intents
      {
        id: 'create_reminder',
        patterns: [
          'remind me to {task}',
          'remind me to {task} at {time}',
          'set reminder for {task}',
          'reminder {task} at {time}',
          'remind me {task} in {duration}'
        ],
        intent: 'create_reminder',
        parameters: ['task', 'time', 'duration'],
        examples: ['remind me to call mom at 5pm', 'remind me to drink water in 1 hour']
      },

      // Information Intents
      {
        id: 'get_time',
        patterns: [
          'what time is it',
          'current time',
          'tell me the time',
          'what\'s the time'
        ],
        intent: 'get_time',
        examples: ['what time is it', 'current time']
      },

      {
        id: 'get_date',
        patterns: [
          'what date is it',
          'today\'s date',
          'what day is it',
          'current date'
        ],
        intent: 'get_date',
        examples: ['what date is it', 'today\'s date']
      },

      // Assistant Control Intents
      {
        id: 'stop_listening',
        patterns: [
          'stop listening',
          'stop',
          'cancel',
          'never mind',
          'forget it'
        ],
        intent: 'stop_listening',
        examples: ['stop listening', 'cancel']
      },

      // Media Control Intents
      {
        id: 'play_music',
        patterns: [
          'play music',
          'start music',
          'play {song}',
          'play {artist}',
          'music on'
        ],
        intent: 'play_music',
        parameters: ['song', 'artist'],
        examples: ['play music', 'play bohemian rhapsody']
      },

      // Learning and Memory Intents
      {
        id: 'what_did_i_do',
        patterns: [
          'what did i do yesterday',
          'what did i do today',
          'show my activity',
          'my recent activity',
          'what have i been doing'
        ],
        intent: 'show_activity',
        parameters: ['timeframe'],
        examples: ['what did i do yesterday', 'show my activity today']
      }
    ];

    this.isInitialized = true;
  }

  /**
   * Parse user input text and return intent object
   */
  public async parseIntent(text: string): Promise<IntentObject> {
    if (!this.isInitialized) {
      this.initializePatterns();
    }

    const normalizedText = this.normalizeText(text);

    // Try pattern matching first
    const patternMatch = this.matchPatterns(normalizedText);

    if (patternMatch.confidence > 0.7) {
      return {
        intent: patternMatch.intent,
        parameters: patternMatch.parameters,
        confidenceScore: patternMatch.confidence,
        originalText: text
      };
    }

    // Fallback to keyword matching
    const keywordMatch = this.matchKeywords(normalizedText);

    if (keywordMatch.confidence > 0.5) {
      return {
        intent: keywordMatch.intent,
        parameters: keywordMatch.parameters,
        confidenceScore: keywordMatch.confidence,
        originalText: text
      };
    }

    // Return unknown intent
    return {
      intent: 'unknown',
      parameters: {},
      confidenceScore: 0.0,
      originalText: text
    };
  }

  /**
   * Normalize text for better matching
   */
  private normalizeText(text: string): string {
    return text
      .toLowerCase()
      .trim()
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ');
  }

  /**
   * Match against predefined patterns
   */
  private matchPatterns(text: string): IntentMatch {
    let bestMatch: IntentMatch = {
      intent: 'unknown',
      confidence: 0,
      parameters: {}
    };

    for (const pattern of this.patterns) {
      for (const patternText of pattern.patterns) {
        const match = this.matchPattern(text, patternText, pattern.parameters || []);

        if (match.confidence > bestMatch.confidence) {
          bestMatch = {
            intent: pattern.intent,
            confidence: match.confidence,
            parameters: match.parameters
          };
        }
      }
    }

    return bestMatch;
  }

  /**
   * Match a single pattern against text
   */
  private matchPattern(text: string, pattern: string, parameters: string[]): IntentMatch {
    // Convert pattern to regex, replacing {param} with capture groups
    let regexPattern = pattern.replace(/\{(\w+)\}/g, '([\\w\\s]+)');
    regexPattern = `^${regexPattern}$`;

    const regex = new RegExp(regexPattern, 'i');
    const match = text.match(regex);

    if (!match) {
      return { intent: 'unknown', confidence: 0, parameters: {} };
    }

    // Extract parameters
    const extractedParams: Record<string, any> = {};
    for (let i = 0; i < parameters.length && i + 1 < match.length; i++) {
      extractedParams[parameters[i]] = match[i + 1].trim();
    }

    // Calculate confidence based on exact match
    const confidence = match[0].length === text.length ? 1.0 : 0.8;

    return {
      intent: 'matched',
      confidence,
      parameters: extractedParams
    };
  }

  /**
   * Fallback keyword matching
   */
  private matchKeywords(text: string): IntentMatch {
    const keywords = {
      'open_app': ['open', 'launch', 'start', 'run'],
      'toggle_flashlight': ['flashlight', 'torch', 'light'],
      'adjust_volume': ['volume', 'sound', 'loud', 'quiet'],
      'toggle_wifi': ['wifi', 'internet', 'connection'],
      'make_call': ['call', 'phone', 'dial'],
      'create_reminder': ['remind', 'reminder', 'remember'],
      'get_time': ['time', 'clock'],
      'get_date': ['date', 'day', 'today'],
      'play_music': ['music', 'song', 'play', 'audio'],
      'stop_listening': ['stop', 'cancel', 'quit', 'exit']
    };

    let bestMatch: IntentMatch = {
      intent: 'unknown',
      confidence: 0,
      parameters: {}
    };

    for (const [intent, intentKeywords] of Object.entries(keywords)) {
      const matches = intentKeywords.filter(keyword => text.includes(keyword));
      const confidence = matches.length / intentKeywords.length;

      if (confidence > bestMatch.confidence) {
        bestMatch = {
          intent,
          confidence: confidence * 0.6, // Lower confidence for keyword matching
          parameters: {}
        };
      }
    }

    return bestMatch;
  }

  /**
   * Add new pattern dynamically
   */
  public addPattern(pattern: IntentPattern): void {
    this.patterns.push(pattern);
  }

  /**
   * Update existing pattern
   */
  public updatePattern(id: string, updates: Partial<IntentPattern>): boolean {
    const index = this.patterns.findIndex(p => p.id === id);
    if (index !== -1) {
      this.patterns[index] = { ...this.patterns[index], ...updates };
      return true;
    }
    return false;
  }

  /**
   * Get all patterns
   */
  public getPatterns(): IntentPattern[] {
    return [...this.patterns];
  }

  /**
   * Train from user examples (simple learning)
   */
  public trainFromExample(text: string, intent: string, parameters: Record<string, any> = {}): void {
    // Find existing pattern or create new one
    let pattern = this.patterns.find(p => p.intent === intent);

    if (!pattern) {
      pattern = {
        id: `learned_${intent}_${Date.now()}`,
        patterns: [],
        intent,
        parameters: Object.keys(parameters),
        examples: []
      };
      this.patterns.push(pattern);
    }

    // Add example
    if (!pattern.examples) {
      pattern.examples = [];
    }
    pattern.examples.push(text);

    // Simple pattern generation from examples
    if (pattern.examples.length >= 3) {
      this.generatePatternsFromExamples(pattern);
    }
  }

  /**
   * Generate patterns from examples (basic implementation)
   */
  private generatePatternsFromExamples(pattern: IntentPattern): void {
    // This is a simplified pattern generation
    // In a real implementation, you'd use more sophisticated NLP
    const examples = pattern.examples || [];
    const commonWords = this.findCommonWords(examples);

    if (commonWords.length > 0) {
      const generatedPattern = commonWords.join(' ');
      if (!pattern.patterns.includes(generatedPattern)) {
        pattern.patterns.push(generatedPattern);
      }
    }
  }

  /**
   * Find common words across examples
   */
  private findCommonWords(examples: string[]): string[] {
    if (examples.length === 0) return [];

    const wordCounts: Record<string, number> = {};

    examples.forEach(example => {
      const words = this.normalizeText(example).split(' ');
      words.forEach(word => {
        if (word.length > 2) { // Ignore short words
          wordCounts[word] = (wordCounts[word] || 0) + 1;
        }
      });
    });

    // Return words that appear in most examples
    const threshold = Math.ceil(examples.length * 0.6);
    return Object.entries(wordCounts)
      .filter(([_, count]) => count >= threshold)
      .map(([word, _]) => word);
  }
}

// Export standalone function for compatibility with prompt2.txt API
export function parseIntent(text: string): IntentObject {
  const cleanedText = text.toLowerCase().trim();

  for (const rule of intentRules) {
    const match = cleanedText.match(rule.pattern);
    if (match) {
      return {
        intent: rule.intent,
        parameters: extractParameters(cleanedText, rule.intent),
        confidenceScore: 0.95,
        originalText: text
      };
    }
  }

  // Fallback
  return {
    intent: 'UNKNOWN',
    parameters: { rawText: cleanedText },
    confidenceScore: 0.4,
    originalText: text
  };
}

function extractParameters(text: string, intent: string): Record<string, string> {
  const params: Record<string, string> = {};

  if (intent === 'SET_REMINDER') {
    params['reminder'] = text.replace(/remind.*to\s?/i, '');
  }

  if (intent === 'MAKE_CALL') {
    const nameMatch = text.match(/call\s(.+)/i);
    if (nameMatch) params['contact'] = nameMatch[1];
  }

  if (intent === 'OPEN_APP_CAMERA' || intent === 'OPEN_APP_GALLERY') {
    params['app'] = intent.includes('CAMERA') ? 'camera' : 'gallery';
  }

  return params;
}

export const intentParser = new IntentParser();
