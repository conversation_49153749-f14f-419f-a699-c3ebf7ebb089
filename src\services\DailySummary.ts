/**
 * DailySummary.ts
 * Generates daily activity summaries and insights
 * Analyzes interaction patterns and provides personalized reports
 */

import moment from 'moment';
import { getRecentLogs, InteractionLogEntry } from './InteractionLogger';
import { getAllLearnedPatterns } from './LocalMemory';
import { getTodayReminders } from './ReminderManager';
import { speak } from './OfflineTTS';

interface DailySummaryData {
  date: string;
  totalInteractions: number;
  topCommands: Array<{ command: string; count: number }>;
  mostActiveHour: string;
  completedReminders: number;
  newPatternsLearned: number;
  productivityScore: number;
  insights: string[];
  recommendations: string[];
}

interface WeeklySummaryData {
  weekStart: string;
  weekEnd: string;
  dailyAverages: {
    interactions: number;
    reminders: number;
    patterns: number;
  };
  topActivities: string[];
  improvementAreas: string[];
  achievements: string[];
}

export async function generateDailySummary(date?: Date): Promise<DailySummaryData> {
  const targetDate = date || new Date();
  const startOfDay = moment(targetDate).startOf('day');
  const endOfDay = moment(targetDate).endOf('day');

  console.log(`[DailySummary] Generating summary for ${startOfDay.format('YYYY-MM-DD')}`);

  try {
    // Get all logs for the day
    const allLogs = await getRecentLogs(1000);
    const dayLogs = allLogs.filter(log => {
      const logTime = moment(log.timestamp);
      return logTime.isBetween(startOfDay, endOfDay, null, '[]');
    });

    // Analyze command frequency
    const commandCounts: Record<string, number> = {};
    dayLogs.forEach(log => {
      const command = log.command.toLowerCase();
      commandCounts[command] = (commandCounts[command] || 0) + 1;
    });

    const topCommands = Object.entries(commandCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([command, count]) => ({ command, count }));

    // Find most active hour
    const hourCounts: Record<string, number> = {};
    dayLogs.forEach(log => {
      const hour = moment(log.timestamp).format('HH:00');
      hourCounts[hour] = (hourCounts[hour] || 0) + 1;
    });

    const mostActiveHour = Object.entries(hourCounts)
      .sort(([, a], [, b]) => b - a)[0]?.[0] || '12:00';

    // Get reminders data
    const todayReminders = await getTodayReminders();
    const completedReminders = todayReminders.filter(r => 
      moment(r.timestamp).isBefore(moment())
    ).length;

    // Analyze patterns
    const patterns = getAllLearnedPatterns();
    const newPatternsToday = patterns.filter(p => 
      moment().isSame(moment(), 'day') // Simplified - would need creation date
    ).length;

    // Calculate productivity score (0-100)
    const productivityScore = calculateProductivityScore(dayLogs, completedReminders);

    // Generate insights
    const insights = generateInsights(dayLogs, topCommands, mostActiveHour);

    // Generate recommendations
    const recommendations = generateRecommendations(dayLogs, patterns);

    return {
      date: startOfDay.format('YYYY-MM-DD'),
      totalInteractions: dayLogs.length,
      topCommands,
      mostActiveHour,
      completedReminders,
      newPatternsLearned: newPatternsToday,
      productivityScore,
      insights,
      recommendations,
    };
  } catch (error) {
    console.error('[DailySummary] Error generating summary:', error);
    return getEmptySummary(targetDate);
  }
}

export async function generateWeeklySummary(): Promise<WeeklySummaryData> {
  const weekStart = moment().startOf('week');
  const weekEnd = moment().endOf('week');

  console.log(`[DailySummary] Generating weekly summary for ${weekStart.format('YYYY-MM-DD')} to ${weekEnd.format('YYYY-MM-DD')}`);

  try {
    const dailySummaries: DailySummaryData[] = [];
    
    // Generate summaries for each day of the week
    for (let i = 0; i < 7; i++) {
      const date = weekStart.clone().add(i, 'days').toDate();
      const summary = await generateDailySummary(date);
      dailySummaries.push(summary);
    }

    // Calculate averages
    const totalInteractions = dailySummaries.reduce((sum, day) => sum + day.totalInteractions, 0);
    const totalReminders = dailySummaries.reduce((sum, day) => sum + day.completedReminders, 0);
    const totalPatterns = dailySummaries.reduce((sum, day) => sum + day.newPatternsLearned, 0);

    const dailyAverages = {
      interactions: Math.round(totalInteractions / 7),
      reminders: Math.round(totalReminders / 7),
      patterns: Math.round(totalPatterns / 7),
    };

    // Find top activities
    const allCommands: Record<string, number> = {};
    dailySummaries.forEach(day => {
      day.topCommands.forEach(cmd => {
        allCommands[cmd.command] = (allCommands[cmd.command] || 0) + cmd.count;
      });
    });

    const topActivities = Object.entries(allCommands)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([command]) => command);

    // Generate improvement areas and achievements
    const improvementAreas = generateImprovementAreas(dailySummaries);
    const achievements = generateAchievements(dailySummaries);

    return {
      weekStart: weekStart.format('YYYY-MM-DD'),
      weekEnd: weekEnd.format('YYYY-MM-DD'),
      dailyAverages,
      topActivities,
      improvementAreas,
      achievements,
    };
  } catch (error) {
    console.error('[DailySummary] Error generating weekly summary:', error);
    return getEmptyWeeklySummary();
  }
}

export async function speakDailySummary(date?: Date): Promise<void> {
  const summary = await generateDailySummary(date);
  
  const summaryText = `
    Here's your daily summary for ${moment(summary.date).format('MMMM Do')}.
    You had ${summary.totalInteractions} interactions with me today.
    Your most active hour was ${summary.mostActiveHour}.
    You completed ${summary.completedReminders} reminders.
    Your productivity score is ${summary.productivityScore} out of 100.
    ${summary.insights.length > 0 ? summary.insights[0] : ''}
  `.trim().replace(/\s+/g, ' ');

  await speak(summaryText);
}

export async function speakWeeklySummary(): Promise<void> {
  const summary = await generateWeeklySummary();
  
  const summaryText = `
    Here's your weekly summary.
    You averaged ${summary.dailyAverages.interactions} interactions per day.
    Your top activities were ${summary.topActivities.slice(0, 3).join(', ')}.
    ${summary.achievements.length > 0 ? summary.achievements[0] : ''}
  `.trim().replace(/\s+/g, ' ');

  await speak(summaryText);
}

function calculateProductivityScore(logs: InteractionLogEntry[], completedReminders: number): number {
  // Simple productivity scoring algorithm
  let score = 0;

  // Base score from interactions (max 40 points)
  score += Math.min(logs.length * 2, 40);

  // Bonus for completed reminders (max 30 points)
  score += Math.min(completedReminders * 10, 30);

  // Bonus for variety of commands (max 20 points)
  const uniqueCommands = new Set(logs.map(log => log.intent)).size;
  score += Math.min(uniqueCommands * 3, 20);

  // Bonus for consistent usage throughout day (max 10 points)
  const hours = new Set(logs.map(log => moment(log.timestamp).hour())).size;
  score += Math.min(hours, 10);

  return Math.min(score, 100);
}

function generateInsights(logs: InteractionLogEntry[], topCommands: Array<{ command: string; count: number }>, mostActiveHour: string): string[] {
  const insights: string[] = [];

  if (logs.length > 20) {
    insights.push("You had a very active day with JARVIS!");
  } else if (logs.length < 5) {
    insights.push("You had a quiet day with minimal assistant usage.");
  }

  if (topCommands.length > 0) {
    insights.push(`Your most used command today was "${topCommands[0].command}".`);
  }

  const hour = parseInt(mostActiveHour.split(':')[0]);
  if (hour < 12) {
    insights.push("You're most active with JARVIS in the morning.");
  } else if (hour < 17) {
    insights.push("You're most active with JARVIS in the afternoon.");
  } else {
    insights.push("You're most active with JARVIS in the evening.");
  }

  return insights;
}

function generateRecommendations(logs: InteractionLogEntry[], patterns: any[]): string[] {
  const recommendations: string[] = [];

  // Analyze command patterns
  const commandTypes = logs.map(log => log.intent);
  const hasSystemCommands = commandTypes.some(cmd => cmd.includes('TOGGLE') || cmd.includes('ADJUST'));
  const hasAppCommands = commandTypes.some(cmd => cmd.includes('OPEN_APP'));

  if (!hasSystemCommands) {
    recommendations.push("Try using voice commands to control your device settings like brightness or volume.");
  }

  if (!hasAppCommands) {
    recommendations.push("You can ask me to open apps like camera, gallery, or music player.");
  }

  if (patterns.length < 3) {
    recommendations.push("Keep using JARVIS regularly to help me learn your patterns and provide better suggestions.");
  }

  return recommendations;
}

function generateImprovementAreas(summaries: DailySummaryData[]): string[] {
  const areas: string[] = [];

  const avgProductivity = summaries.reduce((sum, day) => sum + day.productivityScore, 0) / summaries.length;
  if (avgProductivity < 50) {
    areas.push("Consider setting more reminders to boost productivity");
  }

  const avgInteractions = summaries.reduce((sum, day) => sum + day.totalInteractions, 0) / summaries.length;
  if (avgInteractions < 10) {
    areas.push("Try exploring more voice commands to get the most out of JARVIS");
  }

  return areas;
}

function generateAchievements(summaries: DailySummaryData[]): string[] {
  const achievements: string[] = [];

  const totalInteractions = summaries.reduce((sum, day) => sum + day.totalInteractions, 0);
  if (totalInteractions > 100) {
    achievements.push("Power user achievement: Over 100 interactions this week!");
  }

  const consistentDays = summaries.filter(day => day.totalInteractions > 5).length;
  if (consistentDays >= 5) {
    achievements.push("Consistency champion: Active with JARVIS 5+ days this week!");
  }

  return achievements;
}

function getEmptySummary(date: Date): DailySummaryData {
  return {
    date: moment(date).format('YYYY-MM-DD'),
    totalInteractions: 0,
    topCommands: [],
    mostActiveHour: '12:00',
    completedReminders: 0,
    newPatternsLearned: 0,
    productivityScore: 0,
    insights: ['No activity recorded for this day.'],
    recommendations: ['Start using JARVIS to build your activity history.'],
  };
}

function getEmptyWeeklySummary(): WeeklySummaryData {
  return {
    weekStart: moment().startOf('week').format('YYYY-MM-DD'),
    weekEnd: moment().endOf('week').format('YYYY-MM-DD'),
    dailyAverages: { interactions: 0, reminders: 0, patterns: 0 },
    topActivities: [],
    improvementAreas: ['Start using JARVIS regularly to see insights'],
    achievements: [],
  };
}

// Export types
export type { DailySummaryData, WeeklySummaryData };
