/**
 * QuickActionChips.tsx
 * Horizontal scrollable quick action buttons
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from 'react-native';

import { useTheme } from '@/utils/ThemeContext';

interface QuickAction {
  id: string;
  label: string;
  icon: string;
  action: string;
}

interface QuickActionChipsProps {
  onActionPress: (action: string) => void;
  disabled?: boolean;
}

const quickActions: QuickAction[] = [
  { id: 'camera', label: 'Camera', icon: '📷', action: 'camera' },
  { id: 'flashlight', label: 'Flashlight', icon: '🔦', action: 'flashlight' },
  { id: 'music', label: 'Music', icon: '🎵', action: 'music' },
  { id: 'time', label: 'Time', icon: '🕐', action: 'time' },
  { id: 'weather', label: 'Weather', icon: '🌤️', action: 'weather' },
  { id: 'settings', label: 'Settings', icon: '⚙️', action: 'settings' },
];

const QuickActionChips: React.FC<QuickActionChipsProps> = ({
  onActionPress,
  disabled = false,
}) => {
  const { theme } = useTheme();

  const handleActionPress = (action: string) => {
    if (!disabled) {
      onActionPress(action);
    }
  };

  return (
    <View style={localStyles.container}>
      <Text
        style={[
          localStyles.title,
          { color: theme.colors.textSecondary },
        ]}
      >
        Quick Actions
      </Text>
      
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={localStyles.scrollContent}
        style={localStyles.scrollView}
      >
        {quickActions.map((action) => (
          <TouchableOpacity
            key={action.id}
            style={[
              localStyles.chip,
              {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border,
                opacity: disabled ? 0.5 : 1,
              },
            ]}
            onPress={() => handleActionPress(action.action)}
            disabled={disabled}
            activeOpacity={0.7}
          >
            <Text style={localStyles.icon}>{action.icon}</Text>
            <Text
              style={[
                localStyles.label,
                { color: theme.colors.text },
              ]}
            >
              {action.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const localStyles = StyleSheet.create({
  container: {
    marginVertical: 20,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
    marginLeft: 20,
  },
  scrollView: {
    flexGrow: 0,
  },
  scrollContent: {
    paddingHorizontal: 20,
  },
  chip: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 12,
    borderRadius: 20,
    borderWidth: 1,
    minWidth: 80,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  icon: {
    fontSize: 24,
    marginBottom: 4,
  },
  label: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default QuickActionChips;
