/**
 * AutomationImportExportScreen.tsx
 * Interface for importing and exporting automation rules
 * Allows users to backup and share their automation configurations
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  Share,
  Clipboard,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { exportRules, importRules, getStorageStats, clearAllRules, restoreFromBackup } from '../modules/AutomationStorage';

const AutomationImportExportScreen: React.FC = () => {
  const [jsonData, setJsonData] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState({ totalRules: 0, activeRules: 0, categoryCounts: {}, lastBackup: undefined });

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const statsData = await getStorageStats();
      setStats(statsData);
    } catch (error) {
      console.error('[ImportExport] Error loading stats:', error);
    }
  };

  const handleExport = async () => {
    setIsLoading(true);
    try {
      const json = await exportRules();
      setJsonData(json);
      Alert.alert('Success', 'Rules exported successfully! You can now copy or share the JSON data.');
    } catch (error) {
      console.error('[ImportExport] Export error:', error);
      Alert.alert('Error', 'Failed to export rules');
    } finally {
      setIsLoading(false);
    }
  };

  const handleImport = async (replaceExisting: boolean = false) => {
    if (!jsonData.trim()) {
      Alert.alert('Error', 'Please paste JSON data to import');
      return;
    }

    setIsLoading(true);
    try {
      const success = await importRules(jsonData, replaceExisting);
      if (success) {
        Alert.alert(
          'Success',
          replaceExisting
            ? 'Rules imported successfully! All existing rules have been replaced.'
            : 'Rules imported successfully! New rules have been added to your existing ones.'
        );
        setJsonData('');
        await loadStats();
      } else {
        Alert.alert('Error', 'Failed to import rules. Please check the JSON format.');
      }
    } catch (error) {
      console.error('[ImportExport] Import error:', error);
      Alert.alert('Error', 'Failed to import rules. Invalid JSON format.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = async () => {
    if (!jsonData.trim()) {
      Alert.alert('Error', 'No data to share. Please export rules first.');
      return;
    }

    try {
      await Share.share({
        message: jsonData,
        title: 'JARVIS Lite Automation Rules',
      });
    } catch (error) {
      console.error('[ImportExport] Share error:', error);
      Alert.alert('Error', 'Failed to share rules');
    }
  };

  const handleCopyToClipboard = async () => {
    if (!jsonData.trim()) {
      Alert.alert('Error', 'No data to copy. Please export rules first.');
      return;
    }

    try {
      await Clipboard.setString(jsonData);
      Alert.alert('Success', 'Rules copied to clipboard!');
    } catch (error) {
      console.error('[ImportExport] Copy error:', error);
      Alert.alert('Error', 'Failed to copy to clipboard');
    }
  };

  const handlePasteFromClipboard = async () => {
    try {
      const clipboardData = await Clipboard.getString();
      if (clipboardData) {
        setJsonData(clipboardData);
        Alert.alert('Success', 'Data pasted from clipboard!');
      } else {
        Alert.alert('Info', 'Clipboard is empty');
      }
    } catch (error) {
      console.error('[ImportExport] Paste error:', error);
      Alert.alert('Error', 'Failed to paste from clipboard');
    }
  };

  const handleClearAll = () => {
    Alert.alert(
      'Clear All Rules',
      'Are you sure you want to delete all automation rules? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: async () => {
            setIsLoading(true);
            try {
              const success = await clearAllRules();
              if (success) {
                Alert.alert('Success', 'All automation rules have been cleared.');
                await loadStats();
              } else {
                Alert.alert('Error', 'Failed to clear rules');
              }
            } catch (error) {
              console.error('[ImportExport] Clear error:', error);
              Alert.alert('Error', 'Failed to clear rules');
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  const handleRestoreBackup = () => {
    Alert.alert(
      'Restore Backup',
      'This will restore your rules from the last backup. Current rules will be replaced.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Restore',
          onPress: async () => {
            setIsLoading(true);
            try {
              const success = await restoreFromBackup();
              if (success) {
                Alert.alert('Success', 'Rules restored from backup successfully!');
                await loadStats();
              } else {
                Alert.alert('Error', 'No backup found or restore failed');
              }
            } catch (error) {
              console.error('[ImportExport] Restore error:', error);
              Alert.alert('Error', 'Failed to restore from backup');
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.heading}>📤 Export / 📥 Import Rules</Text>

      {/* Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.totalRules}</Text>
          <Text style={styles.statLabel}>Total Rules</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.activeRules}</Text>
          <Text style={styles.statLabel}>Active Rules</Text>
        </View>
      </View>

      {/* Export Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📤 Export Rules</Text>
        <Text style={styles.sectionDescription}>
          Export your automation rules as JSON to backup or share with others.
        </Text>

        <TouchableOpacity
          onPress={handleExport}
          style={[styles.button, styles.exportButton]}
          disabled={isLoading}
        >
          <Icon name="export" size={20} color="#000" />
          <Text style={styles.buttonText}>Export Rules</Text>
        </TouchableOpacity>

        {jsonData ? (
          <View style={styles.actionRow}>
            <TouchableOpacity onPress={handleCopyToClipboard} style={styles.smallButton}>
              <Icon name="content-copy" size={16} color="#00FFB2" />
              <Text style={styles.smallButtonText}>Copy</Text>
            </TouchableOpacity>

            <TouchableOpacity onPress={handleShare} style={styles.smallButton}>
              <Icon name="share" size={16} color="#00FFB2" />
              <Text style={styles.smallButtonText}>Share</Text>
            </TouchableOpacity>
          </View>
        ) : null}
      </View>

      {/* JSON Data Display/Input */}
      <View style={styles.section}>
        <View style={styles.textBoxHeader}>
          <Text style={styles.sectionTitle}>JSON Data</Text>
          <TouchableOpacity onPress={handlePasteFromClipboard} style={styles.pasteButton}>
            <Icon name="content-paste" size={16} color="#00FFB2" />
            <Text style={styles.pasteButtonText}>Paste</Text>
          </TouchableOpacity>
        </View>

        <TextInput
          multiline
          style={styles.textBox}
          placeholder="Paste JSON here to import or view exported data..."
          placeholderTextColor="#666"
          value={jsonData}
          onChangeText={setJsonData}
          editable={!isLoading}
        />
      </View>

      {/* Import Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📥 Import Rules</Text>
        <Text style={styles.sectionDescription}>
          Import automation rules from JSON data. You can merge with existing rules or replace them entirely.
        </Text>

        <View style={styles.buttonRow}>
          <TouchableOpacity
            onPress={() => handleImport(false)}
            style={[styles.button, styles.importButton]}
            disabled={isLoading || !jsonData.trim()}
          >
            <Icon name="plus" size={20} color="#fff" />
            <Text style={[styles.buttonText, { color: '#fff' }]}>Merge Rules</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => handleImport(true)}
            style={[styles.button, styles.replaceButton]}
            disabled={isLoading || !jsonData.trim()}
          >
            <Icon name="swap-horizontal" size={20} color="#fff" />
            <Text style={[styles.buttonText, { color: '#fff' }]}>Replace All</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Backup & Restore Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🔄 Backup & Restore</Text>
        {stats.lastBackup && (
          <Text style={styles.backupInfo}>
            Last backup: {new Date(stats.lastBackup).toLocaleString()}
          </Text>
        )}

        <View style={styles.buttonRow}>
          <TouchableOpacity
            onPress={handleRestoreBackup}
            style={[styles.button, styles.restoreButton]}
            disabled={isLoading}
          >
            <Icon name="restore" size={20} color="#fff" />
            <Text style={[styles.buttonText, { color: '#fff' }]}>Restore Backup</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleClearAll}
            style={[styles.button, styles.clearButton]}
            disabled={isLoading}
          >
            <Icon name="delete-sweep" size={20} color="#fff" />
            <Text style={[styles.buttonText, { color: '#fff' }]}>Clear All</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#121212',
    flex: 1,
  },
  heading: {
    color: '#00FFB2',
    fontSize: 20,
    marginBottom: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
    backgroundColor: '#1a1a1a',
    borderRadius: 8,
    padding: 12,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#00FFB2',
  },
  statLabel: {
    fontSize: 10,
    color: '#aaa',
    marginTop: 4,
  },
  section: {
    marginBottom: 24,
    backgroundColor: '#1a1a1a',
    borderRadius: 8,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#ccc',
    marginBottom: 12,
    lineHeight: 20,
  },
  textBoxHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  textBox: {
    backgroundColor: '#2a2a2a',
    color: '#fff',
    height: 150,
    padding: 12,
    borderRadius: 6,
    textAlignVertical: 'top',
    fontSize: 12,
    fontFamily: 'monospace',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  exportButton: {
    backgroundColor: '#00FFB2',
  },
  importButton: {
    backgroundColor: '#4ECDC4',
    flex: 1,
    marginRight: 8,
  },
  replaceButton: {
    backgroundColor: '#FF6B6B',
    flex: 1,
    marginLeft: 8,
  },
  restoreButton: {
    backgroundColor: '#45B7D1',
    flex: 1,
    marginRight: 8,
  },
  clearButton: {
    backgroundColor: '#E74C3C',
    flex: 1,
    marginLeft: 8,
  },
  buttonText: {
    textAlign: 'center',
    fontWeight: 'bold',
    color: '#000',
    fontSize: 14,
    marginLeft: 8,
  },
  buttonRow: {
    flexDirection: 'row',
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 12,
  },
  smallButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 4,
    backgroundColor: '#2a2a2a',
  },
  smallButtonText: {
    color: '#00FFB2',
    fontSize: 12,
    marginLeft: 4,
  },
  pasteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 6,
    borderRadius: 4,
    backgroundColor: '#2a2a2a',
  },
  pasteButtonText: {
    color: '#00FFB2',
    fontSize: 12,
    marginLeft: 4,
  },
  backupInfo: {
    fontSize: 12,
    color: '#aaa',
    marginBottom: 12,
    fontStyle: 'italic',
  },
});

export default AutomationImportExportScreen;
