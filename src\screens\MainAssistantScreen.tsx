/**
 * MainAssistantScreen.tsx
 * Main interface for JARVIS Lite AI assistant
 * Features floating mic button, response area, command history, and quick actions
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

// Import types and services
import { RootStackParamList, VoiceProcessorState, IntentObject } from '@/types';
import { useTheme, getThemedStyles } from '@/utils/ThemeContext';
import { usePersonalityConfig } from '@/utils/AppConfigContext';
import {
  initializeJARVIS,
  startVoiceListening,
  stopVoiceListening,
  isJARVISReady,
  getSmartSuggestions
} from '@/services/JARVIS';

// Import components (we'll create these)
import FloatingMicButton from '@/components/FloatingMicButton';
import ResponseBubble from '@/components/ResponseBubble';
import CommandHistoryList from '@/components/CommandHistoryList';
import QuickActionChips from '@/components/QuickActionChips';

type NavigationProp = StackNavigationProp<RootStackParamList, 'MainAssistant'>;

interface AssistantState {
  isListening: boolean;
  isProcessing: boolean;
  currentResponse: string;
  isTyping: boolean;
  voiceState: VoiceProcessorState;
}

interface CommandHistoryItem {
  id: string;
  timestamp: Date;
  command: string;
  intent: string;
  response: string;
  success: boolean;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const MainAssistantScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { theme } = useTheme();
  const { personality } = usePersonalityConfig();
  const styles = getThemedStyles(theme);

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(screenHeight)).current;

  // State
  const [assistantState, setAssistantState] = useState<AssistantState>({
    isListening: false,
    isProcessing: false,
    currentResponse: '',
    isTyping: false,
    voiceState: VoiceProcessorState.IDLE,
  });

  const [commandHistory, setCommandHistory] = useState<CommandHistoryItem[]>([]);
  const [showHistory, setShowHistory] = useState(false);

  useEffect(() => {
    initializeScreen();

    return () => {
      cleanup();
    };
  }, []);

  useEffect(() => {
    // Animate screen entrance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  /**
   * Initialize screen
   */
  const initializeScreen = async (): Promise<void> => {
    try {
      // Initialize JARVIS
      const success = await initializeJARVIS();

      if (success) {
        // Show welcome message
        const welcomeMessage = getWelcomeMessage();
        setAssistantState(prev => ({
          ...prev,
          currentResponse: welcomeMessage,
        }));

        // Get smart suggestions
        const suggestions = await getSmartSuggestions();
        if (suggestions.length > 0) {
          console.log('Smart suggestions:', suggestions);
        }
      } else {
        setAssistantState(prev => ({
          ...prev,
          currentResponse: "I'm having trouble starting up. Please check the system status.",
        }));
      }

    } catch (error) {
      console.error('Error initializing main screen:', error);
      setAssistantState(prev => ({
        ...prev,
        currentResponse: "I'm having trouble starting up. Please check the system status.",
      }));
    }
  };

  // Voice processing is now handled by JARVIS orchestrator

  // Voice command processing is now handled by JARVIS orchestrator

  /**
   * Handle mic button press
   */
  const handleMicPress = async (): Promise<void> => {
    try {
      if (!isJARVISReady()) {
        setAssistantState(prev => ({
          ...prev,
          currentResponse: "JARVIS is still initializing. Please wait a moment.",
        }));
        return;
      }

      if (assistantState.isListening) {
        await stopVoiceListening();
        setAssistantState(prev => ({
          ...prev,
          isListening: false,
          voiceState: VoiceProcessorState.IDLE,
        }));
      } else {
        await startVoiceListening();
        setAssistantState(prev => ({
          ...prev,
          isListening: true,
          voiceState: VoiceProcessorState.LISTENING,
        }));
      }
    } catch (error) {
      console.error('Error toggling voice input:', error);
      setAssistantState(prev => ({
        ...prev,
        currentResponse: "Voice input is not available right now.",
        isListening: false,
        voiceState: VoiceProcessorState.ERROR,
      }));
    }
  };

  /**
   * Handle quick action press
   */
  const handleQuickAction = async (action: string): Promise<void> => {
    const quickActions: Record<string, string> = {
      camera: 'open camera',
      flashlight: 'toggle flashlight',
      music: 'play music',
      time: 'what time is it',
      settings: 'open settings',
    };

    const command = quickActions[action];
    if (command) {
      // Use JARVIS to process the command
      const { processVoiceCommand } = await import('@/services/JARVIS');
      await processVoiceCommand(command);
    }
  };

  /**
   * Get personalized welcome message
   */
  const getWelcomeMessage = (): string => {
    const hour = new Date().getHours();
    const timeGreeting = hour < 12 ? 'Good morning' : hour < 17 ? 'Good afternoon' : 'Good evening';
    const avatar = personality?.emojiAvatar || '🤖';

    const greetings = [
      `${timeGreeting}! ${avatar} I'm JARVIS Lite, ready to assist you.`,
      `${timeGreeting}! How can I help you today? ${avatar}`,
      `Hello! ${avatar} JARVIS Lite at your service.`,
    ];

    return personality?.customGreeting || greetings[Math.floor(Math.random() * greetings.length)];
  };

  /**
   * Start pulse animation for mic button
   */
  const startPulseAnimation = (): void => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  /**
   * Stop pulse animation
   */
  const stopPulseAnimation = (): void => {
    pulseAnim.stopAnimation();
    Animated.timing(pulseAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  /**
   * Cleanup resources
   */
  const cleanup = (): void => {
    stopPulseAnimation();
    // Voice processor cleanup is handled in its own component
  };

  return (
    <SafeAreaView style={[styles.container, localStyles.container]}>
      <StatusBar
        backgroundColor="transparent"
        barStyle={theme.isDark ? 'light-content' : 'dark-content'}
        translucent
      />

      <Animated.View
        style={[
          localStyles.mainContent,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {/* Assistant Avatar */}
        <View style={localStyles.avatarContainer}>
          <Text style={localStyles.avatar}>{personality?.emojiAvatar || '🤖'}</Text>
          <Text style={[styles.text, localStyles.assistantName]}>JARVIS Lite</Text>
        </View>

        {/* Response Area */}
        <ResponseBubble
          text={assistantState.currentResponse}
          isTyping={assistantState.isTyping}
          isProcessing={assistantState.isProcessing}
        />

        {/* Quick Actions */}
        <QuickActionChips
          onActionPress={handleQuickAction}
          disabled={assistantState.isProcessing}
        />

        {/* Command History Toggle */}
        <TouchableOpacity
          style={localStyles.historyToggle}
          onPress={() => setShowHistory(!showHistory)}
        >
          <Text style={[styles.textSecondary, localStyles.historyToggleText]}>
            {showHistory ? 'Hide History' : 'Show History'}
          </Text>
        </TouchableOpacity>

        {/* Command History */}
        {showHistory && (
          <CommandHistoryList
            history={commandHistory}
            onItemPress={(item) => handleVoiceCommand(item.command)}
          />
        )}

        {/* Offline Status */}
        <View style={localStyles.offlineStatus}>
          <Text style={[styles.textSecondary, localStyles.offlineText]}>
            🔒 JARVIS Lite is running offline
          </Text>
        </View>
      </Animated.View>

      {/* Floating Mic Button */}
      <Animated.View
        style={[
          localStyles.micButtonContainer,
          {
            transform: [{ scale: pulseAnim }],
          },
        ]}
      >
        <FloatingMicButton
          isListening={assistantState.isListening}
          isProcessing={assistantState.isProcessing}
          onPress={handleMicPress}
          voiceState={assistantState.voiceState}
        />
      </Animated.View>
    </SafeAreaView>
  );
};

const localStyles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 40,
  },
  avatarContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  avatar: {
    fontSize: 60,
    marginBottom: 10,
  },
  assistantName: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  historyToggle: {
    alignSelf: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginVertical: 10,
  },
  historyToggleText: {
    fontSize: 14,
    textDecorationLine: 'underline',
  },
  offlineStatus: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    alignItems: 'center',
  },
  offlineText: {
    fontSize: 12,
    opacity: 0.7,
  },
  micButtonContainer: {
    position: 'absolute',
    bottom: 100,
    alignSelf: 'center',
  },
});

export default MainAssistantScreen;
