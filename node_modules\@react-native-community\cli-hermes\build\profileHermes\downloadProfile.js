"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.downloadProfile = downloadProfile;
function _child_process() {
  const data = require("child_process");
  _child_process = function () {
    return data;
  };
  return data;
}
function _cliTools() {
  const data = require("@react-native-community/cli-tools");
  _cliTools = function () {
    return data;
  };
  return data;
}
function _fs() {
  const data = _interopRequireDefault(require("fs"));
  _fs = function () {
    return data;
  };
  return data;
}
function _path() {
  const data = _interopRequireDefault(require("path"));
  _path = function () {
    return data;
  };
  return data;
}
function _os() {
  const data = _interopRequireDefault(require("os"));
  _os = function () {
    return data;
  };
  return data;
}
function _hermesProfileTransformer() {
  const data = _interopRequireDefault(require("hermes-profile-transformer"));
  _hermesProfileTransformer = function () {
    return data;
  };
  return data;
}
var _sourcemapUtils = require("./sourcemapUtils");
function _cliPlatformAndroid() {
  const data = require("@react-native-community/cli-platform-android");
  _cliPlatformAndroid = function () {
    return data;
  };
  return data;
}
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
/**
 * Get the last modified hermes profile
 * @param packageNameWithSuffix
 */
function getLatestFile(packageNameWithSuffix) {
  try {
    const file = (0, _child_process().execSync)(`adb shell run-as ${packageNameWithSuffix} ls cache/ -tp | grep -v /$ | grep -E '.cpuprofile' | head -1
        `);
    return file.toString().trim();
  } catch (e) {
    throw e;
  }
}
function execSyncWithLog(command) {
  _cliTools().logger.debug(`${command}`);
  return (0, _child_process().execSync)(command);
}

/**
 * Pull and convert a Hermes tracing profile to Chrome tracing profile
 * @param ctx
 * @param dstPath
 * @param fileName
 * @param sourceMapPath
 * @param raw
 * @param generateSourceMap
 * @param appId
 * @param appIdSuffix
 */
async function downloadProfile(ctx, dstPath, filename, sourcemapPath, raw, shouldGenerateSourcemap, port, appId, appIdSuffix) {
  try {
    const androidProject = (0, _cliPlatformAndroid().getAndroidProject)(ctx);
    const packageNameWithSuffix = [appId || androidProject.packageName, appIdSuffix].filter(Boolean).join('.');

    // If file name is not specified, pull the latest file from device
    const file = filename || getLatestFile(packageNameWithSuffix);
    if (!file) {
      throw new (_cliTools().CLIError)('There is no file in the cache/ directory. Did you record a profile from the developer menu?');
    }
    _cliTools().logger.info(`File to be pulled: ${file}`);

    // If destination path is not specified, pull to the current directory
    dstPath = dstPath || ctx.root;
    _cliTools().logger.debug('Internal commands run to pull the file:');

    // If --raw, pull the hermes profile to dstPath
    if (raw) {
      execSyncWithLog(`adb shell run-as ${packageNameWithSuffix} cat cache/${file} > ${dstPath}/${file}`);
      _cliTools().logger.success(`Successfully pulled the file to ${dstPath}/${file}`);
    }

    // Else: transform the profile to Chrome format and pull it to dstPath
    else {
      const osTmpDir = _os().default.tmpdir();
      const tempFilePath = _path().default.join(osTmpDir, file);
      execSyncWithLog(`adb shell run-as ${packageNameWithSuffix} cat cache/${file} > ${tempFilePath}`);
      // If path to source map is not given
      if (!sourcemapPath) {
        // Get or generate the source map
        if (shouldGenerateSourcemap) {
          sourcemapPath = await (0, _sourcemapUtils.generateSourcemap)(port);
        } else {
          sourcemapPath = await (0, _sourcemapUtils.findSourcemap)(ctx, port);
        }

        // Run without source map
        if (!sourcemapPath) {
          _cliTools().logger.warn('Cannot find source maps, running the transformer without it');
          _cliTools().logger.info('Instructions on how to get source maps: set `bundleInDebug: true` in your app/build.gradle file, inside the `project.ext.react` map.');
        }
      }

      // Run transformer tool to convert from Hermes to Chrome format
      const events = await (0, _hermesProfileTransformer().default)(tempFilePath, sourcemapPath, 'index.bundle');
      const transformedFilePath = `${dstPath}/${_path().default.basename(file, '.cpuprofile')}-converted.json`;
      _fs().default.writeFileSync(transformedFilePath, JSON.stringify(events, undefined, 4), 'utf-8');
      _cliTools().logger.success(`Successfully converted to Chrome tracing format and pulled the file to ${transformedFilePath}`);
    }
  } catch (e) {
    throw e;
  }
}

//# sourceMappingURL=downloadProfile.ts.map