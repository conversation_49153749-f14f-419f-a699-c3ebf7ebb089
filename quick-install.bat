@echo off
echo ========================================
echo JARVIS Lite - Quick Install Solution
echo ========================================
echo.

echo Your device is connected: RWRGL7MFDEKFBQBQ
echo.

echo Option 1: Install Expo Go and test immediately
echo ---------------------------------------------
echo 1. Install "Expo Go" app from Google Play Store
echo 2. Run: npm install -g @expo/cli
echo 3. Run: npx create-expo-app JarvisLiteDemo --template blank-typescript
echo 4. Copy JARVIS Lite code to the new project
echo 5. Run: npx expo start
echo 6. Scan QR code with Expo Go
echo.

echo Option 2: Use React Native CLI with fresh project
echo ------------------------------------------------
echo 1. Run: npx react-native init JarvisLiteClean --version 0.72.6
echo 2. Copy JARVIS Lite source code to new project
echo 3. Install dependencies
echo 4. Build and run
echo.

echo Option 3: Direct APK install (if available)
echo -------------------------------------------
echo If you have an APK file, install directly:
echo adb install -r jarvis-lite.apk
echo.

echo ========================================
echo Recommendation: Try Expo Go first!
echo ========================================
echo.
echo Expo Go will let you test JARVIS Lite immediately
echo without dealing with Android build configuration.
echo.

pause

echo.
echo Starting Expo setup...
echo.

echo Installing Expo CLI...
npm install -g @expo/cli

echo.
echo Creating new Expo project...
npx create-expo-app JarvisLiteExpo --template blank-typescript

echo.
echo Project created! Next steps:
echo 1. Copy JARVIS Lite code to JarvisLiteExpo folder
echo 2. Install dependencies in the new project
echo 3. Run: cd JarvisLiteExpo && npx expo start
echo 4. Scan QR code with Expo Go app on your phone
echo.

pause
