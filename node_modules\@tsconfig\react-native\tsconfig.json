{"$schema": "https://json.schemastore.org/tsconfig", "_version": "3.0.2", "compilerOptions": {"target": "esnext", "module": "commonjs", "types": ["react-native", "jest"], "lib": ["es2019", "es2020.bigint", "es2020.date", "es2020.number", "es2020.promise", "es2020.string", "es2020.symbol.wellknown", "es2021.promise", "es2021.string", "es2021.weakref", "es2022.array", "es2022.object", "es2022.string"], "allowJs": true, "jsx": "react-native", "noEmit": true, "isolatedModules": true, "strict": true, "moduleResolution": "node", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true}}