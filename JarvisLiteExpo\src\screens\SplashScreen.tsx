/**
 * SplashScreen.tsx
 * Animated splash screen with JARVIS Lite branding
 */

import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function SplashScreen() {
  const navigation = useNavigation();
  const fade = new Animated.Value(0);

  useEffect(() => {
    const initializeApp = async () => {
      // Start fade animation
      Animated.timing(fade, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      }).start();

      // Check if user has launched before
      try {
        const hasLaunched = await AsyncStorage.getItem('launchedBefore');
        
        setTimeout(() => {
          if (hasLaunched) {
            navigation.replace('MainAssistant' as never);
          } else {
            AsyncStorage.setItem('launchedBefore', 'true');
            navigation.replace('Onboarding' as never);
          }
        }, 2500); // Show splash for 2.5 seconds
      } catch (error) {
        console.error('[SplashScreen] Error checking launch status:', error);
        // Default to onboarding if error
        setTimeout(() => {
          navigation.replace('Onboarding' as never);
        }, 2500);
      }
    };

    initializeApp();
  }, [navigation, fade]);

  return (
    <View style={styles.container}>
      <Animated.Text style={[styles.title, { opacity: fade }]}>
        JΛRVIS Lite
      </Animated.Text>
      <Animated.Text style={[styles.subtitle, { opacity: fade }]}>
        Offline AI. Always with you.
      </Animated.Text>
      <Animated.View style={[styles.loader, { opacity: fade }]} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  title: {
    fontSize: 36,
    color: '#00FFB2',
    fontWeight: 'bold',
    letterSpacing: 2,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#00FFB2',
    marginTop: 10,
    opacity: 0.8,
    textAlign: 'center',
  },
  loader: {
    marginTop: 30,
    height: 4,
    width: 100,
    backgroundColor: '#00FFB2',
    borderRadius: 2,
  },
});
