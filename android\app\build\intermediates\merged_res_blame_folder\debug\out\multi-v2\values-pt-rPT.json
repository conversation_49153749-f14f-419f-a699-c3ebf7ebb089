{"logs": [{"outputFile": "com.jarvislite.app-mergeDebugResources-29:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "F:\\gradle-cache\\caches\\transforms-3\\ab12076093e54ca9ba43148476352294\\transformed\\appcompat-1.4.2\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "278,386,492,599,688,789,907,992,1072,1164,1258,1355,1449,1548,1642,1738,1833,1925,2017,2102,2209,2320,2422,2530,2638,2745,2910,7442", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "381,487,594,683,784,902,987,1067,1159,1253,1350,1444,1543,1637,1733,1828,1920,2012,2097,2204,2315,2417,2525,2633,2740,2905,3004,7523"}}, {"source": "F:\\gradle-cache\\caches\\transforms-3\\6c9de45700a7851c879b33c8d726a07f\\transformed\\core-1.8.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7528", "endColumns": "100", "endOffsets": "7624"}}, {"source": "F:\\gradle-cache\\caches\\transforms-3\\c572b377194a8a385981f85402606c3e\\transformed\\material-1.6.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,310,413,533,614,678,770,849,914,1004,1072,1134,1207,1271,1325,1451,1509,1571,1625,1701,1844,1931,2013,2122,2204,2286,2373,2440,2506,2581,2661,2748,2821,2898,2971,3045,3138,3215,3308,3406,3480,3561,3660,3713,3779,3868,3956,4018,4082,4145,4261,4364,4471,4575", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,102,119,80,63,91,78,64,89,67,61,72,63,53,125,57,61,53,75,142,86,81,108,81,81,86,66,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,85", "endOffsets": "223,305,408,528,609,673,765,844,909,999,1067,1129,1202,1266,1320,1446,1504,1566,1620,1696,1839,1926,2008,2117,2199,2281,2368,2435,2501,2576,2656,2743,2816,2893,2966,3040,3133,3210,3303,3401,3475,3556,3655,3708,3774,3863,3951,4013,4077,4140,4256,4359,4466,4570,4656"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3009,3091,3194,3314,3395,3459,3551,3630,3695,3785,3853,3915,3988,4052,4106,4232,4290,4352,4406,4482,4625,4712,4794,4903,4985,5067,5154,5221,5287,5362,5442,5529,5602,5679,5752,5826,5919,5996,6089,6187,6261,6342,6441,6494,6560,6649,6737,6799,6863,6926,7042,7145,7252,7356", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,81,102,119,80,63,91,78,64,89,67,61,72,63,53,125,57,61,53,75,142,86,81,108,81,81,86,66,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,85", "endOffsets": "273,3086,3189,3309,3390,3454,3546,3625,3690,3780,3848,3910,3983,4047,4101,4227,4285,4347,4401,4477,4620,4707,4789,4898,4980,5062,5149,5216,5282,5357,5437,5524,5597,5674,5747,5821,5914,5991,6084,6182,6256,6337,6436,6489,6555,6644,6732,6794,6858,6921,7037,7140,7247,7351,7437"}}]}]}