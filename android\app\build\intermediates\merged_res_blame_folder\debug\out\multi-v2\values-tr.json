{"logs": [{"outputFile": "com.jarvislite.app-mergeDebugResources-29:/values-tr/values-tr.xml", "map": [{"source": "F:\\gradle-cache\\caches\\transforms-3\\6c9de45700a7851c879b33c8d726a07f\\transformed\\core-1.8.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7299", "endColumns": "100", "endOffsets": "7395"}}, {"source": "F:\\gradle-cache\\caches\\transforms-3\\ab12076093e54ca9ba43148476352294\\transformed\\appcompat-1.4.2\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,384,483,595,680,786,906,986,1061,1152,1245,1337,1431,1531,1624,1726,1821,1912,2003,2082,2189,2293,2389,2496,2599,2708,2864,7219", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "379,478,590,675,781,901,981,1056,1147,1240,1332,1426,1526,1619,1721,1816,1907,1998,2077,2184,2288,2384,2491,2594,2703,2859,2957,7294"}}, {"source": "F:\\gradle-cache\\caches\\transforms-3\\c572b377194a8a385981f85402606c3e\\transformed\\material-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,297,393,505,587,651,742,819,880,971,1034,1093,1162,1225,1279,1387,1445,1507,1561,1634,1755,1839,1930,2040,2117,2193,2280,2347,2413,2483,2560,2643,2714,2789,2867,2938,3023,3112,3207,3300,3372,3444,3540,3592,3659,3743,3833,3895,3959,4022,4116,4212,4301,4398", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,95,111,81,63,90,76,60,90,62,58,68,62,53,107,57,61,53,72,120,83,90,109,76,75,86,66,65,69,76,82,70,74,77,70,84,88,94,92,71,71,95,51,66,83,89,61,63,62,93,95,88,96,78", "endOffsets": "215,292,388,500,582,646,737,814,875,966,1029,1088,1157,1220,1274,1382,1440,1502,1556,1629,1750,1834,1925,2035,2112,2188,2275,2342,2408,2478,2555,2638,2709,2784,2862,2933,3018,3107,3202,3295,3367,3439,3535,3587,3654,3738,3828,3890,3954,4017,4111,4207,4296,4393,4472"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2962,3039,3135,3247,3329,3393,3484,3561,3622,3713,3776,3835,3904,3967,4021,4129,4187,4249,4303,4376,4497,4581,4672,4782,4859,4935,5022,5089,5155,5225,5302,5385,5456,5531,5609,5680,5765,5854,5949,6042,6114,6186,6282,6334,6401,6485,6575,6637,6701,6764,6858,6954,7043,7140", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,76,95,111,81,63,90,76,60,90,62,58,68,62,53,107,57,61,53,72,120,83,90,109,76,75,86,66,65,69,76,82,70,74,77,70,84,88,94,92,71,71,95,51,66,83,89,61,63,62,93,95,88,96,78", "endOffsets": "265,3034,3130,3242,3324,3388,3479,3556,3617,3708,3771,3830,3899,3962,4016,4124,4182,4244,4298,4371,4492,4576,4667,4777,4854,4930,5017,5084,5150,5220,5297,5380,5451,5526,5604,5675,5760,5849,5944,6037,6109,6181,6277,6329,6396,6480,6570,6632,6696,6759,6853,6949,7038,7135,7214"}}]}]}