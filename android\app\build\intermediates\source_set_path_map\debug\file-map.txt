com.jarvislite.app-jetified-savedstate-1.1.0-0 F:\gradle-cache\caches\transforms-3\1439685bbe36ee41bd634acd15700d76\transformed\jetified-savedstate-1.1.0\res
com.jarvislite.app-transition-1.2.0-1 F:\gradle-cache\caches\transforms-3\15135d2e7a25889be5627095d6d2c745\transformed\transition-1.2.0\res
com.jarvislite.app-cardview-1.0.0-2 F:\gradle-cache\caches\transforms-3\2b55356f56543166e7bde63d258c8943\transformed\cardview-1.0.0\res
com.jarvislite.app-coordinatorlayout-1.2.0-3 F:\gradle-cache\caches\transforms-3\2d68e271aea678e72589c8cc9877e0ad\transformed\coordinatorlayout-1.2.0\res
com.jarvislite.app-jetified-autofill-1.1.0-4 F:\gradle-cache\caches\transforms-3\310bc8789d92739ad542b189d9cba041\transformed\jetified-autofill-1.1.0\res
com.jarvislite.app-jetified-appcompat-resources-1.4.2-5 F:\gradle-cache\caches\transforms-3\340ecd04f65bea04e220644699ca2e30\transformed\jetified-appcompat-resources-1.4.2\res
com.jarvislite.app-drawerlayout-1.1.1-6 F:\gradle-cache\caches\transforms-3\3db21b5b0927d179863295a6430118a1\transformed\drawerlayout-1.1.1\res
com.jarvislite.app-jetified-emoji2-views-helper-1.0.0-7 F:\gradle-cache\caches\transforms-3\566761a0341f70aab2ab9eba03a03bfe\transformed\jetified-emoji2-views-helper-1.0.0\res
com.jarvislite.app-jetified-react-android-0.72.6-debug-8 F:\gradle-cache\caches\transforms-3\59d2ff0b6347c1a315f288d4329a7d28\transformed\jetified-react-android-0.72.6-debug\res
com.jarvislite.app-core-1.8.0-9 F:\gradle-cache\caches\transforms-3\6c9de45700a7851c879b33c8d726a07f\transformed\core-1.8.0\res
com.jarvislite.app-recyclerview-1.1.0-10 F:\gradle-cache\caches\transforms-3\6e907c5a662ed5fb07b8fe8debef2071\transformed\recyclerview-1.1.0\res
com.jarvislite.app-jetified-emoji2-1.0.0-11 F:\gradle-cache\caches\transforms-3\7752f00a9ab209825b03d268e4986842\transformed\jetified-emoji2-1.0.0\res
com.jarvislite.app-lifecycle-viewmodel-2.3.1-12 F:\gradle-cache\caches\transforms-3\8c21e01e5bf1e69b296125173074873e\transformed\lifecycle-viewmodel-2.3.1\res
com.jarvislite.app-jetified-tracing-1.1.0-13 F:\gradle-cache\caches\transforms-3\90f3d923ad7e9d92b7d856b1a7d3d0d3\transformed\jetified-tracing-1.1.0\res
com.jarvislite.app-appcompat-1.4.2-14 F:\gradle-cache\caches\transforms-3\ab12076093e54ca9ba43148476352294\transformed\appcompat-1.4.2\res
com.jarvislite.app-jetified-lifecycle-process-2.4.0-15 F:\gradle-cache\caches\transforms-3\b5799f2137ee91c1cc5ce05b0561e59f\transformed\jetified-lifecycle-process-2.4.0\res
com.jarvislite.app-material-1.6.1-16 F:\gradle-cache\caches\transforms-3\c572b377194a8a385981f85402606c3e\transformed\material-1.6.1\res
com.jarvislite.app-jetified-annotation-experimental-1.1.0-17 F:\gradle-cache\caches\transforms-3\d09835cff6f6e8d50deb43579d8ac4c6\transformed\jetified-annotation-experimental-1.1.0\res
com.jarvislite.app-jetified-drawee-2.5.0-18 F:\gradle-cache\caches\transforms-3\d3fe09a9aefc14091dbfd11ae362fa89\transformed\jetified-drawee-2.5.0\res
com.jarvislite.app-lifecycle-runtime-2.4.0-19 F:\gradle-cache\caches\transforms-3\d5520a225def22ded685bd6e381b8dea\transformed\lifecycle-runtime-2.4.0\res
com.jarvislite.app-jetified-viewpager2-1.0.0-20 F:\gradle-cache\caches\transforms-3\d96d30db2bdb6412ed453dcd702c4bb4\transformed\jetified-viewpager2-1.0.0\res
com.jarvislite.app-jetified-core-ktx-1.8.0-21 F:\gradle-cache\caches\transforms-3\dc41285db52eb0cd7e9663e166da2823\transformed\jetified-core-ktx-1.8.0\res
com.jarvislite.app-constraintlayout-2.0.1-22 F:\gradle-cache\caches\transforms-3\dc90cd28ca6ab5c37f0641415f960eae\transformed\constraintlayout-2.0.1\res
com.jarvislite.app-jetified-startup-runtime-1.0.0-23 F:\gradle-cache\caches\transforms-3\df5520077ecd40ff7181ebfe285cd272\transformed\jetified-startup-runtime-1.0.0\res
com.jarvislite.app-fragment-1.3.6-24 F:\gradle-cache\caches\transforms-3\ef802cd2c4d5e3cdb9b62720a17abc6d\transformed\fragment-1.3.6\res
com.jarvislite.app-swiperefreshlayout-1.1.0-25 F:\gradle-cache\caches\transforms-3\f249f371f5d3b03ad219f3dc58423412\transformed\swiperefreshlayout-1.1.0\res
com.jarvislite.app-pngs-26 F:\jarvis\android\app\build\generated\res\pngs\debug
com.jarvislite.app-resValues-27 F:\jarvis\android\app\build\generated\res\resValues\debug
com.jarvislite.app-rs-28 F:\jarvis\android\app\build\generated\res\rs\debug
com.jarvislite.app-mergeDebugResources-29 F:\jarvis\android\app\build\intermediates\incremental\debug\mergeDebugResources\merged.dir
com.jarvislite.app-mergeDebugResources-30 F:\jarvis\android\app\build\intermediates\incremental\debug\mergeDebugResources\stripped.dir
com.jarvislite.app-merged_res-31 F:\jarvis\android\app\build\intermediates\merged_res\debug
com.jarvislite.app-debug-32 F:\jarvis\android\app\src\debug\res
com.jarvislite.app-main-33 F:\jarvis\android\app\src\main\res
com.jarvislite.app-packaged_res-34 F:\jarvis\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug
com.jarvislite.app-packaged_res-35 F:\jarvis\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug
com.jarvislite.app-packaged_res-36 F:\jarvis\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug
com.jarvislite.app-packaged_res-37 F:\jarvis\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug
com.jarvislite.app-packaged_res-38 F:\jarvis\node_modules\react-native-vector-icons\android\build\intermediates\packaged_res\debug
