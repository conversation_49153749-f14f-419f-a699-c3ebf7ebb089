/**
 * AutomationTimelineScreen.tsx
 * Visual timeline dashboard showing automation rules and their status
 * Displays past triggered automations and upcoming rules
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { getRules, getActiveRules, getStorageStats } from '../modules/AutomationStorage';
import { AutomationRule, AVAILABLE_ACTIONS, formatTriggerTime, getActionById } from '../models/AutomationRule';
import moment from 'moment';

interface TimelineItem extends AutomationRule {
  status: 'upcoming' | 'active' | 'past' | 'disabled';
  timeUntil?: string;
}

const AutomationTimelineScreen: React.FC = () => {
  const [rules, setRules] = useState<AutomationRule[]>([]);
  const [timelineItems, setTimelineItems] = useState<TimelineItem[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<string>('All');
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({ totalRules: 0, activeRules: 0, categoryCounts: {} });

  useEffect(() => {
    loadData();

    // Update timeline every minute
    const interval = setInterval(updateTimeline, 60000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    updateTimeline();
  }, [rules]);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const [rulesData, statsData] = await Promise.all([
        getRules(),
        getStorageStats()
      ]);
      setRules(rulesData);
      setStats(statsData);
    } catch (error) {
      console.error('[AutomationTimeline] Error loading data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateTimeline = () => {
    const now = moment();
    const items: TimelineItem[] = rules.map(rule => {
      const ruleTime = moment(rule.triggerTime, 'HH:mm');
      const todayRuleTime = now.clone().hour(ruleTime.hour()).minute(ruleTime.minute()).second(0);

      let status: 'upcoming' | 'active' | 'past' | 'disabled';
      let timeUntil: string | undefined;

      if (!rule.enabled) {
        status = 'disabled';
      } else if (now.isSame(todayRuleTime, 'minute')) {
        status = 'active';
        timeUntil = 'Now';
      } else if (now.isBefore(todayRuleTime)) {
        status = 'upcoming';
        const duration = moment.duration(todayRuleTime.diff(now));
        timeUntil = formatDuration(duration);
      } else {
        status = 'past';
        const nextRun = todayRuleTime.add(1, 'day');
        const duration = moment.duration(nextRun.diff(now));
        timeUntil = `Tomorrow (${formatDuration(duration)})`;
      }

      return {
        ...rule,
        status,
        timeUntil,
      };
    });

    // Sort by time
    items.sort((a, b) => a.triggerTime.localeCompare(b.triggerTime));
    setTimelineItems(items);
  };

  const formatDuration = (duration: moment.Duration): string => {
    const hours = Math.floor(duration.asHours());
    const minutes = duration.minutes();

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const getFilteredItems = (): TimelineItem[] => {
    if (selectedFilter === 'All') {
      return timelineItems;
    }
    return timelineItems.filter(item => item.category === selectedFilter);
  };

  const getStatusIcon = (status: string): string => {
    switch (status) {
      case 'upcoming': return 'clock-outline';
      case 'active': return 'play-circle';
      case 'past': return 'check-circle';
      case 'disabled': return 'pause-circle';
      default: return 'help-circle';
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'upcoming': return '#4ECDC4';
      case 'active': return '#00FFB2';
      case 'past': return '#95A5A6';
      case 'disabled': return '#E74C3C';
      default: return '#95A5A6';
    }
  };

  const getCategoryColor = (category: string): string => {
    switch (category) {
      case 'Media': return '#FF6B6B';
      case 'Utility': return '#4ECDC4';
      case 'Wellness': return '#45B7D1';
      case 'Custom': return '#96CEB4';
      default: return '#95A5A6';
    }
  };

  const renderTimelineItem = ({ item }: { item: TimelineItem }) => {
    const action = getActionById(item.action);
    const actionLabel = action?.label || item.action;

    return (
      <View style={[styles.timelineCard, { borderLeftColor: getStatusColor(item.status) }]}>
        <View style={styles.cardHeader}>
          <View style={styles.timeContainer}>
            <Icon
              name={getStatusIcon(item.status)}
              size={20}
              color={getStatusColor(item.status)}
            />
            <Text style={styles.timeText}>{formatTriggerTime(item.triggerTime)}</Text>
          </View>
          <View style={[styles.categoryBadge, { backgroundColor: getCategoryColor(item.category) }]}>
            <Text style={styles.categoryText}>{item.category}</Text>
          </View>
        </View>

        <Text style={styles.actionText}>⚡ {actionLabel}</Text>

        <View style={styles.statusContainer}>
          <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
            {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
          </Text>
          {item.timeUntil && (
            <Text style={styles.timeUntilText}>{item.timeUntil}</Text>
          )}
        </View>
      </View>
    );
  };

  const renderFilterButton = (filter: string) => (
    <TouchableOpacity
      key={filter}
      onPress={() => setSelectedFilter(filter)}
      style={[
        styles.filterButton,
        selectedFilter === filter && styles.filterButtonActive,
        filter !== 'All' && { backgroundColor: selectedFilter === filter ? getCategoryColor(filter) : '#333' }
      ]}
    >
      <Text style={[
        styles.filterButtonText,
        selectedFilter === filter && styles.filterButtonTextActive
      ]}>
        {filter}
      </Text>
    </TouchableOpacity>
  );

  const filteredItems = getFilteredItems();
  const upcomingCount = filteredItems.filter(item => item.status === 'upcoming').length;
  const activeCount = filteredItems.filter(item => item.status === 'active').length;

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>📅 Automation Timeline</Text>

      {/* Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{activeCount}</Text>
          <Text style={styles.statLabel}>Active Now</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{upcomingCount}</Text>
          <Text style={styles.statLabel}>Upcoming</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.totalRules}</Text>
          <Text style={styles.statLabel}>Total Rules</Text>
        </View>
      </View>

      {/* Filters */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filterContainer}
        contentContainerStyle={styles.filterContent}
      >
        {['All', 'Media', 'Utility', 'Wellness', 'Custom'].map(renderFilterButton)}
      </ScrollView>

      {/* Timeline */}
      <FlatList
        data={filteredItems}
        keyExtractor={(item) => item.id}
        renderItem={renderTimelineItem}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={loadData}
            colors={['#00FFB2']}
            tintColor="#00FFB2"
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="timeline-clock-outline" size={64} color="#666" />
            <Text style={styles.emptyText}>
              {selectedFilter === 'All'
                ? 'No automation rules scheduled.'
                : `No ${selectedFilter} rules found.`
              }
            </Text>
            <Text style={styles.emptySubtext}>
              Create automation rules to see them here!
            </Text>
          </View>
        }
        contentContainerStyle={filteredItems.length === 0 ? styles.emptyList : undefined}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#121212',
  },
  heading: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#00FFB2',
    marginBottom: 16,
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
    backgroundColor: '#1a1a1a',
    borderRadius: 8,
    padding: 12,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#00FFB2',
  },
  statLabel: {
    fontSize: 10,
    color: '#aaa',
    marginTop: 4,
  },
  filterContainer: {
    marginBottom: 16,
  },
  filterContent: {
    paddingHorizontal: 4,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#333',
  },
  filterButtonActive: {
    backgroundColor: '#00FFB2',
  },
  filterButtonText: {
    fontSize: 12,
    color: '#ccc',
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: '#000',
    fontWeight: 'bold',
  },
  timelineCard: {
    padding: 16,
    backgroundColor: '#1e1e1e',
    marginBottom: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: 'bold',
  },
  actionText: {
    fontSize: 14,
    color: '#00FFB2',
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  timeUntilText: {
    fontSize: 12,
    color: '#ccc',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyList: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  emptyText: {
    color: '#aaa',
    fontStyle: 'italic',
    marginTop: 16,
    textAlign: 'center',
    fontSize: 16,
  },
  emptySubtext: {
    color: '#666',
    textAlign: 'center',
    fontSize: 14,
    marginTop: 8,
  },
});

export default AutomationTimelineScreen;
