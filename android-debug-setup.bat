@echo off
echo ========================================
echo Android Device Debug Connection Setup
echo ========================================
echo.

echo Step 1: Check ADB is working
adb version
echo.

echo Step 2: Kill and restart ADB server
echo Killing ADB server...
adb kill-server
timeout /t 2 /nobreak >nul

echo Starting ADB server...
adb start-server
echo.

echo Step 3: Check for connected devices
echo Looking for devices...
adb devices
echo.

echo Step 4: Check if device is detected by Windows
echo Checking USB devices...
wmic path Win32_USBControllerDevice get Dependent | findstr /i "android\|adb\|usb"
echo.

echo ========================================
echo TROUBLESHOOTING STEPS
echo ========================================
echo.

echo If no device shows up, try these:
echo.

echo 📱 ON YOUR PHONE:
echo 1. Go to Settings → About phone
echo 2. Tap "Build number" 7 times (enables Developer options)
echo 3. Go to Settings → Developer options
echo 4. Enable "USB debugging" ✅
echo 5. Enable "Install via USB" ✅
echo 6. Change "USB configuration" to "File Transfer (MTP)" or "PTP"
echo.

echo 💻 ON YOUR COMPUTER:
echo 1. Try different USB cable
echo 2. Try different USB port
echo 3. Install/update Android USB drivers
echo 4. Restart ADB: adb kill-server && adb start-server
echo.

echo 🔧 ADVANCED TROUBLESHOOTING:
echo 1. Revoke USB debugging authorizations on phone
echo 2. Disable and re-enable USB debugging
echo 3. Change USB mode to "File Transfer" then back to "Charging"
echo 4. Install universal ADB drivers
echo.

echo ========================================
echo TESTING CONNECTION
echo ========================================
echo.

echo Please follow the steps above, then:
echo 1. Unplug and replug your USB cable
echo 2. On phone: Allow USB debugging when prompted
echo 3. Check "Always allow from this computer"
echo 4. Press any key to test connection again...

pause

echo.
echo Testing connection again...
adb devices

echo.
if "%ERRORLEVEL%"=="0" (
    echo ✅ ADB is working
) else (
    echo ❌ ADB has issues
)

echo.
echo ========================================
echo ALTERNATIVE: Wireless ADB (if USB fails)
echo ========================================
echo.

echo If USB debugging doesn't work, you can try wireless:
echo 1. Connect phone and computer to same WiFi
echo 2. Enable "Wireless debugging" in Developer options
echo 3. Get IP address from phone WiFi settings
echo 4. Run: adb connect [phone-ip]:5555
echo.

echo ========================================
echo NEXT STEPS AFTER CONNECTION
echo ========================================
echo.

echo Once your device shows as "device" (not "unauthorized"):
echo.

echo Option 1: Install JARVIS Lite via React Native
echo   node "node_modules/@react-native-community/cli/build/bin.js" run-android
echo.

echo Option 2: Install JARVIS Lite via Expo
echo   cd JarvisLiteExpo
echo   npx expo start
echo   Scan QR code with Expo Go app
echo.

echo Option 3: Install APK directly
echo   adb install jarvis-lite.apk
echo.

pause
