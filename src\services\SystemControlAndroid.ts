/**
 * SystemControlAndroid.ts
 * React Native bridge module for executing native Android actions
 * Controls flashlight, volume, WiFi, apps, calls, etc.
 */

import { 
  NativeModules, 
  Linking, 
  PermissionsAndroid, 
  Platform,
  ToastAndroid 
} from 'react-native';
import DeviceInfo from 'react-native-device-info';

// Native module interface (would be implemented in native Android code)
interface SystemControlModule {
  toggleFlashlight(): Promise<boolean>;
  setVolume(level: number): Promise<number>;
  getCurrentVolume(): Promise<number>;
  toggleWifi(): Promise<boolean>;
  isWifiEnabled(): Promise<boolean>;
  launchAppByPackage(packageName: string): Promise<boolean>;
  getInstalledApps(): Promise<string[]>;
  makePhoneCall(number: string): Promise<boolean>;
  openCamera(): Promise<boolean>;
  setBrightness(level: number): Promise<boolean>;
  toggleBluetooth(): Promise<boolean>;
}

// Mock implementation for development
const mockSystemControl: SystemControlModule = {
  async toggleFlashlight(): Promise<boolean> {
    console.log('Mock: Toggle flashlight');
    return Math.random() > 0.5;
  },
  async setVolume(level: number): Promise<number> {
    console.log(`Mock: Set volume to ${level}`);
    return Math.max(0, Math.min(100, level));
  },
  async getCurrentVolume(): Promise<number> {
    console.log('Mock: Get current volume');
    return 50;
  },
  async toggleWifi(): Promise<boolean> {
    console.log('Mock: Toggle WiFi');
    return Math.random() > 0.5;
  },
  async isWifiEnabled(): Promise<boolean> {
    console.log('Mock: Check WiFi status');
    return Math.random() > 0.5;
  },
  async launchAppByPackage(packageName: string): Promise<boolean> {
    console.log(`Mock: Launch app ${packageName}`);
    return Math.random() > 0.3;
  },
  async getInstalledApps(): Promise<string[]> {
    console.log('Mock: Get installed apps');
    return ['com.android.camera', 'com.spotify.music', 'com.whatsapp'];
  },
  async makePhoneCall(number: string): Promise<boolean> {
    console.log(`Mock: Make call to ${number}`);
    return Math.random() > 0.2;
  },
  async openCamera(): Promise<boolean> {
    console.log('Mock: Open camera');
    return Math.random() > 0.1;
  },
  async setBrightness(level: number): Promise<boolean> {
    console.log(`Mock: Set brightness to ${level}`);
    return Math.random() > 0.1;
  },
  async toggleBluetooth(): Promise<boolean> {
    console.log('Mock: Toggle Bluetooth');
    return Math.random() > 0.5;
  }
};

class SystemControlAndroid {
  private nativeModule: SystemControlModule;
  private appPackageMap: Map<string, string> = new Map();

  constructor() {
    // Use native module if available, otherwise use mock
    this.nativeModule = NativeModules.SystemControlModule || mockSystemControl;
    this.initializeAppPackageMap();
  }

  /**
   * Initialize mapping of app names to package names
   */
  private initializeAppPackageMap(): void {
    this.appPackageMap.set('camera', 'com.android.camera');
    this.appPackageMap.set('gallery', 'com.android.gallery3d');
    this.appPackageMap.set('photos', 'com.google.android.apps.photos');
    this.appPackageMap.set('contacts', 'com.android.contacts');
    this.appPackageMap.set('phone', 'com.android.dialer');
    this.appPackageMap.set('messages', 'com.android.mms');
    this.appPackageMap.set('settings', 'com.android.settings');
    this.appPackageMap.set('chrome', 'com.android.chrome');
    this.appPackageMap.set('youtube', 'com.google.android.youtube');
    this.appPackageMap.set('spotify', 'com.spotify.music');
    this.appPackageMap.set('whatsapp', 'com.whatsapp');
    this.appPackageMap.set('telegram', 'org.telegram.messenger');
    this.appPackageMap.set('instagram', 'com.instagram.android');
    this.appPackageMap.set('facebook', 'com.facebook.katana');
    this.appPackageMap.set('twitter', 'com.twitter.android');
    this.appPackageMap.set('gmail', 'com.google.android.gm');
    this.appPackageMap.set('maps', 'com.google.android.apps.maps');
    this.appPackageMap.set('calculator', 'com.android.calculator2');
    this.appPackageMap.set('calendar', 'com.android.calendar');
    this.appPackageMap.set('clock', 'com.android.deskclock');
    this.appPackageMap.set('music', 'com.android.music');
    this.appPackageMap.set('play store', 'com.android.vending');
  }

  /**
   * Launch an app by name or package
   */
  public async launchApp(appName: string): Promise<boolean> {
    try {
      const normalizedName = appName.toLowerCase().trim();
      
      // Try to get package name from our mapping
      let packageName = this.appPackageMap.get(normalizedName);
      
      if (!packageName) {
        // Try common package name patterns
        packageName = this.guessPackageName(normalizedName);
      }

      if (packageName) {
        // Try launching via native module first
        try {
          const success = await this.nativeModule.launchAppByPackage(packageName);
          if (success) return true;
        } catch (error) {
          console.log(`Failed to launch via native module: ${error.message}`);
        }

        // Fallback to Linking API
        try {
          const url = `intent://launch?package=${packageName}#Intent;scheme=android-app;end`;
          const canOpen = await Linking.canOpenURL(url);
          if (canOpen) {
            await Linking.openURL(url);
            return true;
          }
        } catch (error) {
          console.log(`Failed to launch via Linking: ${error.message}`);
        }
      }

      // Last resort: try to open via generic intent
      try {
        const searchUrl = `market://search?q=${encodeURIComponent(appName)}`;
        const canOpen = await Linking.canOpenURL(searchUrl);
        if (canOpen) {
          await Linking.openURL(searchUrl);
          return true;
        }
      } catch (error) {
        console.log(`Failed to open Play Store search: ${error.message}`);
      }

      return false;
    } catch (error) {
      console.error(`Error launching app ${appName}:`, error);
      return false;
    }
  }

  /**
   * Toggle flashlight on/off
   */
  public async toggleFlashlight(): Promise<boolean> {
    try {
      return await this.nativeModule.toggleFlashlight();
    } catch (error) {
      console.error('Error toggling flashlight:', error);
      this.showToast('Flashlight control not available');
      return false;
    }
  }

  /**
   * Set volume level (0-100)
   */
  public async setVolume(level: number): Promise<number> {
    try {
      const clampedLevel = Math.max(0, Math.min(100, level));
      return await this.nativeModule.setVolume(clampedLevel);
    } catch (error) {
      console.error('Error setting volume:', error);
      this.showToast('Volume control not available');
      return await this.getCurrentVolume();
    }
  }

  /**
   * Adjust volume up or down
   */
  public async adjustVolume(direction: 'up' | 'down'): Promise<number> {
    try {
      const currentVolume = await this.getCurrentVolume();
      const adjustment = direction === 'up' ? 10 : -10;
      const newLevel = Math.max(0, Math.min(100, currentVolume + adjustment));
      return await this.setVolume(newLevel);
    } catch (error) {
      console.error('Error adjusting volume:', error);
      return 0;
    }
  }

  /**
   * Get current volume level
   */
  public async getCurrentVolume(): Promise<number> {
    try {
      return await this.nativeModule.getCurrentVolume();
    } catch (error) {
      console.error('Error getting volume:', error);
      return 0;
    }
  }

  /**
   * Toggle WiFi on/off
   */
  public async toggleWifi(): Promise<boolean> {
    try {
      return await this.nativeModule.toggleWifi();
    } catch (error) {
      console.error('Error toggling WiFi:', error);
      this.showToast('WiFi control requires system permissions');
      return false;
    }
  }

  /**
   * Check if WiFi is enabled
   */
  public async isWifiEnabled(): Promise<boolean> {
    try {
      return await this.nativeModule.isWifiEnabled();
    } catch (error) {
      console.error('Error checking WiFi status:', error);
      return false;
    }
  }

  /**
   * Make a phone call
   */
  public async makeCall(contact: string): Promise<boolean> {
    try {
      // Check if we have phone permission
      if (Platform.OS === 'android') {
        const hasPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.CALL_PHONE
        );
        
        if (!hasPermission) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.CALL_PHONE,
            {
              title: 'Phone Permission',
              message: 'JARVIS Lite needs phone permission to make calls',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            }
          );
          
          if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
            this.showToast('Phone permission denied');
            return false;
          }
        }
      }

      // Try native module first
      try {
        const success = await this.nativeModule.makePhoneCall(contact);
        if (success) return true;
      } catch (error) {
        console.log(`Native call failed: ${error.message}`);
      }

      // Fallback to Linking API
      const phoneUrl = `tel:${contact}`;
      const canOpen = await Linking.canOpenURL(phoneUrl);
      
      if (canOpen) {
        await Linking.openURL(phoneUrl);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error making call:', error);
      this.showToast('Unable to make call');
      return false;
    }
  }

  /**
   * Open camera app
   */
  public async openCamera(): Promise<boolean> {
    try {
      // Try native module first
      try {
        const success = await this.nativeModule.openCamera();
        if (success) return true;
      } catch (error) {
        console.log(`Native camera open failed: ${error.message}`);
      }

      // Fallback to launching camera app
      return await this.launchApp('camera');
    } catch (error) {
      console.error('Error opening camera:', error);
      return false;
    }
  }

  /**
   * Set screen brightness (0-100)
   */
  public async setBrightness(level: number): Promise<boolean> {
    try {
      const clampedLevel = Math.max(0, Math.min(100, level));
      return await this.nativeModule.setBrightness(clampedLevel);
    } catch (error) {
      console.error('Error setting brightness:', error);
      this.showToast('Brightness control requires system permissions');
      return false;
    }
  }

  /**
   * Toggle Bluetooth on/off
   */
  public async toggleBluetooth(): Promise<boolean> {
    try {
      return await this.nativeModule.toggleBluetooth();
    } catch (error) {
      console.error('Error toggling Bluetooth:', error);
      this.showToast('Bluetooth control requires system permissions');
      return false;
    }
  }

  /**
   * Get list of installed apps
   */
  public async getInstalledApps(): Promise<string[]> {
    try {
      return await this.nativeModule.getInstalledApps();
    } catch (error) {
      console.error('Error getting installed apps:', error);
      return [];
    }
  }

  /**
   * Get device information
   */
  public async getDeviceInfo(): Promise<{
    model: string;
    brand: string;
    systemVersion: string;
    batteryLevel: number;
  }> {
    try {
      const [model, brand, systemVersion, batteryLevel] = await Promise.all([
        DeviceInfo.getModel(),
        DeviceInfo.getBrand(),
        DeviceInfo.getSystemVersion(),
        DeviceInfo.getBatteryLevel()
      ]);

      return {
        model,
        brand,
        systemVersion,
        batteryLevel: Math.round(batteryLevel * 100)
      };
    } catch (error) {
      console.error('Error getting device info:', error);
      return {
        model: 'Unknown',
        brand: 'Unknown',
        systemVersion: 'Unknown',
        batteryLevel: 0
      };
    }
  }

  /**
   * Guess package name from app name
   */
  private guessPackageName(appName: string): string | null {
    // Common package name patterns
    const patterns = [
      `com.${appName}.android`,
      `com.${appName}`,
      `com.google.android.${appName}`,
      `org.${appName}`,
      `${appName}.android`
    ];

    // Return first pattern (in real implementation, you'd check if package exists)
    return patterns[0];
  }

  /**
   * Show toast message
   */
  private showToast(message: string): void {
    if (Platform.OS === 'android') {
      ToastAndroid.show(message, ToastAndroid.SHORT);
    }
  }

  /**
   * Check if required permissions are granted
   */
  public async checkPermissions(): Promise<{
    phone: boolean;
    camera: boolean;
    microphone: boolean;
    storage: boolean;
  }> {
    if (Platform.OS !== 'android') {
      return { phone: false, camera: false, microphone: false, storage: false };
    }

    try {
      const [phone, camera, microphone, storage] = await Promise.all([
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.CALL_PHONE),
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.CAMERA),
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO),
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE)
      ]);

      return { phone, camera, microphone, storage };
    } catch (error) {
      console.error('Error checking permissions:', error);
      return { phone: false, camera: false, microphone: false, storage: false };
    }
  }

  /**
   * Request required permissions
   */
  public async requestPermissions(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    try {
      const permissions = [
        PermissionsAndroid.PERMISSIONS.CALL_PHONE,
        PermissionsAndroid.PERMISSIONS.CAMERA,
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE
      ];

      const results = await PermissionsAndroid.requestMultiple(permissions);
      
      return Object.values(results).every(
        result => result === PermissionsAndroid.RESULTS.GRANTED
      );
    } catch (error) {
      console.error('Error requesting permissions:', error);
      return false;
    }
  }
}

export const systemControlAndroid = new SystemControlAndroid();
