@echo off
echo ========================================
echo JARVIS Lite - Build and Install Script
echo ========================================
echo.

echo Checking device connection...
adb devices
echo.

echo Building JARVIS Lite APK...
cd /d F:\jarvis\android
gradlew.bat assembleDebug

if %errorlevel% neq 0 (
    echo ❌ Build failed
    echo Trying alternative build method...
    cd /d F:\jarvis
    node "node_modules/@react-native-community/cli/build/bin.js" run-android --no-packager
    goto :end
)

echo ✅ Build successful!
echo.

echo Installing APK on device...
adb install -r app\build\outputs\apk\debug\app-debug.apk

if %errorlevel% neq 0 (
    echo ❌ Installation failed
    echo Please check device connection and USB debugging
    goto :end
)

echo ✅ JARVIS Lite installed successfully!
echo.
echo Starting the app...
adb shell am start -n com.jarvislite/.MainActivity

echo.
echo ========================================
echo JARVIS Lite is now running on your device!
echo ========================================

:end
pause
