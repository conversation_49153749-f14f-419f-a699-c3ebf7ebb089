/**
 * StartupGreetingScreen.tsx
 * Personalized greeting screen shown on app launch
 */

import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { RootStackParamList } from '@/types';
import { useTheme, getThemedStyles } from '@/utils/ThemeContext';
import { usePersonalityConfig } from '@/utils/AppConfigContext';

type NavigationProp = StackNavigationProp<RootStackParamList, 'StartupGreeting'>;

const { width: screenWidth } = Dimensions.get('window');

const StartupGreetingScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { theme } = useTheme();
  const { personality } = usePersonalityConfig();
  const styles = getThemedStyles(theme);

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    startAnimations();
    
    // Auto-navigate after 3 seconds
    const timer = setTimeout(() => {
      navigateToMain();
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const startAnimations = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const navigateToMain = () => {
    navigation.replace('MainAssistant');
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    const timeGreeting = hour < 12 ? 'Good morning' : hour < 17 ? 'Good afternoon' : 'Good evening';
    
    if (personality?.customGreeting) {
      return personality.customGreeting;
    }

    const greetings = [
      `${timeGreeting}, Commander! 👾`,
      `${timeGreeting}! Ready for action? 🚀`,
      `Welcome back! Let's get things done 💪`,
      `${timeGreeting}! How can I assist you today? 🤖`,
    ];

    return greetings[Math.floor(Math.random() * greetings.length)];
  };

  const getQuote = () => {
    const quotes = [
      "Small routines make big results.",
      "Discipline is choosing between what you want now and what you want most.",
      "The future belongs to those who prepare for it today.",
      "Excellence is not an act, but a habit.",
      "Progress, not perfection.",
    ];

    return quotes[Math.floor(Math.random() * quotes.length)];
  };

  return (
    <SafeAreaView style={[styles.container, localStyles.container]}>
      <Animated.View
        style={[
          localStyles.content,
          {
            opacity: fadeAnim,
            transform: [
              { scale: scaleAnim },
              { translateY: slideAnim },
            ],
          },
        ]}
      >
        {/* Assistant Avatar */}
        <View style={localStyles.avatarContainer}>
          <Text style={localStyles.avatar}>
            {personality?.emojiAvatar || '🤖'}
          </Text>
        </View>

        {/* Greeting */}
        <Text style={[styles.text, localStyles.greeting]}>
          {getGreeting()}
        </Text>

        {/* Quote of the day */}
        <View style={localStyles.quoteContainer}>
          <Text style={[styles.textSecondary, localStyles.quote]}>
            "{getQuote()}"
          </Text>
        </View>

        {/* Continue Button */}
        <TouchableOpacity
          style={[styles.button, localStyles.continueButton]}
          onPress={navigateToMain}
        >
          <Text style={[styles.buttonText, localStyles.continueButtonText]}>
            Let's Begin
          </Text>
        </TouchableOpacity>

        {/* Auto-skip indicator */}
        <Text style={[styles.textSecondary, localStyles.autoSkipText]}>
          Continuing automatically in 3 seconds...
        </Text>
      </Animated.View>

      {/* Background decoration */}
      <View style={localStyles.backgroundDecoration}>
        <View style={[localStyles.circle, { backgroundColor: theme.colors.primary + '20' }]} />
        <View style={[localStyles.circle, localStyles.circle2, { backgroundColor: theme.colors.accent + '15' }]} />
      </View>
    </SafeAreaView>
  );
};

const localStyles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 40,
    zIndex: 1,
  },
  avatarContainer: {
    marginBottom: 30,
  },
  avatar: {
    fontSize: 80,
    textAlign: 'center',
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
  },
  quoteContainer: {
    marginBottom: 50,
    paddingHorizontal: 20,
  },
  quote: {
    fontSize: 16,
    fontStyle: 'italic',
    textAlign: 'center',
    lineHeight: 24,
  },
  continueButton: {
    paddingVertical: 16,
    paddingHorizontal: 40,
    marginBottom: 20,
  },
  continueButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  autoSkipText: {
    fontSize: 12,
    opacity: 0.7,
    textAlign: 'center',
  },
  backgroundDecoration: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
  },
  circle: {
    position: 'absolute',
    width: 200,
    height: 200,
    borderRadius: 100,
    top: 100,
    right: -50,
  },
  circle2: {
    width: 150,
    height: 150,
    borderRadius: 75,
    bottom: 150,
    left: -30,
    top: undefined,
    right: undefined,
  },
});

export default StartupGreetingScreen;
