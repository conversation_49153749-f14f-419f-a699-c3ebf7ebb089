/**
 * EventEmitter.ts
 * Simple event emitter for app-wide communication
 */

import { AppEvent, EventCallback } from '@/types';

export class EventEmitter {
  private listeners: Set<EventCallback> = new Set();

  /**
   * Subscribe to events
   */
  public subscribe(callback: EventCallback): () => void {
    this.listeners.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(callback);
    };
  }

  /**
   * Emit an event to all subscribers
   */
  public emit(event: AppEvent): void {
    this.listeners.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        console.error('Error in event callback:', error);
      }
    });
  }

  /**
   * Get number of active listeners
   */
  public getListenerCount(): number {
    return this.listeners.size;
  }

  /**
   * Clear all listeners
   */
  public clear(): void {
    this.listeners.clear();
  }
}

// Global event emitter instance
export const globalEventEmitter = new EventEmitter();
