# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 13ms]
      create-cmake-model 77ms
    create-module-model completed in 92ms
    [gap of 14ms]
    create-ARM64_V8A-model 12ms
    create-module-model
      create-cmake-model 68ms
    create-module-model completed in 77ms
    create-ARMEABI_V7A-model 10ms
    [gap of 18ms]
  create-initial-cxx-model completed in 240ms
  [gap of 15ms]
create_cxx_tasks completed in 256ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 73ms
    create-module-model completed in 80ms
    create-module-model
      create-cmake-model 76ms
    create-module-model completed in 86ms
    [gap of 32ms]
  create-initial-cxx-model completed in 228ms
create_cxx_tasks completed in 230ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 54ms
    create-module-model completed in 60ms
    create-module-model
      create-cmake-model 61ms
    create-module-model completed in 66ms
    [gap of 22ms]
  create-initial-cxx-model completed in 170ms
create_cxx_tasks completed in 172ms

