{"name": "jarvis-lite", "version": "1.0.0", "description": "JARVIS Lite - Offline Android AI Assistant", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "clean": "react-native clean-project-auto"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-sqlite-storage": "^6.0.1", "react-native-async-storage": "@react-native-async-storage/async-storage", "react-native-permissions": "^3.10.1", "react-native-tts": "^4.1.0", "react-native-vector-icons": "^10.0.2", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "^3.5.4", "react-native-device-info": "^10.11.0", "react-native-background-job": "^0.2.9", "react-native-push-notification": "^8.1.1", "react-native-fs": "^2.20.0", "date-fns": "^2.30.0", "react-native-chart-kit": "^6.12.0", "react-native-svg": "^13.14.0", "react-native-vosk": "^0.1.12", "react-native-system-setting": "^1.7.6", "react-native-intent-launcher": "^0.1.0", "moment": "^2.29.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.37.0", "@typescript-eslint/parser": "^5.37.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}, "keywords": ["react-native", "android", "ai-assistant", "offline", "voice-recognition", "jarvis"], "author": "JARVIS Lite Team", "license": "MIT"}