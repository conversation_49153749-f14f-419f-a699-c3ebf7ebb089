{"name": "jarvis-lite", "version": "1.0.0", "description": "JARVIS Lite - Offline Android AI Assistant", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.24.0", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "moment": "^2.30.1", "react": "18.2.0", "react-native": "0.72.6", "react-native-gesture-handler": "^2.25.0", "react-native-safe-area-context": "^4.14.1", "react-native-screens": "^3.37.0", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}