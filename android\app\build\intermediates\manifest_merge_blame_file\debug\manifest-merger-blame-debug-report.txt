1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.jarvislite"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->F:\jarvis\android\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->F:\jarvis\android\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->F:\jarvis\android\app\src\main\AndroidManifest.xml:4:5-67
11-->F:\jarvis\android\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->F:\jarvis\android\app\src\main\AndroidManifest.xml:5:5-71
12-->F:\jarvis\android\app\src\main\AndroidManifest.xml:5:22-68
13    <uses-permission android:name="android.permission.CALL_PHONE" />
13-->F:\jarvis\android\app\src\main\AndroidManifest.xml:6:5-69
13-->F:\jarvis\android\app\src\main\AndroidManifest.xml:6:22-66
14    <uses-permission android:name="android.permission.CAMERA" />
14-->F:\jarvis\android\app\src\main\AndroidManifest.xml:7:5-65
14-->F:\jarvis\android\app\src\main\AndroidManifest.xml:7:22-62
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->F:\jarvis\android\app\src\main\AndroidManifest.xml:8:5-81
15-->F:\jarvis\android\app\src\main\AndroidManifest.xml:8:22-78
16    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
16-->F:\jarvis\android\app\src\main\AndroidManifest.xml:9:5-80
16-->F:\jarvis\android\app\src\main\AndroidManifest.xml:9:22-77
17    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
17-->F:\jarvis\android\app\src\main\AndroidManifest.xml:10:5-76
17-->F:\jarvis\android\app\src\main\AndroidManifest.xml:10:22-73
18    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
18-->F:\jarvis\android\app\src\main\AndroidManifest.xml:11:5-76
18-->F:\jarvis\android\app\src\main\AndroidManifest.xml:11:22-73
19    <uses-permission android:name="android.permission.BLUETOOTH" />
19-->F:\jarvis\android\app\src\main\AndroidManifest.xml:12:5-68
19-->F:\jarvis\android\app\src\main\AndroidManifest.xml:12:22-65
20    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
20-->F:\jarvis\android\app\src\main\AndroidManifest.xml:13:5-74
20-->F:\jarvis\android\app\src\main\AndroidManifest.xml:13:22-71
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
21-->F:\jarvis\android\app\src\main\AndroidManifest.xml:14:5-78
21-->F:\jarvis\android\app\src\main\AndroidManifest.xml:14:22-75
22    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
22-->F:\jarvis\android\app\src\main\AndroidManifest.xml:15:5-78
22-->F:\jarvis\android\app\src\main\AndroidManifest.xml:15:22-75
23    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
23-->F:\jarvis\android\app\src\main\AndroidManifest.xml:16:5-85
23-->F:\jarvis\android\app\src\main\AndroidManifest.xml:16:22-82
24    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
24-->F:\jarvis\android\app\src\main\AndroidManifest.xml:17:5-77
24-->F:\jarvis\android\app\src\main\AndroidManifest.xml:17:22-74
25    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
25-->F:\jarvis\android\app\src\main\AndroidManifest.xml:18:5-73
25-->F:\jarvis\android\app\src\main\AndroidManifest.xml:18:22-70
26    <uses-permission android:name="android.permission.FLASHLIGHT" />
26-->F:\jarvis\android\app\src\main\AndroidManifest.xml:19:5-69
26-->F:\jarvis\android\app\src\main\AndroidManifest.xml:19:22-66
27    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
28
29    <application
29-->F:\jarvis\android\app\src\main\AndroidManifest.xml:21:5-40:19
30        android:name="com.jarvislite.MainApplication"
30-->F:\jarvis\android\app\src\main\AndroidManifest.xml:22:7-38
31        android:allowBackup="false"
31-->F:\jarvis\android\app\src\main\AndroidManifest.xml:26:7-34
32        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
32-->[androidx.core:core:1.7.0] F:\gradle-cache-new\caches\transforms-3\a8962bb5790e2a717af128f173a274ed\transformed\core-1.7.0\AndroidManifest.xml:24:18-86
33        android:debuggable="true"
34        android:icon="@mipmap/ic_launcher"
34-->F:\jarvis\android\app\src\main\AndroidManifest.xml:24:7-41
35        android:label="@string/app_name"
35-->F:\jarvis\android\app\src\main\AndroidManifest.xml:23:7-39
36        android:roundIcon="@mipmap/ic_launcher_round"
36-->F:\jarvis\android\app\src\main\AndroidManifest.xml:25:7-52
37        android:theme="@style/AppTheme" >
37-->F:\jarvis\android\app\src\main\AndroidManifest.xml:27:7-38
38        <activity
38-->F:\jarvis\android\app\src\main\AndroidManifest.xml:28:7-39:18
39            android:name="com.jarvislite.MainActivity"
39-->F:\jarvis\android\app\src\main\AndroidManifest.xml:29:9-37
40            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
40-->F:\jarvis\android\app\src\main\AndroidManifest.xml:31:9-118
41            android:exported="true"
41-->F:\jarvis\android\app\src\main\AndroidManifest.xml:34:9-32
42            android:label="@string/app_name"
42-->F:\jarvis\android\app\src\main\AndroidManifest.xml:30:9-41
43            android:launchMode="singleTask"
43-->F:\jarvis\android\app\src\main\AndroidManifest.xml:32:9-40
44            android:windowSoftInputMode="adjustResize" >
44-->F:\jarvis\android\app\src\main\AndroidManifest.xml:33:9-51
45            <intent-filter>
45-->F:\jarvis\android\app\src\main\AndroidManifest.xml:35:9-38:25
46                <action android:name="android.intent.action.MAIN" />
46-->F:\jarvis\android\app\src\main\AndroidManifest.xml:36:13-65
46-->F:\jarvis\android\app\src\main\AndroidManifest.xml:36:21-62
47
48                <category android:name="android.intent.category.LAUNCHER" />
48-->F:\jarvis\android\app\src\main\AndroidManifest.xml:37:13-73
48-->F:\jarvis\android\app\src\main\AndroidManifest.xml:37:23-70
49            </intent-filter>
50        </activity>
51
52        <provider
52-->[androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:26:9-34:20
53            android:name="androidx.startup.InitializationProvider"
53-->[androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:27:13-67
54            android:authorities="com.jarvislite.androidx-startup"
54-->[androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:28:13-68
55            android:exported="false" >
55-->[androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:29:13-37
56            <meta-data
56-->[androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:31:13-33:52
57                android:name="androidx.emoji2.text.EmojiCompatInitializer"
57-->[androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:32:17-75
58                android:value="androidx.startup" />
58-->[androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:33:17-49
59            <meta-data
59-->[androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache-new\caches\transforms-3\b7a4b4b32fa1a3f9ef62577eecee019c\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:31:13-33:52
60                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
60-->[androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache-new\caches\transforms-3\b7a4b4b32fa1a3f9ef62577eecee019c\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:32:17-78
61                android:value="androidx.startup" />
61-->[androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache-new\caches\transforms-3\b7a4b4b32fa1a3f9ef62577eecee019c\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:33:17-49
62        </provider>
63
64        <meta-data
64-->[com.facebook.soloader:soloader:0.10.5] F:\gradle-cache-new\caches\transforms-3\7a78738add03003b048c09f83d1118f1\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
65            android:name="com.facebook.soloader.enabled"
65-->[com.facebook.soloader:soloader:0.10.5] F:\gradle-cache-new\caches\transforms-3\7a78738add03003b048c09f83d1118f1\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:13:13-57
66            android:value="false" />
66-->[com.facebook.soloader:soloader:0.10.5] F:\gradle-cache-new\caches\transforms-3\7a78738add03003b048c09f83d1118f1\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:14:13-34
67    </application>
68
69</manifest>
