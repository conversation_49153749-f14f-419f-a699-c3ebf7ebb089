# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-ndk-meta-abi-list 99ms
      create-cmake-model 425ms
    create-module-model completed in 537ms
    create-ARMEABI_V7A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 12ms
    create-module-model
      create-cmake-model 277ms
    create-module-model completed in 287ms
    create-ARMEABI_V7A-model 10ms
    create-X86-model 13ms
    create-X86_64-model 24ms
  create-initial-cxx-model completed in 934ms
create_cxx_tasks completed in 938ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 112ms
    create-module-model completed in 122ms
    [gap of 33ms]
    create-X86_64-model 18ms
    create-module-model
      create-cmake-model 93ms
    create-module-model completed in 100ms
    [gap of 36ms]
  create-initial-cxx-model completed in 309ms
create_cxx_tasks completed in 313ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 83ms
    create-module-model completed in 91ms
    create-module-model
      create-cmake-model 103ms
    create-module-model completed in 112ms
    [gap of 34ms]
  create-initial-cxx-model completed in 269ms
create_cxx_tasks completed in 271ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 62ms
    create-module-model completed in 69ms
    create-module-model
      create-cmake-model 70ms
    create-module-model completed in 76ms
    [gap of 24ms]
  create-initial-cxx-model completed in 196ms
create_cxx_tasks completed in 199ms

