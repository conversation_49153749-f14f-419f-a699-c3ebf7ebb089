{"compilerOptions": {"target": "es2017", "lib": ["es2017", "es6", "dom"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@screens/*": ["src/screens/*"], "@services/*": ["src/services/*"], "@utils/*": ["src/utils/*"], "@types/*": ["src/types/*"], "@assets/*": ["src/assets/*"]}}, "include": ["src/**/*", "*.ts", "*.tsx", "index.js"], "exclude": ["node_modules", "android", "ios", "**/*.test.ts", "**/*.test.tsx"]}