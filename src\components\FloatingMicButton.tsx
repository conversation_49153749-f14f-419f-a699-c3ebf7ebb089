/**
 * FloatingMicButton.tsx
 * Floating microphone button for voice input
 */

import React, { useRef, useEffect } from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  Animated,
  View,
} from 'react-native';

import { VoiceProcessorState } from '@/types';
import { useTheme } from '@/utils/ThemeContext';

interface FloatingMicButtonProps {
  isListening: boolean;
  isProcessing: boolean;
  onPress: () => void;
  voiceState: VoiceProcessorState;
}

const FloatingMicButton: React.FC<FloatingMicButtonProps> = ({
  isListening,
  isProcessing,
  onPress,
  voiceState,
}) => {
  const { theme } = useTheme();
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isListening) {
      startListeningAnimation();
    } else {
      stopListeningAnimation();
    }
  }, [isListening]);

  useEffect(() => {
    if (isProcessing) {
      startProcessingAnimation();
    } else {
      stopProcessingAnimation();
    }
  }, [isProcessing]);

  const startListeningAnimation = () => {
    // Pulse animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Glow animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(glowAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(glowAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const stopListeningAnimation = () => {
    pulseAnim.stopAnimation();
    glowAnim.stopAnimation();
    
    Animated.parallel([
      Animated.timing(pulseAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(glowAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const startProcessingAnimation = () => {
    Animated.loop(
      Animated.timing(pulseAnim, {
        toValue: 0.9,
        duration: 800,
        useNativeDriver: true,
      })
    ).start();
  };

  const stopProcessingAnimation = () => {
    pulseAnim.stopAnimation();
    Animated.timing(pulseAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const getButtonColor = () => {
    if (voiceState === VoiceProcessorState.ERROR) {
      return theme.colors.error;
    }
    if (isListening) {
      return theme.colors.success;
    }
    if (isProcessing) {
      return theme.colors.warning;
    }
    return theme.colors.primary;
  };

  const getButtonIcon = () => {
    if (voiceState === VoiceProcessorState.ERROR) {
      return '❌';
    }
    if (isListening) {
      return '🎤';
    }
    if (isProcessing) {
      return '⏳';
    }
    return '🎙️';
  };

  const getStatusText = () => {
    if (voiceState === VoiceProcessorState.ERROR) {
      return 'Error';
    }
    if (isListening) {
      return 'Listening...';
    }
    if (isProcessing) {
      return 'Processing...';
    }
    return 'Tap to speak';
  };

  return (
    <View style={localStyles.container}>
      {/* Glow effect */}
      <Animated.View
        style={[
          localStyles.glowEffect,
          {
            opacity: glowAnim,
            backgroundColor: getButtonColor(),
          },
        ]}
      />
      
      {/* Main button */}
      <Animated.View
        style={[
          localStyles.buttonContainer,
          {
            transform: [{ scale: pulseAnim }],
          },
        ]}
      >
        <TouchableOpacity
          style={[
            localStyles.button,
            {
              backgroundColor: getButtonColor(),
              shadowColor: getButtonColor(),
            },
          ]}
          onPress={onPress}
          activeOpacity={0.8}
          disabled={isProcessing}
        >
          <Text style={localStyles.icon}>
            {getButtonIcon()}
          </Text>
        </TouchableOpacity>
      </Animated.View>

      {/* Status text */}
      <Text style={[localStyles.statusText, { color: theme.colors.textSecondary }]}>
        {getStatusText()}
      </Text>
    </View>
  );
};

const localStyles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  glowEffect: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 50,
    top: -10,
  },
  buttonContainer: {
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  button: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    fontSize: 32,
  },
  statusText: {
    marginTop: 12,
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default FloatingMicButton;
