# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 55ms]
      create-cmake-model 230ms
    create-module-model completed in 288ms
    create-module-model
      create-cmake-model 65ms
    create-module-model completed in 72ms
    [gap of 36ms]
  create-initial-cxx-model completed in 433ms
  [gap of 16ms]
create_cxx_tasks completed in 449ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 74ms
    create-module-model completed in 82ms
    [gap of 18ms]
    create-X86-model 10ms
    create-module-model
      create-cmake-model 79ms
    create-module-model completed in 85ms
    [gap of 32ms]
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 244ms
create_cxx_tasks completed in 251ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 80ms
    create-module-model completed in 90ms
    [gap of 19ms]
    create-X86-model 10ms
    create-module-model
      create-cmake-model 104ms
    create-module-model completed in 110ms
    [gap of 40ms]
  create-initial-cxx-model completed in 276ms
create_cxx_tasks completed in 281ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 95ms
    create-module-model completed in 103ms
    create-module-model
      create-cmake-model 86ms
    create-module-model completed in 94ms
    [gap of 35ms]
  create-initial-cxx-model completed in 262ms
create_cxx_tasks completed in 269ms

