/**
 * VoiceProcessor.ts
 * Voice processing service for offline speech-to-text
 * Handles microphone input and speech recognition
 */

import { NativeEventEmitter, NativeModules, PermissionsAndroid, Platform } from 'react-native';
import { VoiceProcessorState, SpeechResult, VoiceProcessorError } from '@/types';

const { VoiceProcessorModule } = NativeModules;

if (!VoiceProcessorModule && Platform.OS === 'android') {
  console.warn(
    "VoiceProcessorModule: Native module not found. Did you forget to link it?"
  );
}

/**
 * Defines the events emitted by the VoiceProcessor.
 */
export type VoiceProcessorEvent =
  | { type: 'onSpeechResult'; payload: SpeechResult }
  | { type: 'onSpeechStart'; payload?: undefined }
  | { type: 'onSpeechEnd'; payload?: undefined }
  | { type: 'onSpeechPartialResult'; payload: SpeechResult }
  | { type: 'onError'; payload: VoiceProcessorError }
  | { type: 'onStateChanged'; payload: VoiceProcessorState };

type EventCallback = (event: VoiceProcessorEvent) => void;

class VoiceProcessor {
  private eventEmitter: NativeEventEmitter | null = null;
  private listeners: Set<EventCallback> = new Set();
  private currentState: VoiceProcessorState = VoiceProcessorState.IDLE;
  private isInitialized = false;

  constructor() {
    if (VoiceProcessorModule) {
      this.eventEmitter = new NativeEventEmitter(VoiceProcessorModule);
      this.setupEventListeners();
    }
  }

  /**
   * Setup native event listeners
   */
  private setupEventListeners(): void {
    if (!this.eventEmitter) return;

    this.eventEmitter.addListener('onSpeechResult', (data: SpeechResult) => 
      this.emit({ type: 'onSpeechResult', payload: data })
    );
    
    this.eventEmitter.addListener('onSpeechStart', () => {
      this.updateState(VoiceProcessorState.LISTENING);
      this.emit({ type: 'onSpeechStart' });
    });
    
    this.eventEmitter.addListener('onSpeechEnd', () => {
      this.updateState(VoiceProcessorState.IDLE);
      this.emit({ type: 'onSpeechEnd' });
    });
    
    this.eventEmitter.addListener('onSpeechPartialResult', (data: SpeechResult) => 
      this.emit({ type: 'onSpeechPartialResult', payload: data })
    );
    
    this.eventEmitter.addListener('onError', (error: VoiceProcessorError) => {
      this.updateState(VoiceProcessorState.ERROR);
      this.emit({ type: 'onError', payload: error });
    });
  }

  /**
   * Update internal state and notify listeners
   */
  private updateState(newState: VoiceProcessorState): void {
    if (this.currentState !== newState) {
      this.currentState = newState;
      this.emit({ type: 'onStateChanged', payload: this.currentState });
    }
  }

  /**
   * Emit event to all listeners
   */
  private emit(event: VoiceProcessorEvent): void {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in voice processor event listener:', error);
      }
    });
  }

  /**
   * Subscribe to voice processor events
   */
  public onEvent(callback: EventCallback): () => void {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  /**
   * Initialize the voice processor
   */
  public async initialize(): Promise<boolean> {
    try {
      console.log('VoiceProcessor: Initializing...');
      
      // Check microphone permission
      const hasPermission = await this.checkMicrophonePermission();
      if (!hasPermission) {
        console.warn('VoiceProcessor: Microphone permission not granted');
        this.updateState(VoiceProcessorState.ERROR);
        return false;
      }

      // Initialize native module if available
      if (VoiceProcessorModule) {
        try {
          const success = await VoiceProcessorModule.initializeEngine();
          if (success) {
            this.updateState(VoiceProcessorState.IDLE);
            this.isInitialized = true;
            console.log('VoiceProcessor: Initialized successfully');
            return true;
          }
        } catch (error) {
          console.error('VoiceProcessor: Native initialization failed:', error);
        }
      }

      // Fallback: Mock initialization for development
      console.log('VoiceProcessor: Using mock implementation');
      this.isInitialized = true;
      this.updateState(VoiceProcessorState.IDLE);
      return true;

    } catch (error) {
      console.error('VoiceProcessor: Initialization failed:', error);
      this.updateState(VoiceProcessorState.ERROR);
      return false;
    }
  }

  /**
   * Start listening for voice input
   */
  public async startListening(continuous: boolean = false): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('VoiceProcessor not initialized');
    }

    try {
      console.log(`VoiceProcessor: Start listening (continuous: ${continuous})`);
      
      // Check permission again
      const hasPermission = await this.checkMicrophonePermission();
      if (!hasPermission) {
        throw new Error('Microphone permission required');
      }

      this.updateState(VoiceProcessorState.LISTENING);

      if (VoiceProcessorModule) {
        await VoiceProcessorModule.startListening(continuous);
      } else {
        // Mock implementation for development
        this.simulateVoiceInput();
      }

    } catch (error) {
      console.error('VoiceProcessor: Failed to start listening:', error);
      this.updateState(VoiceProcessorState.ERROR);
      this.emit({ 
        type: 'onError', 
        payload: { 
          code: 'start_listening_failed', 
          message: error.message 
        } 
      });
    }
  }

  /**
   * Stop listening for voice input
   */
  public async stopListening(): Promise<void> {
    try {
      console.log('VoiceProcessor: Stop listening');
      
      if (VoiceProcessorModule) {
        await VoiceProcessorModule.stopListening();
      }
      
      this.updateState(VoiceProcessorState.IDLE);

    } catch (error) {
      console.error('VoiceProcessor: Failed to stop listening:', error);
      this.updateState(VoiceProcessorState.ERROR);
    }
  }

  /**
   * Get current processor state
   */
  public getCurrentState(): VoiceProcessorState {
    return this.currentState;
  }

  /**
   * Check if processor is initialized
   */
  public isReady(): boolean {
    return this.isInitialized && this.currentState !== VoiceProcessorState.ERROR;
  }

  /**
   * Check microphone permission
   */
  private async checkMicrophonePermission(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return true; // Assume granted on other platforms
    }

    try {
      const granted = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO
      );
      
      if (!granted) {
        const result = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
          {
            title: 'Microphone Permission',
            message: 'JARVIS Lite needs microphone access for voice commands',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );
        
        return result === PermissionsAndroid.RESULTS.GRANTED;
      }
      
      return true;
    } catch (error) {
      console.error('Error checking microphone permission:', error);
      return false;
    }
  }

  /**
   * Simulate voice input for development/testing
   */
  private simulateVoiceInput(): void {
    // Simulate speech start
    setTimeout(() => {
      this.emit({ type: 'onSpeechStart' });
    }, 500);

    // Simulate partial results
    const testPhrases = [
      'Hello',
      'Hello JARVIS',
      'Hello JARVIS what time is it',
    ];

    testPhrases.forEach((phrase, index) => {
      setTimeout(() => {
        this.emit({
          type: 'onSpeechPartialResult',
          payload: {
            text: phrase,
            isFinal: false,
            confidence: 0.8 + (index * 0.1),
          },
        });
      }, 1000 + (index * 500));
    });

    // Simulate final result
    setTimeout(() => {
      this.emit({
        type: 'onSpeechResult',
        payload: {
          text: 'what time is it',
          isFinal: true,
          confidence: 0.95,
        },
      });
      
      this.emit({ type: 'onSpeechEnd' });
    }, 3000);
  }

  /**
   * Cleanup resources
   */
  public cleanup(): void {
    try {
      if (this.eventEmitter) {
        this.eventEmitter.removeAllListeners('onSpeechResult');
        this.eventEmitter.removeAllListeners('onSpeechStart');
        this.eventEmitter.removeAllListeners('onSpeechEnd');
        this.eventEmitter.removeAllListeners('onSpeechPartialResult');
        this.eventEmitter.removeAllListeners('onError');
      }
      
      this.listeners.clear();
      this.isInitialized = false;
      this.updateState(VoiceProcessorState.IDLE);
      
      console.log('VoiceProcessor: Cleanup completed');
    } catch (error) {
      console.error('VoiceProcessor: Error during cleanup:', error);
    }
  }
}

export const voiceProcessor = new VoiceProcessor();
