/**
 * VoiceProcessor.ts
 * Voice processing service for offline speech-to-text using Vosk
 * Handles microphone input and speech recognition
 */

import { NativeModules, NativeEventEmitter, Platform, PermissionsAndroid } from 'react-native';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import { VoiceProcessorState, SpeechResult, VoiceProcessorError } from '@/types';

const { Vosk } = NativeModules;
const voskEvents = Vosk ? new NativeEventEmitter(Vosk) : null;

let isEngineReady = false;
let isCurrentlyListening = false;

const MODEL_PATH = 'models/vosk-model-small-en-us-0.15';

type StateCallback = (text: string) => void;

let onResultCallback: StateCallback | null = null;

/**
 * Defines the events emitted by the VoiceProcessor.
 */
export type VoiceProcessorEvent =
  | { type: 'onSpeechResult'; payload: SpeechResult }
  | { type: 'onSpeechStart'; payload?: undefined }
  | { type: 'onSpeechEnd'; payload?: undefined }
  | { type: 'onSpeechPartialResult'; payload: SpeechResult }
  | { type: 'onError'; payload: VoiceProcessorError }
  | { type: 'onStateChanged'; payload: VoiceProcessorState };

type EventCallback = (event: VoiceProcessorEvent) => void;

class VoiceProcessor {
  private listeners: Set<EventCallback> = new Set();
  private currentState: VoiceProcessorState = VoiceProcessorState.IDLE;

  constructor() {
    this.setupEventListeners();
  }

  /**
   * Setup native event listeners
   */
  private setupEventListeners(): void {
    if (!voskEvents) return;

    voskEvents.addListener('onResult', (event: any) => {
      if (event && event.text) {
        console.log('[VoiceProcessor] Recognized:', event.text);
        this.emit({
          type: 'onSpeechResult',
          payload: {
            text: event.text,
            isFinal: true,
            confidence: 0.95
          }
        });
        if (onResultCallback) onResultCallback(event.text);
      }
    });

    voskEvents.addListener('onPartialResult', (event: any) => {
      if (event && event.text) {
        this.emit({
          type: 'onSpeechPartialResult',
          payload: {
            text: event.text,
            isFinal: false,
            confidence: 0.7
          }
        });
      }
    });

    voskEvents.addListener('onError', (error: any) => {
      this.updateState(VoiceProcessorState.ERROR);
      this.emit({ type: 'onError', payload: { code: 'vosk_error', message: error.message || 'Unknown error' } });
    });
  }

  /**
   * Update internal state and notify listeners
   */
  private updateState(newState: VoiceProcessorState): void {
    if (this.currentState !== newState) {
      this.currentState = newState;
      this.emit({ type: 'onStateChanged', payload: this.currentState });
    }
  }

  /**
   * Emit event to all listeners
   */
  private emit(event: VoiceProcessorEvent): void {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in voice processor event listener:', error);
      }
    });
  }

  /**
   * Subscribe to voice processor events
   */
  public onEvent(callback: EventCallback): () => void {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  /**
   * Initialize the voice processor with Vosk model
   */
  public async initialize(): Promise<boolean> {
    try {
      console.log('[VoiceProcessor] Initializing Vosk...');

      // Check microphone permission
      const hasPermission = await this.checkMicrophonePermission();
      if (!hasPermission) {
        console.warn('[VoiceProcessor] Microphone permission not granted');
        this.updateState(VoiceProcessorState.ERROR);
        return false;
      }

      // Initialize Vosk model if available
      if (Vosk && Platform.OS === 'android') {
        try {
          await Vosk.setLogLevel(0);
          await Vosk.loadModel(MODEL_PATH);
          isEngineReady = true;
          this.updateState(VoiceProcessorState.IDLE);
          console.log('[VoiceProcessor] Vosk model loaded successfully');
          return true;
        } catch (error) {
          console.error('[VoiceProcessor] Failed to load Vosk model:', error);
        }
      }

      // Fallback: Mock initialization for development
      console.log('[VoiceProcessor] Using mock implementation (Vosk not available)');
      isEngineReady = true;
      this.updateState(VoiceProcessorState.IDLE);
      return true;

    } catch (error) {
      console.error('[VoiceProcessor] Initialization failed:', error);
      this.updateState(VoiceProcessorState.ERROR);
      return false;
    }
  }

  /**
   * Start listening for voice input with callback
   */
  public async startListening(callback: StateCallback): Promise<void> {
    if (!isEngineReady) {
      console.warn('[VoiceProcessor] Engine not ready.');
      return;
    }

    const micGranted = await this.checkMicrophonePermission();
    if (!micGranted) {
      console.warn('[VoiceProcessor] Mic permission denied.');
      this.emit({
        type: 'onError',
        payload: {
          code: 'permission_denied',
          message: 'Microphone permission denied'
        }
      });
      return;
    }

    if (isCurrentlyListening) {
      console.log('[VoiceProcessor] Already listening.');
      return;
    }

    onResultCallback = callback;

    try {
      if (Vosk) {
        await Vosk.start();
        isCurrentlyListening = true;
        this.updateState(VoiceProcessorState.LISTENING);
        this.emit({ type: 'onSpeechStart' });
        console.log('[VoiceProcessor] Listening started.');
      } else {
        // Mock implementation for development
        this.simulateVoiceInput();
      }
    } catch (err) {
      console.error('[VoiceProcessor] Failed to start:', err);
      this.emit({
        type: 'onError',
        payload: {
          code: 'start_failed',
          message: err.message || 'Failed to start listening'
        }
      });
    }
  }

  /**
   * Stop listening for voice input
   */
  public async stopListening(): Promise<void> {
    try {
      console.log('[VoiceProcessor] Stop listening');

      if (Vosk) {
        await Vosk.stop();
      }

      isCurrentlyListening = false;
      onResultCallback = null;
      this.updateState(VoiceProcessorState.IDLE);
      this.emit({ type: 'onSpeechEnd' });

    } catch (error) {
      console.error('[VoiceProcessor] Failed to stop listening:', error);
      this.updateState(VoiceProcessorState.ERROR);
    }
  }

  /**
   * Check if currently listening
   */
  public isListening(): boolean {
    return isCurrentlyListening;
  }

  /**
   * Get current processor state
   */
  public getCurrentState(): VoiceProcessorState {
    return this.currentState;
  }

  /**
   * Check if processor is ready
   */
  public isReady(): boolean {
    return isEngineReady && this.currentState !== VoiceProcessorState.ERROR;
  }

  /**
   * Check microphone permission
   */
  private async checkMicrophonePermission(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return true; // Assume granted on other platforms
    }

    try {
      const result = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        {
          title: 'Microphone Permission',
          message: 'JARVIS Lite needs microphone access for voice commands',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );

      return result === PermissionsAndroid.RESULTS.GRANTED;
    } catch (error) {
      console.error('Error checking microphone permission:', error);
      return false;
    }
  }

  /**
   * Simulate voice input for development/testing
   */
  private simulateVoiceInput(): void {
    isCurrentlyListening = true;
    this.updateState(VoiceProcessorState.LISTENING);
    this.emit({ type: 'onSpeechStart' });

    // Simulate partial results
    const testPhrases = [
      'Hello',
      'Hello JARVIS',
      'Hello JARVIS what time is it',
    ];

    testPhrases.forEach((phrase, index) => {
      setTimeout(() => {
        this.emit({
          type: 'onSpeechPartialResult',
          payload: {
            text: phrase,
            isFinal: false,
            confidence: 0.8 + (index * 0.1),
          },
        });
      }, 1000 + (index * 500));
    });

    // Simulate final result
    setTimeout(() => {
      const finalText = 'what time is it';
      this.emit({
        type: 'onSpeechResult',
        payload: {
          text: finalText,
          isFinal: true,
          confidence: 0.95,
        },
      });

      if (onResultCallback) onResultCallback(finalText);

      isCurrentlyListening = false;
      this.updateState(VoiceProcessorState.IDLE);
      this.emit({ type: 'onSpeechEnd' });
    }, 3000);
  }

  /**
   * Cleanup resources
   */
  public cleanup(): void {
    try {
      if (voskEvents) {
        voskEvents.removeAllListeners('onResult');
        voskEvents.removeAllListeners('onPartialResult');
        voskEvents.removeAllListeners('onError');
      }

      this.listeners.clear();
      isEngineReady = false;
      isCurrentlyListening = false;
      onResultCallback = null;
      this.updateState(VoiceProcessorState.IDLE);

      console.log('[VoiceProcessor] Cleanup completed');
    } catch (error) {
      console.error('[VoiceProcessor] Error during cleanup:', error);
    }
  }
}

// Export standalone functions for compatibility with prompt2.txt API
export async function initializeVoskModel(): Promise<void> {
  await voiceProcessor.initialize();
}

export async function requestMicPermission(): Promise<boolean> {
  return await voiceProcessor['checkMicrophonePermission']();
}

export async function startListening(callback: StateCallback): Promise<void> {
  return await voiceProcessor.startListening(callback);
}

export async function stopListening(): Promise<void> {
  return await voiceProcessor.stopListening();
}

export function isListening(): boolean {
  return voiceProcessor.isListening();
}

export const voiceProcessor = new VoiceProcessor();
