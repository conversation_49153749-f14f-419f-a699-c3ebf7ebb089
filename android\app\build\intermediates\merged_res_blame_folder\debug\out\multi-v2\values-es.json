{"logs": [{"outputFile": "com.jarvislite.app-mergeDebugResources-29:/values-es/values-es.xml", "map": [{"source": "F:\\gradle-cache\\caches\\transforms-3\\ab12076093e54ca9ba43148476352294\\transformed\\appcompat-1.4.2\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "283,385,498,606,691,792,920,1006,1087,1179,1273,1370,1464,1564,1658,1754,1850,1942,2034,2116,2223,2334,2433,2541,2649,2756,2915,7450", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "380,493,601,686,787,915,1001,1082,1174,1268,1365,1459,1559,1653,1749,1845,1937,2029,2111,2218,2329,2428,2536,2644,2751,2910,3009,7528"}}, {"source": "F:\\gradle-cache\\caches\\transforms-3\\6c9de45700a7851c879b33c8d726a07f\\transformed\\core-1.8.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7533", "endColumns": "100", "endOffsets": "7629"}}, {"source": "F:\\gradle-cache\\caches\\transforms-3\\c572b377194a8a385981f85402606c3e\\transformed\\material-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,233,320,424,546,627,692,787,868,931,1020,1089,1152,1226,1290,1346,1464,1522,1584,1640,1720,1859,1948,2030,2141,2222,2302,2392,2459,2525,2604,2686,2774,2848,2925,2995,3074,3158,3242,3334,3434,3508,3589,3691,3744,3811,3904,3993,4055,4119,4182,4295,4388,4492,4586", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,86,103,121,80,64,94,80,62,88,68,62,73,63,55,117,57,61,55,79,138,88,81,110,80,79,89,66,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,82", "endOffsets": "228,315,419,541,622,687,782,863,926,1015,1084,1147,1221,1285,1341,1459,1517,1579,1635,1715,1854,1943,2025,2136,2217,2297,2387,2454,2520,2599,2681,2769,2843,2920,2990,3069,3153,3237,3329,3429,3503,3584,3686,3739,3806,3899,3988,4050,4114,4177,4290,4383,4487,4581,4664"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3014,3101,3205,3327,3408,3473,3568,3649,3712,3801,3870,3933,4007,4071,4127,4245,4303,4365,4421,4501,4640,4729,4811,4922,5003,5083,5173,5240,5306,5385,5467,5555,5629,5706,5776,5855,5939,6023,6115,6215,6289,6370,6472,6525,6592,6685,6774,6836,6900,6963,7076,7169,7273,7367", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,86,103,121,80,64,94,80,62,88,68,62,73,63,55,117,57,61,55,79,138,88,81,110,80,79,89,66,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,82", "endOffsets": "278,3096,3200,3322,3403,3468,3563,3644,3707,3796,3865,3928,4002,4066,4122,4240,4298,4360,4416,4496,4635,4724,4806,4917,4998,5078,5168,5235,5301,5380,5462,5550,5624,5701,5771,5850,5934,6018,6110,6210,6284,6365,6467,6520,6587,6680,6769,6831,6895,6958,7071,7164,7268,7362,7445"}}]}]}