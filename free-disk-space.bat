@echo off
echo ========================================
echo JARVIS Lite - Disk Space Cleanup
echo ========================================
echo.

echo 🚨 DISK SPACE ISSUE
echo NDK installation requires ~2GB free space on C: drive
echo.

echo Current disk space:
for /f "tokens=3" %%a in ('dir C:\ /-c ^| find "bytes free"') do echo Free space: %%a bytes

echo.
echo ========================================
echo CLEANUP OPTIONS
echo ========================================
echo.

echo 1. Clean temporary files
echo 2. Clean Windows Update cache
echo 3. Clean browser cache
echo 4. Clean old Android SDK versions
echo 5. Move Android SDK to different drive
echo.

echo Option 1: Clean temporary files
echo Cleaning temp files...
del /q /f /s "%TEMP%\*" 2>nul
del /q /f /s "C:\Windows\Temp\*" 2>nul
echo ✅ Temp files cleaned

echo.
echo Option 2: Clean Windows Update cache
echo Cleaning Windows Update cache...
dism /online /cleanup-image /startcomponentcleanup /resetbase
echo ✅ Windows Update cache cleaned

echo.
echo Option 3: Clean Android SDK cache
if exist "C:\Users\<USER>\AppData\Local\Android\Sdk\.temp" (
    echo Cleaning Android SDK temp files...
    rmdir /s /q "C:\Users\<USER>\AppData\Local\Android\Sdk\.temp"
    echo ✅ Android SDK temp cleaned
)

echo.
echo ========================================
echo ALTERNATIVE: Move Android SDK
echo ========================================
echo.
echo If you have another drive with more space (D:, E:, etc.):
echo 1. Move entire Android SDK folder to new location
echo 2. Update ANDROID_HOME environment variable
echo 3. Update Android Studio SDK path
echo.

echo Current Android SDK location:
echo C:\Users\<USER>\AppData\Local\Android\Sdk
echo.

echo After cleanup, check free space again:
for /f "tokens=3" %%a in ('dir C:\ /-c ^| find "bytes free"') do echo Free space: %%a bytes

echo.
echo If you now have 2GB+ free, you can install NDK:
echo 1. Open Android Studio
echo 2. Tools → SDK Manager → SDK Tools
echo 3. Install "NDK (Side by side)"
echo.

pause
