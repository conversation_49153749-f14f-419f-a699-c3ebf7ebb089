# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 11ms]
      create-cmake-model 84ms
    create-module-model completed in 96ms
    create-module-model
      create-cmake-model 81ms
    create-module-model completed in 87ms
    [gap of 32ms]
  create-initial-cxx-model completed in 245ms
create_cxx_tasks completed in 248ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 49ms
    create-module-model completed in 54ms
    create-module-model
      [gap of 16ms]
      create-cmake-model 50ms
    create-module-model completed in 67ms
    [gap of 20ms]
  create-initial-cxx-model completed in 162ms
create_cxx_tasks completed in 166ms

