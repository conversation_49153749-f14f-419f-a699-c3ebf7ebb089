/**
 * WakeWordDetector.ts
 * Continuous wake word detection for hands-free activation
 * Listens for "Hey JARVIS" or "OK JARVIS" in the background
 */

import { NativeModules, NativeEventEmitter, AppState, Platform } from 'react-native';

// Mock implementations for development
const BackgroundJob = {
  register: (options: any) => console.log('[BackgroundJob] Register:', options),
  start: (options: any) => console.log('[BackgroundJob] Start:', options),
  stop: (options: any) => console.log('[BackgroundJob] Stop:', options),
  on: (event: string, callback: Function) => console.log('[BackgroundJob] On:', event),
};

// Mock functions
async function requestMicPermission(): Promise<boolean> {
  console.log('[WakeWordDetector] Mock mic permission granted');
  return true;
}

async function speak(text: string): Promise<void> {
  console.log('[WakeWordDetector] Would speak:', text);
}

const { WakeWordModule } = NativeModules;
const wakeWordEvents = WakeWordModule ? new NativeEventEmitter(WakeWordModule) : null;

interface WakeWordConfig {
  enabled: boolean;
  sensitivity: number; // 0.0 - 1.0
  keywords: string[];
  backgroundMode: boolean;
}

let isDetectionActive = false;
let currentConfig: WakeWordConfig = {
  enabled: false,
  sensitivity: 0.7,
  keywords: ['hey jarvis', 'ok jarvis', 'jarvis'],
  backgroundMode: true,
};

let onWakeWordCallback: (() => void) | null = null;

export async function initializeWakeWordDetector(): Promise<boolean> {
  try {
    console.log('[WakeWordDetector] Initializing...');

    // Check microphone permission
    const hasPermission = await requestMicPermission();
    if (!hasPermission) {
      console.warn('[WakeWordDetector] Microphone permission denied');
      return false;
    }

    // Setup native event listeners if available
    if (wakeWordEvents) {
      setupNativeEventListeners();
    }

    // Setup background job for continuous detection
    setupBackgroundDetection();

    console.log('[WakeWordDetector] Initialized successfully');
    return true;
  } catch (error) {
    console.error('[WakeWordDetector] Initialization failed:', error);
    return false;
  }
}

function setupNativeEventListeners(): void {
  if (!wakeWordEvents) return;

  wakeWordEvents.addListener('onWakeWordDetected', (event: any) => {
    console.log('[WakeWordDetector] Wake word detected:', event.keyword);
    handleWakeWordDetected(event.keyword);
  });

  wakeWordEvents.addListener('onError', (error: any) => {
    console.error('[WakeWordDetector] Native error:', error);
    // Fallback to software detection
    startSoftwareDetection();
  });
}

function setupBackgroundDetection(): void {
  // Register background job for wake word detection
  BackgroundJob.register({
    jobKey: 'wakeWordDetection',
    requiredNetworkType: 'none',
    requiresCharging: false,
    requiresDeviceIdle: false,
  });

  // Handle app state changes
  AppState.addEventListener('change', handleAppStateChange);
}

function handleAppStateChange(nextAppState: string): void {
  if (currentConfig.enabled && currentConfig.backgroundMode) {
    if (nextAppState === 'background') {
      startBackgroundDetection();
    } else if (nextAppState === 'active') {
      stopBackgroundDetection();
      if (currentConfig.enabled) {
        startForegroundDetection();
      }
    }
  }
}

export async function startWakeWordDetection(callback: () => void): Promise<boolean> {
  if (isDetectionActive) {
    console.log('[WakeWordDetector] Detection already active');
    return true;
  }

  onWakeWordCallback = callback;

  try {
    if (WakeWordModule && Platform.OS === 'android') {
      // Use native wake word detection
      await WakeWordModule.startDetection({
        keywords: currentConfig.keywords,
        sensitivity: currentConfig.sensitivity,
      });
    } else {
      // Fallback to software detection
      startSoftwareDetection();
    }

    isDetectionActive = true;
    console.log('[WakeWordDetector] Detection started');
    return true;
  } catch (error) {
    console.error('[WakeWordDetector] Failed to start detection:', error);
    return false;
  }
}

export async function stopWakeWordDetection(): Promise<void> {
  if (!isDetectionActive) return;

  try {
    if (WakeWordModule) {
      await WakeWordModule.stopDetection();
    }

    stopBackgroundDetection();
    isDetectionActive = false;
    onWakeWordCallback = null;

    console.log('[WakeWordDetector] Detection stopped');
  } catch (error) {
    console.error('[WakeWordDetector] Failed to stop detection:', error);
  }
}

function startForegroundDetection(): void {
  if (WakeWordModule) {
    WakeWordModule.startDetection({
      keywords: currentConfig.keywords,
      sensitivity: currentConfig.sensitivity,
    });
  } else {
    startSoftwareDetection();
  }
}

function startBackgroundDetection(): void {
  if (!currentConfig.backgroundMode) return;

  BackgroundJob.start({
    jobKey: 'wakeWordDetection',
    period: 5000, // Check every 5 seconds
  });

  BackgroundJob.on('wakeWordDetection', () => {
    // Simplified background detection
    // In a real implementation, this would use a lightweight wake word model
    console.log('[WakeWordDetector] Background detection tick');
  });
}

function stopBackgroundDetection(): void {
  BackgroundJob.stop({
    jobKey: 'wakeWordDetection',
  });
}

function startSoftwareDetection(): void {
  // Software-based wake word detection using continuous voice processing
  // This is a simplified implementation - in production, you'd use a dedicated wake word model
  console.log('[WakeWordDetector] Starting software detection (mock)');

  // Mock detection for development
  if (__DEV__) {
    setTimeout(() => {
      console.log('[WakeWordDetector] Mock wake word detected');
      handleWakeWordDetected('hey jarvis');
    }, 10000); // Simulate detection after 10 seconds
  }
}

function handleWakeWordDetected(keyword: string): void {
  console.log(`[WakeWordDetector] Wake word detected: "${keyword}"`);

  // Provide audio feedback
  speak('Yes?');

  // Trigger callback
  if (onWakeWordCallback) {
    onWakeWordCallback();
  }

  // Emit system-wide event
  // You could use EventEmitter here to notify other parts of the app
}

export function updateWakeWordConfig(config: Partial<WakeWordConfig>): void {
  currentConfig = { ...currentConfig, ...config };
  console.log('[WakeWordDetector] Config updated:', currentConfig);

  // Restart detection with new config if active
  if (isDetectionActive) {
    stopWakeWordDetection().then(() => {
      if (onWakeWordCallback) {
        startWakeWordDetection(onWakeWordCallback);
      }
    });
  }
}

export function getWakeWordConfig(): WakeWordConfig {
  return { ...currentConfig };
}

export function isWakeWordDetectionActive(): boolean {
  return isDetectionActive;
}

export async function testWakeWordDetection(): Promise<void> {
  console.log('[WakeWordDetector] Testing wake word detection...');

  // Simulate wake word detection for testing
  setTimeout(() => {
    handleWakeWordDetected('test keyword');
  }, 2000);
}

export function addWakeWord(keyword: string): void {
  if (!currentConfig.keywords.includes(keyword.toLowerCase())) {
    currentConfig.keywords.push(keyword.toLowerCase());
    console.log('[WakeWordDetector] Added wake word:', keyword);
  }
}

export function removeWakeWord(keyword: string): void {
  const index = currentConfig.keywords.indexOf(keyword.toLowerCase());
  if (index > -1) {
    currentConfig.keywords.splice(index, 1);
    console.log('[WakeWordDetector] Removed wake word:', keyword);
  }
}

export function getWakeWordStats(): {
  isActive: boolean;
  keywordCount: number;
  sensitivity: number;
  backgroundMode: boolean;
} {
  return {
    isActive: isDetectionActive,
    keywordCount: currentConfig.keywords.length,
    sensitivity: currentConfig.sensitivity,
    backgroundMode: currentConfig.backgroundMode,
  };
}

// Export types
export type { WakeWordConfig };
