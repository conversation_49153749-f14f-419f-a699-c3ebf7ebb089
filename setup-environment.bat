@echo off
echo ========================================
echo JARVIS Lite - Environment Setup Script
echo ========================================
echo.

echo Checking current environment...
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java is not installed or not in PATH
    echo Please install Java JDK 11 or 17 from:
    echo https://adoptium.net/temurin/releases/
    echo.
) else (
    echo ✅ Java is installed
    java -version
    echo.
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js 18+ from:
    echo https://nodejs.org/
    echo.
) else (
    echo ✅ Node.js is installed
    node --version
    npm --version
    echo.
)

REM Check if Android SDK is available
if not defined ANDROID_HOME (
    echo ❌ ANDROID_HOME is not set
    echo Please install Android Studio and set ANDROID_HOME
    echo Example: C:\Users\<USER>\AppData\Local\Android\Sdk
    echo.
) else (
    echo ✅ ANDROID_HOME is set: %ANDROID_HOME%
    echo.
)

echo ========================================
echo Environment Variables Needed:
echo ========================================
echo.
echo JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-11.x.x-hotspot
echo ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
echo PATH should include:
echo   - %%JAVA_HOME%%\bin
echo   - %%ANDROID_HOME%%\platform-tools
echo   - %%ANDROID_HOME%%\tools
echo   - Node.js installation directory
echo.

echo ========================================
echo Next Steps:
echo ========================================
echo.
echo 1. Install missing software from the URLs above
echo 2. Set environment variables in System Properties
echo 3. Restart command prompt/IDE
echo 4. Run: npm install
echo 5. Run: npx react-native run-android
echo.

echo ========================================
echo Quick Test Commands:
echo ========================================
echo.
echo npm install                    # Install dependencies
echo npx react-native clean         # Clean previous builds
echo npx react-native start         # Start Metro bundler
echo npx react-native run-android   # Build and run Android app
echo.

echo ========================================
echo Alternative: Web Demo
echo ========================================
echo.
echo Open web-demo.html in your browser to see
echo the complete feature overview and implementation status
echo.

pause
