@echo off
echo ========================================
echo JARVIS Lite - Starting Expo Development Server
echo ========================================
echo.

echo Step 1: Navigate to Expo project
cd /d "F:\jarvis\JarvisLiteExpo"
echo Current directory: %CD%

echo.
echo Step 2: Check if Expo is installed locally
if exist "node_modules\expo" (
    echo ✅ Expo found locally
) else (
    echo ❌ Expo not found, installing...
    npm install expo
)

echo.
echo Step 3: Starting Expo development server...
echo.
echo 📱 Instructions:
echo 1. Install "Expo Go" app on your phone from Google Play Store
echo 2. When QR code appears below, scan it with Expo Go
echo 3. JARVIS Lite will load on your phone!
echo.
echo Starting server...

expo start

pause
