#Fri May 30 23:22:46 IST 2025
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_fade_from_bottom.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_fade_from_bottom.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_ios_from_right_foreground_open.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_ios_from_right_foreground_open.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_no_animation_350.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_no_animation_350.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_ios_from_right_foreground_close.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_ios_from_right_foreground_close.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_fade_to_bottom.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_fade_to_bottom.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_default_enter_out.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_default_enter_out.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_ios_from_left_background_open.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_ios_from_left_background_open.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_fade_in.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_fade_in.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_slide_out_to_bottom.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_slide_out_to_bottom.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_no_animation_medium.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_no_animation_medium.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_no_animation_250.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_no_animation_250.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_slide_in_from_right.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_slide_in_from_right.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_slide_in_from_left.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_slide_in_from_left.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_ios_from_right_background_open.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_ios_from_right_background_open.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_default_exit_in.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_default_exit_in.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_ios_from_right_background_close.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_ios_from_right_background_close.xml
com.swmansion.rnscreens.react-native-screens-res-8\:/anim-v33/rns_default_enter_out.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim-v33\\rns_default_enter_out.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_standard_accelerate_interpolator.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_standard_accelerate_interpolator.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_slide_out_to_left.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_slide_out_to_left.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_slide_out_to_right.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_slide_out_to_right.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_ios_from_left_foreground_close.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_ios_from_left_foreground_close.xml
com.swmansion.rnscreens.react-native-screens-res-8\:/anim-v33/rns_default_exit_out.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim-v33\\rns_default_exit_out.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_ios_from_left_foreground_open.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_ios_from_left_foreground_open.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_ios_from_left_background_close.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_ios_from_left_background_close.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_default_exit_out.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_default_exit_out.xml
com.swmansion.rnscreens.react-native-screens-res-8\:/anim-v33/rns_default_enter_in.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim-v33\\rns_default_enter_in.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_fade_out.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_fade_out.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_default_enter_in.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_default_enter_in.xml
com.swmansion.rnscreens.react-native-screens-res-8\:/anim-v33/rns_default_exit_in.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim-v33\\rns_default_exit_in.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_slide_in_from_bottom.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_slide_in_from_bottom.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim/rns_no_animation_20.xml=F\:\\jarvis\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\anim\\rns_no_animation_20.xml
