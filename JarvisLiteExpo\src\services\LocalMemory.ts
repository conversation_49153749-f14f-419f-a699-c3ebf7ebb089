/**
 * LocalMemory.ts
 * Self-learning module that analyzes interaction logs and suggests patterns
 * Completely offline pattern recognition and habit learning
 */

import { getRecentLogs, InteractionLogEntry } from './InteractionLogger';
import moment from 'moment';

interface LearnedPattern {
  id: string;
  type: 'app' | 'reminder' | 'action';
  triggerTime?: string; // e.g., "20:00"
  triggerIntent?: string;
  resultAction: string;
  frequency: number; // number of times it occurred
  confidence: number; // 0.0 - 1.0
}

let memoryCache: LearnedPattern[] = [];

export async function analyzePatterns(): Promise<void> {
  const logs = await getRecentLogs(500);
  const patternMap: Record<string, LearnedPattern> = {};

  logs.forEach((log: InteractionLogEntry) => {
    const hour = moment(log.timestamp).format('HH:00');
    const key = `${log.intent}_${hour}`;

    if (!patternMap[key]) {
      patternMap[key] = {
        id: key,
        type: intentToType(log.intent),
        triggerTime: hour,
        triggerIntent: log.intent,
        resultAction: log.actionTaken,
        frequency: 1,
        confidence: 0.2,
      };
    } else {
      patternMap[key].frequency += 1;
    }
  });

  // Compute confidence based on frequency
  for (const key in patternMap) {
    const p = patternMap[key];
    p.confidence = Math.min(p.frequency / 7, 1.0); // e.g., if seen 5+ times, confidence is high
  }

  memoryCache = Object.values(patternMap).filter(p => p.confidence >= 0.5);
  console.log('[LocalMemory] Patterns learned:', memoryCache.length);
}

export function getSuggestions(currentHour: string): LearnedPattern[] {
  return memoryCache.filter(
    (pattern) => pattern.triggerTime === currentHour && pattern.confidence >= 0.6
  );
}

export function getAllLearnedPatterns(): LearnedPattern[] {
  return memoryCache;
}

export function clearMemory(): void {
  memoryCache = [];
  console.log('[LocalMemory] Memory cleared.');
}

export function exportMemoryAsJSON(): string {
  return JSON.stringify(memoryCache, null, 2);
}

function intentToType(intent: string): 'app' | 'reminder' | 'action' {
  if (intent.startsWith('OPEN_APP')) return 'app';
  if (intent.startsWith('SET_REMINDER')) return 'reminder';
  return 'action';
}

export { LearnedPattern };
