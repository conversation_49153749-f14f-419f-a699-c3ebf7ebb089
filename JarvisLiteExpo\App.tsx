/**
 * App.tsx
 * JARVIS Lite - Expo Version
 * Main application component with navigation and theme provider
 */

import React, { useEffect, useState } from 'react';
import { StatusBar, Alert, Platform, Text, View, StyleSheet } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Context Providers
import { ThemeProvider, useTheme } from './src/utils/ThemeContext';

// Types
import { RootStackParamList } from './src/types';

// Import screens
import SplashScreen from './src/screens/SplashScreen';
import OnboardingScreen from './src/screens/OnboardingScreen';
import MainAssistantScreen from './src/screens/MainAssistantScreen';
import SettingsScreen from './src/screens/SettingsScreen';
import AutomationBuilderScreen from './src/screens/AutomationBuilderScreen';
import AutomationTimelineScreen from './src/screens/AutomationTimelineScreen';
import AutomationImportExportScreen from './src/screens/AutomationImportExportScreen';

// Create Stack Navigator
const Stack = createStackNavigator<RootStackParamList>();

// Main App Component
function AppContent() {
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Initialize app
    const initializeApp = async () => {
      try {
        // Add any initialization logic here
        console.log('[JARVIS Lite] Initializing Expo version...');
        
        // Simulate initialization delay
        setTimeout(() => {
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error('[JARVIS Lite] Initialization error:', error);
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.loadingText, { color: theme.colors.primary }]}>
          JΛRVIS Lite
        </Text>
        <Text style={[styles.loadingSubtext, { color: theme.colors.text }]}>
          Loading...
        </Text>
      </View>
    );
  }

  return (
    <>
      <StatusBar
        barStyle={theme.isDark ? 'light-content' : 'dark-content'}
        backgroundColor={theme.colors.background}
      />
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName="Splash"
          screenOptions={{
            headerStyle: {
              backgroundColor: theme.colors.primary,
            },
            headerTintColor: theme.colors.text,
            headerTitleStyle: {
              fontWeight: 'bold',
            },
          }}
        >
          <Stack.Screen
            name="Splash"
            component={SplashScreen}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="Onboarding"
            component={OnboardingScreen}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="MainAssistant"
            component={MainAssistantScreen}
            options={{ title: 'JARVIS Lite' }}
          />
          <Stack.Screen
            name="Settings"
            component={SettingsScreen}
            options={{ title: 'Settings' }}
          />
          <Stack.Screen
            name="AutomationBuilder"
            component={AutomationBuilderScreen}
            options={{ title: 'Automation Builder' }}
          />
          <Stack.Screen
            name="AutomationTimeline"
            component={AutomationTimelineScreen}
            options={{ title: 'Automation Timeline' }}
          />
          <Stack.Screen
            name="AutomationImportExport"
            component={AutomationImportExportScreen}
            options={{ title: 'Import/Export Rules' }}
          />
        </Stack.Navigator>
      </NavigationContainer>
    </>
  );
}

// Root App Component with Providers
export default function App() {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 32,
    fontWeight: 'bold',
    letterSpacing: 2,
    marginBottom: 10,
  },
  loadingSubtext: {
    fontSize: 16,
    opacity: 0.8,
  },
});
