/**
 * AutomationEngine.ts
 * Handles if-this-then-that style automations
 * Triggers actions based on time, app usage, or device state
 */

import { AppState, DeviceEventEmitter } from 'react-native';
import moment from 'moment';
import { processVoiceCommand } from './JARVIS';
import { logInteraction, InteractionLogEntry } from './InteractionLogger';

interface AutomationRule {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  trigger: AutomationTrigger;
  actions: AutomationAction[];
  createdAt: string;
  lastTriggered?: string;
}

interface AutomationTrigger {
  type: 'time' | 'app_opened' | 'device_state' | 'voice_command';
  condition: Record<string, any>;
}

interface AutomationAction {
  type: 'speak' | 'open_app' | 'system_control' | 'reminder';
  parameters: Record<string, any>;
}

let automationRules: AutomationRule[] = [];
let isEngineRunning = false;
let timeCheckInterval: NodeJS.Timeout | null = null;

// Default automation rules
const defaultRules: AutomationRule[] = [
  {
    id: 'morning_greeting',
    name: 'Morning Greeting',
    description: 'Greet user at 8 AM every day',
    isActive: true,
    trigger: {
      type: 'time',
      condition: { hour: 8, minute: 0 }
    },
    actions: [
      {
        type: 'speak',
        parameters: { text: 'Good morning! Ready to start your day?' }
      }
    ],
    createdAt: new Date().toISOString()
  },
  {
    id: 'evening_summary',
    name: 'Evening Summary',
    description: 'Provide daily summary at 8 PM',
    isActive: true,
    trigger: {
      type: 'time',
      condition: { hour: 20, minute: 0 }
    },
    actions: [
      {
        type: 'speak',
        parameters: { text: 'Here\'s your daily summary. You had a productive day!' }
      }
    ],
    createdAt: new Date().toISOString()
  }
];

export function initializeAutomationEngine(): void {
  console.log('[AutomationEngine] Initializing...');
  
  // Load default rules if none exist
  if (automationRules.length === 0) {
    automationRules = [...defaultRules];
  }

  startEngine();
  console.log('[AutomationEngine] Initialized with', automationRules.length, 'rules');
}

export function startEngine(): void {
  if (isEngineRunning) return;

  isEngineRunning = true;
  
  // Check time-based triggers every minute
  timeCheckInterval = setInterval(checkTimeBasedTriggers, 60000);
  
  // Listen for app state changes
  AppState.addEventListener('change', handleAppStateChange);
  
  console.log('[AutomationEngine] Engine started');
}

export function stopEngine(): void {
  if (!isEngineRunning) return;

  isEngineRunning = false;
  
  if (timeCheckInterval) {
    clearInterval(timeCheckInterval);
    timeCheckInterval = null;
  }
  
  AppState.removeEventListener('change', handleAppStateChange);
  
  console.log('[AutomationEngine] Engine stopped');
}

function checkTimeBasedTriggers(): void {
  const now = moment();
  const currentHour = now.hour();
  const currentMinute = now.minute();

  automationRules
    .filter(rule => rule.isActive && rule.trigger.type === 'time')
    .forEach(rule => {
      const { hour, minute } = rule.trigger.condition;
      
      if (currentHour === hour && currentMinute === minute) {
        // Check if already triggered today
        const lastTriggered = rule.lastTriggered ? moment(rule.lastTriggered) : null;
        const isToday = lastTriggered && lastTriggered.isSame(now, 'day');
        
        if (!isToday) {
          executeRule(rule);
        }
      }
    });
}

function handleAppStateChange(nextAppState: string): void {
  // Handle app state changes for automation triggers
  console.log('[AutomationEngine] App state changed to:', nextAppState);
  
  automationRules
    .filter(rule => rule.isActive && rule.trigger.type === 'device_state')
    .forEach(rule => {
      if (rule.trigger.condition.appState === nextAppState) {
        executeRule(rule);
      }
    });
}

async function executeRule(rule: AutomationRule): Promise<void> {
  console.log(`[AutomationEngine] Executing rule: ${rule.name}`);
  
  try {
    for (const action of rule.actions) {
      await executeAction(action);
    }
    
    // Update last triggered time
    rule.lastTriggered = new Date().toISOString();
    
    // Log the automation execution
    const logEntry: InteractionLogEntry = {
      timestamp: new Date().toISOString(),
      command: `automation:${rule.name}`,
      intent: 'AUTOMATION_TRIGGERED',
      parameters: JSON.stringify(rule.trigger),
      actionTaken: `executed_${rule.actions.length}_actions`
    };
    
    await logInteraction(logEntry);
    
  } catch (error) {
    console.error(`[AutomationEngine] Error executing rule ${rule.name}:`, error);
  }
}

async function executeAction(action: AutomationAction): Promise<void> {
  switch (action.type) {
    case 'speak':
      const { speak } = await import('./OfflineTTS');
      await speak(action.parameters.text);
      break;
      
    case 'open_app':
      const { openApp } = await import('./SystemControlAndroid');
      await openApp(action.parameters.appName);
      break;
      
    case 'system_control':
      await handleSystemControl(action.parameters);
      break;
      
    case 'reminder':
      // TODO: Integrate with reminder system
      console.log('[AutomationEngine] Reminder action:', action.parameters);
      break;
      
    default:
      console.warn('[AutomationEngine] Unknown action type:', action.type);
  }
}

async function handleSystemControl(parameters: Record<string, any>): Promise<void> {
  const { action } = parameters;
  
  switch (action) {
    case 'toggle_flashlight':
      const { toggleFlashlight } = await import('./SystemControlAndroid');
      await toggleFlashlight();
      break;
      
    case 'adjust_volume':
      const { adjustVolume } = await import('./SystemControlAndroid');
      await adjustVolume(parameters.level || 0.5);
      break;
      
    default:
      console.warn('[AutomationEngine] Unknown system control action:', action);
  }
}

export function addAutomationRule(rule: Omit<AutomationRule, 'id' | 'createdAt'>): string {
  const newRule: AutomationRule = {
    ...rule,
    id: `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    createdAt: new Date().toISOString()
  };
  
  automationRules.push(newRule);
  console.log('[AutomationEngine] Added new rule:', newRule.name);
  
  return newRule.id;
}

export function removeAutomationRule(ruleId: string): boolean {
  const index = automationRules.findIndex(rule => rule.id === ruleId);
  if (index !== -1) {
    const removedRule = automationRules.splice(index, 1)[0];
    console.log('[AutomationEngine] Removed rule:', removedRule.name);
    return true;
  }
  return false;
}

export function toggleAutomationRule(ruleId: string): boolean {
  const rule = automationRules.find(r => r.id === ruleId);
  if (rule) {
    rule.isActive = !rule.isActive;
    console.log(`[AutomationEngine] Rule ${rule.name} is now ${rule.isActive ? 'active' : 'inactive'}`);
    return true;
  }
  return false;
}

export function getAllAutomationRules(): AutomationRule[] {
  return [...automationRules];
}

export function getActiveAutomationRules(): AutomationRule[] {
  return automationRules.filter(rule => rule.isActive);
}

export function exportAutomationRules(): string {
  return JSON.stringify(automationRules, null, 2);
}

export function importAutomationRules(rulesJson: string): boolean {
  try {
    const importedRules = JSON.parse(rulesJson);
    if (Array.isArray(importedRules)) {
      automationRules = importedRules;
      console.log('[AutomationEngine] Imported', automationRules.length, 'rules');
      return true;
    }
  } catch (error) {
    console.error('[AutomationEngine] Error importing rules:', error);
  }
  return false;
}

export function clearAllAutomationRules(): void {
  automationRules = [];
  console.log('[AutomationEngine] All automation rules cleared');
}

// Export types
export type { AutomationRule, AutomationTrigger, AutomationAction };
