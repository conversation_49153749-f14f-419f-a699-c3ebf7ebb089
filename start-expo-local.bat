@echo off
echo ========================================
echo JARVIS Lite - Starting Expo (Local CLI)
echo ========================================
echo.

echo Step 1: Navigate to Expo project
cd /d "F:\jarvis\JarvisLiteExpo"
echo Current directory: %CD%

echo.
echo Step 2: Using local Expo CLI (recommended)
echo.
echo 📱 Instructions:
echo 1. Install "Expo Go" app on your phone from Google Play Store
echo 2. When QR code appears below, scan it with Expo Go
echo 3. JARVIS Lite will load on your phone!
echo.
echo Starting server with local CLI...

npx expo start

pause
