/**
 * ReminderManager.ts
 * Manages reminders with local notifications and TTS alerts
 * Stores reminders in SQLite and triggers at specified times
 */

import { Platform } from 'react-native';
import SQLite from 'react-native-sqlite-storage';
import moment from 'moment';

// Mock implementations for development
const PushNotification = {
  configure: (config: any) => console.log('[PushNotification] Configure:', config),
  localNotificationSchedule: (notification: any) => console.log('[PushNotification] Schedule:', notification),
  localNotification: (notification: any) => console.log('[PushNotification] Show:', notification),
  cancelLocalNotifications: (options: any) => console.log('[PushNotification] Cancel:', options),
  cancelAllLocalNotifications: () => console.log('[PushNotification] Cancel all'),
};

const BackgroundJob = {
  start: (options: any) => console.log('[BackgroundJob] Start:', options),
  stop: (options: any) => console.log('[BackgroundJob] Stop:', options),
  on: (event: string, callback: Function) => console.log('[BackgroundJob] On:', event),
  register: (options: any) => console.log('[BackgroundJob] Register:', options),
};

// Import speak function
async function speak(text: string): Promise<void> {
  console.log('[ReminderManager] Would speak:', text);
  // This will be replaced with actual TTS import when available
}

SQLite.enablePromise(true);

export interface Reminder {
  id: string;
  title: string;
  timestamp: string; // ISO string
  repeat: 'none' | 'daily' | 'weekly' | 'monthly';
  ttsEnabled: boolean;
  isActive?: boolean;
}

const DB_NAME = 'JARVIS.db';
const TABLE_NAME = 'reminders';

let db: SQLite.SQLiteDatabase | null = null;
let activeReminders: Reminder[] = [];

export async function initReminderManager(): Promise<void> {
  try {
    db = await SQLite.openDatabase({ name: DB_NAME, location: 'default' });

    await db.executeSql(`
      CREATE TABLE IF NOT EXISTS ${TABLE_NAME} (
        id TEXT PRIMARY KEY,
        title TEXT,
        timestamp TEXT,
        repeat TEXT,
        ttsEnabled INTEGER,
        isActive INTEGER DEFAULT 1
      );
    `);

    // Configure push notifications
    PushNotification.configure({
      onNotification: function (notification) {
        console.log('[ReminderManager] Notification:', notification);
        if (notification.userInteraction) {
          // User tapped the notification
          handleReminderTrigger(notification.data?.reminderId);
        }
      },
      requestPermissions: Platform.OS === 'ios',
    });

    // Load active reminders
    await loadActiveReminders();

    // Start background monitoring
    startBackgroundMonitoring();

    console.log('[ReminderManager] Initialized with', activeReminders.length, 'active reminders');
  } catch (err) {
    console.error('[ReminderManager] Init error:', err);
  }
}

export async function saveReminder(reminder: Reminder): Promise<void> {
  if (!db) return;

  try {
    await db.executeSql(
      `INSERT OR REPLACE INTO ${TABLE_NAME} (id, title, timestamp, repeat, ttsEnabled, isActive)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [
        reminder.id,
        reminder.title,
        reminder.timestamp,
        reminder.repeat,
        reminder.ttsEnabled ? 1 : 0,
        reminder.isActive !== false ? 1 : 0,
      ]
    );

    // Schedule notification
    scheduleNotification(reminder);

    // Reload active reminders
    await loadActiveReminders();

    console.log('[ReminderManager] Reminder saved:', reminder.title);
  } catch (err) {
    console.error('[ReminderManager] Save error:', err);
  }
}

export async function getReminders(): Promise<Reminder[]> {
  if (!db) return [];

  try {
    const [results] = await db.executeSql(
      `SELECT * FROM ${TABLE_NAME} WHERE isActive = 1 ORDER BY timestamp ASC`
    );

    const reminders: Reminder[] = [];
    for (let i = 0; i < results.rows.length; i++) {
      const row = results.rows.item(i);
      reminders.push({
        id: row.id,
        title: row.title,
        timestamp: row.timestamp,
        repeat: row.repeat,
        ttsEnabled: row.ttsEnabled === 1,
        isActive: row.isActive === 1,
      });
    }

    return reminders;
  } catch (err) {
    console.error('[ReminderManager] Get error:', err);
    return [];
  }
}

export async function deleteReminder(id: string): Promise<void> {
  if (!db) return;

  try {
    await db.executeSql(`UPDATE ${TABLE_NAME} SET isActive = 0 WHERE id = ?`, [id]);

    // Cancel notification
    PushNotification.cancelLocalNotifications({ id });

    // Reload active reminders
    await loadActiveReminders();

    console.log('[ReminderManager] Reminder deleted:', id);
  } catch (err) {
    console.error('[ReminderManager] Delete error:', err);
  }
}

async function loadActiveReminders(): Promise<void> {
  activeReminders = await getReminders();
}

function scheduleNotification(reminder: Reminder): void {
  const reminderDate = new Date(reminder.timestamp);
  const now = new Date();

  if (reminderDate <= now) {
    console.log('[ReminderManager] Reminder time has passed, skipping notification');
    return;
  }

  PushNotification.localNotificationSchedule({
    id: reminder.id,
    title: 'JARVIS Reminder',
    message: reminder.title,
    date: reminderDate,
    soundName: 'default',
    userInfo: { reminderId: reminder.id },
  });

  console.log('[ReminderManager] Notification scheduled for:', reminderDate.toLocaleString());
}

function startBackgroundMonitoring(): void {
  // Check for due reminders every minute
  BackgroundJob.start({
    jobKey: 'reminderCheck',
    period: 60000, // 1 minute
  });

  BackgroundJob.on('reminderCheck', () => {
    checkDueReminders();
  });
}

async function checkDueReminders(): Promise<void> {
  const now = moment();

  for (const reminder of activeReminders) {
    const reminderTime = moment(reminder.timestamp);

    if (now.isSameOrAfter(reminderTime, 'minute')) {
      await triggerReminder(reminder);

      // Handle repeating reminders
      if (reminder.repeat !== 'none') {
        await scheduleNextOccurrence(reminder);
      } else {
        // Mark as completed
        await deleteReminder(reminder.id);
      }
    }
  }
}

async function triggerReminder(reminder: Reminder): Promise<void> {
  console.log('[ReminderManager] Triggering reminder:', reminder.title);

  // Show notification
  PushNotification.localNotification({
    title: 'JARVIS Reminder',
    message: reminder.title,
    soundName: 'default',
    userInfo: { reminderId: reminder.id },
  });

  // Speak reminder if TTS enabled
  if (reminder.ttsEnabled) {
    await speak(`Reminder: ${reminder.title}`);
  }
}

async function scheduleNextOccurrence(reminder: Reminder): Promise<void> {
  const currentTime = moment(reminder.timestamp);
  let nextTime: moment.Moment;

  switch (reminder.repeat) {
    case 'daily':
      nextTime = currentTime.add(1, 'day');
      break;
    case 'weekly':
      nextTime = currentTime.add(1, 'week');
      break;
    case 'monthly':
      nextTime = currentTime.add(1, 'month');
      break;
    default:
      return;
  }

  const nextReminder: Reminder = {
    ...reminder,
    id: `${reminder.id}_${nextTime.unix()}`,
    timestamp: nextTime.toISOString(),
  };

  await saveReminder(nextReminder);
}

function handleReminderTrigger(reminderId: string): void {
  const reminder = activeReminders.find(r => r.id === reminderId);
  if (reminder && reminder.ttsEnabled) {
    speak(`Reminder: ${reminder.title}`);
  }
}

export async function getUpcomingReminders(limit: number = 5): Promise<Reminder[]> {
  const reminders = await getReminders();
  const now = new Date();

  return reminders
    .filter(r => new Date(r.timestamp) > now)
    .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
    .slice(0, limit);
}

export async function getTodayReminders(): Promise<Reminder[]> {
  const reminders = await getReminders();
  const today = moment().startOf('day');
  const tomorrow = moment().add(1, 'day').startOf('day');

  return reminders.filter(r => {
    const reminderTime = moment(r.timestamp);
    return reminderTime.isBetween(today, tomorrow, null, '[)');
  });
}

export function createQuickReminder(title: string, minutesFromNow: number): Reminder {
  const now = new Date();
  now.setMinutes(now.getMinutes() + minutesFromNow);

  return {
    id: `quick_${Date.now()}`,
    title,
    timestamp: now.toISOString(),
    repeat: 'none',
    ttsEnabled: true,
    isActive: true,
  };
}

export async function clearAllReminders(): Promise<void> {
  if (!db) return;

  try {
    await db.executeSql(`UPDATE ${TABLE_NAME} SET isActive = 0`);
    PushNotification.cancelAllLocalNotifications();
    activeReminders = [];
    console.log('[ReminderManager] All reminders cleared');
  } catch (err) {
    console.error('[ReminderManager] Clear error:', err);
  }
}

export { activeReminders };
