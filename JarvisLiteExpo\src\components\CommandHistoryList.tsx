/**
 * CommandHistoryList.tsx
 * Displays recent voice commands and their results
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
} from 'react-native';

import { useTheme } from '@/utils/ThemeContext';

interface CommandHistoryItem {
  id: string;
  timestamp: Date;
  command: string;
  intent: string;
  response: string;
  success: boolean;
}

interface CommandHistoryListProps {
  history: CommandHistoryItem[];
  onItemPress?: (item: CommandHistoryItem) => void;
}

const CommandHistoryList: React.FC<CommandHistoryListProps> = ({
  history,
  onItemPress,
}) => {
  const { theme } = useTheme();

  const getIntentIcon = (intent: string): string => {
    const iconMap: Record<string, string> = {
      open_app: '📱',
      toggle_flashlight: '🔦',
      adjust_volume: '🔊',
      toggle_wifi: '📶',
      make_call: '📞',
      create_reminder: '⏰',
      get_time: '🕐',
      get_date: '📅',
      play_music: '🎵',
      show_activity: '📊',
      stop_listening: '🛑',
      unknown: '❓',
    };
    return iconMap[intent] || '💬';
  };

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const renderHistoryItem = ({ item }: { item: CommandHistoryItem }) => (
    <TouchableOpacity
      style={[
        localStyles.historyItem,
        {
          backgroundColor: theme.colors.surface,
          borderColor: theme.colors.border,
        },
      ]}
      onPress={() => onItemPress?.(item)}
      activeOpacity={0.7}
    >
      <View style={localStyles.itemHeader}>
        <View style={localStyles.iconAndTime}>
          <Text style={localStyles.intentIcon}>
            {getIntentIcon(item.intent)}
          </Text>
          <Text
            style={[
              localStyles.timestamp,
              { color: theme.colors.textSecondary },
            ]}
          >
            {formatTime(item.timestamp)}
          </Text>
        </View>
        
        <View
          style={[
            localStyles.statusIndicator,
            {
              backgroundColor: item.success 
                ? theme.colors.success 
                : theme.colors.error,
            },
          ]}
        />
      </View>

      <Text
        style={[
          localStyles.command,
          { color: theme.colors.text },
        ]}
        numberOfLines={2}
      >
        "{item.command}"
      </Text>

      <Text
        style={[
          localStyles.intent,
          { color: theme.colors.textSecondary },
        ]}
      >
        Intent: {item.intent.replace(/_/g, ' ')}
      </Text>

      {item.response && (
        <Text
          style={[
            localStyles.response,
            { color: theme.colors.textSecondary },
          ]}
          numberOfLines={2}
        >
          {item.response}
        </Text>
      )}
    </TouchableOpacity>
  );

  if (history.length === 0) {
    return (
      <View style={localStyles.emptyContainer}>
        <Text
          style={[
            localStyles.emptyText,
            { color: theme.colors.textSecondary },
          ]}
        >
          No commands yet. Try saying something!
        </Text>
      </View>
    );
  }

  return (
    <View style={localStyles.container}>
      <Text
        style={[
          localStyles.title,
          { color: theme.colors.textSecondary },
        ]}
      >
        Recent Commands
      </Text>
      
      <FlatList
        data={history}
        renderItem={renderHistoryItem}
        keyExtractor={(item) => item.id}
        style={localStyles.list}
        showsVerticalScrollIndicator={false}
        scrollEnabled={false} // Disable scroll since it's in a parent ScrollView
      />
    </View>
  );
};

const localStyles = StyleSheet.create({
  container: {
    marginVertical: 20,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
  },
  list: {
    maxHeight: 300, // Limit height to prevent overflow
  },
  historyItem: {
    padding: 16,
    marginBottom: 8,
    borderRadius: 12,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconAndTime: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  intentIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  timestamp: {
    fontSize: 12,
    fontWeight: '500',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  command: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
    lineHeight: 20,
  },
  intent: {
    fontSize: 12,
    fontStyle: 'italic',
    marginBottom: 4,
    textTransform: 'capitalize',
  },
  response: {
    fontSize: 12,
    lineHeight: 16,
    fontStyle: 'italic',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default CommandHistoryList;
