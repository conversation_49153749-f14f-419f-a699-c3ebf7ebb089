/**
 * AutomationStorage.ts
 * Handles storage and retrieval of automation rules using AsyncStorage
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { AutomationRule, validateRule } from '../models/AutomationRule';

const STORAGE_KEY = 'JARVIS_AUTOMATION_RULES';
const BACKUP_KEY = 'JARVIS_AUTOMATION_RULES_BACKUP';

export async function getRules(): Promise<AutomationRule[]> {
  try {
    const json = await AsyncStorage.getItem(STORAGE_KEY);
    if (!json) return [];
    
    const rules = JSON.parse(json);
    return Array.isArray(rules) ? rules : [];
  } catch (error) {
    console.error('[AutomationStorage] Error getting rules:', error);
    return [];
  }
}

export async function saveRule(rule: AutomationRule): Promise<boolean> {
  try {
    // Validate rule before saving
    const errors = validateRule(rule);
    if (errors.length > 0) {
      console.error('[AutomationStorage] Validation errors:', errors);
      return false;
    }

    const rules = await getRules();
    
    // Check for duplicate times (optional - you might want to allow this)
    const existingRule = rules.find(r => r.triggerTime === rule.triggerTime && r.id !== rule.id);
    if (existingRule) {
      console.warn('[AutomationStorage] Rule already exists at this time:', rule.triggerTime);
    }
    
    // Add timestamps
    const ruleWithTimestamp = {
      ...rule,
      createdAt: rule.createdAt || new Date().toISOString(),
    };
    
    rules.push(ruleWithTimestamp);
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(rules));
    
    // Create backup
    await createBackup(rules);
    
    console.log('[AutomationStorage] Rule saved:', rule.id);
    return true;
  } catch (error) {
    console.error('[AutomationStorage] Error saving rule:', error);
    return false;
  }
}

export async function updateRule(updatedRule: AutomationRule): Promise<boolean> {
  try {
    const errors = validateRule(updatedRule);
    if (errors.length > 0) {
      console.error('[AutomationStorage] Validation errors:', errors);
      return false;
    }

    const rules = await getRules();
    const index = rules.findIndex(r => r.id === updatedRule.id);
    
    if (index === -1) {
      console.error('[AutomationStorage] Rule not found for update:', updatedRule.id);
      return false;
    }
    
    rules[index] = { ...updatedRule };
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(rules));
    
    console.log('[AutomationStorage] Rule updated:', updatedRule.id);
    return true;
  } catch (error) {
    console.error('[AutomationStorage] Error updating rule:', error);
    return false;
  }
}

export async function deleteRule(id: string): Promise<boolean> {
  try {
    const rules = await getRules();
    const updated = rules.filter(r => r.id !== id);
    
    if (updated.length === rules.length) {
      console.warn('[AutomationStorage] Rule not found for deletion:', id);
      return false;
    }
    
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
    
    console.log('[AutomationStorage] Rule deleted:', id);
    return true;
  } catch (error) {
    console.error('[AutomationStorage] Error deleting rule:', error);
    return false;
  }
}

export async function toggleRule(id: string, enabled: boolean): Promise<boolean> {
  try {
    const rules = await getRules();
    const updated = rules.map(r =>
      r.id === id ? { ...r, enabled } : r
    );
    
    const rule = updated.find(r => r.id === id);
    if (!rule) {
      console.warn('[AutomationStorage] Rule not found for toggle:', id);
      return false;
    }
    
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
    
    console.log('[AutomationStorage] Rule toggled:', id, enabled);
    return true;
  } catch (error) {
    console.error('[AutomationStorage] Error toggling rule:', error);
    return false;
  }
}

export async function getRulesByTime(time: string): Promise<AutomationRule[]> {
  try {
    const rules = await getRules();
    return rules.filter(r => r.triggerTime === time && r.enabled);
  } catch (error) {
    console.error('[AutomationStorage] Error getting rules by time:', error);
    return [];
  }
}

export async function getRulesByCategory(category: string): Promise<AutomationRule[]> {
  try {
    const rules = await getRules();
    return rules.filter(r => r.category === category);
  } catch (error) {
    console.error('[AutomationStorage] Error getting rules by category:', error);
    return [];
  }
}

export async function getActiveRules(): Promise<AutomationRule[]> {
  try {
    const rules = await getRules();
    return rules.filter(r => r.enabled);
  } catch (error) {
    console.error('[AutomationStorage] Error getting active rules:', error);
    return [];
  }
}

export async function exportRules(): Promise<string> {
  try {
    const rules = await getRules();
    const exportData = {
      version: '1.0',
      exportDate: new Date().toISOString(),
      rules: rules,
      totalRules: rules.length,
    };
    return JSON.stringify(exportData, null, 2);
  } catch (error) {
    console.error('[AutomationStorage] Error exporting rules:', error);
    return '{}';
  }
}

export async function importRules(json: string, replaceExisting: boolean = false): Promise<boolean> {
  try {
    const data = JSON.parse(json);
    
    // Handle different import formats
    let importedRules: AutomationRule[] = [];
    
    if (Array.isArray(data)) {
      // Direct array of rules
      importedRules = data;
    } else if (data.rules && Array.isArray(data.rules)) {
      // Export format with metadata
      importedRules = data.rules;
    } else {
      console.error('[AutomationStorage] Invalid import format');
      return false;
    }
    
    // Validate all rules
    for (const rule of importedRules) {
      const errors = validateRule(rule);
      if (errors.length > 0) {
        console.error('[AutomationStorage] Invalid rule in import:', errors);
        return false;
      }
    }
    
    if (replaceExisting) {
      // Replace all existing rules
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(importedRules));
    } else {
      // Merge with existing rules
      const existingRules = await getRules();
      const mergedRules = [...existingRules, ...importedRules];
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(mergedRules));
    }
    
    console.log('[AutomationStorage] Rules imported successfully:', importedRules.length);
    return true;
  } catch (error) {
    console.error('[AutomationStorage] Error importing rules:', error);
    return false;
  }
}

export async function clearAllRules(): Promise<boolean> {
  try {
    // Create backup before clearing
    const rules = await getRules();
    await createBackup(rules);
    
    await AsyncStorage.removeItem(STORAGE_KEY);
    console.log('[AutomationStorage] All rules cleared');
    return true;
  } catch (error) {
    console.error('[AutomationStorage] Error clearing rules:', error);
    return false;
  }
}

export async function createBackup(rules?: AutomationRule[]): Promise<void> {
  try {
    const rulesToBackup = rules || await getRules();
    const backup = {
      timestamp: new Date().toISOString(),
      rules: rulesToBackup,
    };
    await AsyncStorage.setItem(BACKUP_KEY, JSON.stringify(backup));
  } catch (error) {
    console.error('[AutomationStorage] Error creating backup:', error);
  }
}

export async function restoreFromBackup(): Promise<boolean> {
  try {
    const backupJson = await AsyncStorage.getItem(BACKUP_KEY);
    if (!backupJson) {
      console.warn('[AutomationStorage] No backup found');
      return false;
    }
    
    const backup = JSON.parse(backupJson);
    if (backup.rules && Array.isArray(backup.rules)) {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(backup.rules));
      console.log('[AutomationStorage] Restored from backup:', backup.timestamp);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('[AutomationStorage] Error restoring from backup:', error);
    return false;
  }
}

export async function getStorageStats(): Promise<{
  totalRules: number;
  activeRules: number;
  categoryCounts: Record<string, number>;
  lastBackup?: string;
}> {
  try {
    const rules = await getRules();
    const activeRules = rules.filter(r => r.enabled);
    
    const categoryCounts: Record<string, number> = {};
    rules.forEach(rule => {
      categoryCounts[rule.category] = (categoryCounts[rule.category] || 0) + 1;
    });
    
    // Get backup info
    let lastBackup: string | undefined;
    try {
      const backupJson = await AsyncStorage.getItem(BACKUP_KEY);
      if (backupJson) {
        const backup = JSON.parse(backupJson);
        lastBackup = backup.timestamp;
      }
    } catch {
      // Ignore backup errors
    }
    
    return {
      totalRules: rules.length,
      activeRules: activeRules.length,
      categoryCounts,
      lastBackup,
    };
  } catch (error) {
    console.error('[AutomationStorage] Error getting storage stats:', error);
    return {
      totalRules: 0,
      activeRules: 0,
      categoryCounts: {},
    };
  }
}
