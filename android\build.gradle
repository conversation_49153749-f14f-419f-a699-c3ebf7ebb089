// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "31.0.0"
        minSdkVersion = 21
        compileSdkVersion = 31
        targetSdkVersion = 31
        ndkVersion = "27.2.12479018"  // Using NDK installed on F: drive
    }
    repositories {
        google {
            content {
                includeGroupByRegex "com\\.android.*"
                includeGroupByRegex "com\\.google.*"
                includeGroupByRegex "androidx.*"
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:7.3.1")
        // classpath("com.facebook.react:react-native-gradle-plugin:0.72.6")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://www.jitpack.io" }
    }
}
