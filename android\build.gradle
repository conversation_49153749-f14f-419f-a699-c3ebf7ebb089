// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 21
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "27.2.12479018"  // Using NDK installed on F: drive
    }
    repositories {
        google {
            content {
                includeGroupByRegex "com\\.android.*"
                includeGroupByRegex "com\\.google.*"
                includeGroupByRegex "androidx.*"
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:7.3.1")
        // classpath("com.facebook.react:react-native-gradle-plugin:0.72.6")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://www.jitpack.io" }
    }

    // Global dependency substitution for all modules
    configurations.all {
        resolutionStrategy.dependencySubstitution {
            substitute module('com.facebook.react:react-native') using module('com.facebook.react:react-android:0.72.6')
        }
    }
}
