@echo off
echo ========================================
echo JARVIS Lite - NDK Fix and Build Script
echo ========================================
echo.

echo Step 1: Cleaning corrupted NDK folder...
if exist "C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\23.1.7779620" (
    echo Removing corrupted NDK folder...
    rmdir /s /q "C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\23.1.7779620"
    echo ✅ Corrupted NDK folder removed
) else (
    echo ✅ NDK folder already clean
)

echo.
echo Step 2: Cleaning build cache...
cd /d F:\jarvis\android
if exist gradlew.bat (
    echo Running Gradle clean...
    gradlew.bat clean
    echo ✅ Build cache cleaned
) else (
    echo ⚠️ gradlew.bat not found, skipping clean
)

echo.
echo Step 3: Removing node_modules and reinstalling...
cd /d F:\jarvis
rmdir /s /q node_modules
npm install

echo.
echo ========================================
echo NDK Fix Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Open Android Studio
echo 2. Go to Tools → SDK Manager → SDK Tools
echo 3. Check "NDK (Side by side)" and install
echo 4. After installation, run this command:
echo    node "node_modules/@react-native-community/cli/build/bin.js" run-android
echo.
echo OR try the Expo version:
echo    cd JarvisLiteExpo
echo    npx expo start
echo.

pause
