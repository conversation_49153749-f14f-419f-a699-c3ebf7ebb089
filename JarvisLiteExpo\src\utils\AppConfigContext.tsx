/**
 * AppConfigContext.tsx
 * Global application configuration management
 * Handles user preferences, settings, and app state
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppConfig, PersonalityMode } from '@/types';

interface AppConfigContextType {
  config: AppConfig | null;
  isLoading: boolean;
  updateConfig: (updates: Partial<AppConfig>) => Promise<void>;
  resetConfig: () => Promise<void>;
  setFirstLaunchComplete: () => Promise<void>;
}

const defaultConfig: AppConfig = {
  isFirstLaunch: true,
  personality: {
    mode: PersonalityMode.JARVIS,
    emojiAvatar: '🤖',
    voiceProfile: 'default',
    customGreeting: undefined,
    responseStyle: {
      useEmojis: true,
      formality: 'friendly',
      verbosity: 'normal',
    },
  },
  voiceSettings: {
    wakeWordEnabled: false,
    continuousListening: false,
    voiceEngine: 'whisper',
    ttsEngine: 'coqui',
  },
  privacySettings: {
    trackingEnabled: true,
    learningEnabled: true,
    dataRetentionDays: 30,
  },
  uiSettings: {
    theme: 'system',
    language: 'en',
    animations: true,
  },
};

const AppConfigContext = createContext<AppConfigContextType | undefined>(undefined);

interface AppConfigProviderProps {
  children: ReactNode;
}

export const AppConfigProvider: React.FC<AppConfigProviderProps> = ({ children }) => {
  const [config, setConfig] = useState<AppConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadConfig();
  }, []);

  /**
   * Load configuration from storage
   */
  const loadConfig = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const configJson = await AsyncStorage.getItem('app_config');
      
      if (configJson) {
        const savedConfig = JSON.parse(configJson);
        // Merge with default config to ensure all properties exist
        const mergedConfig = mergeConfigs(defaultConfig, savedConfig);
        setConfig(mergedConfig);
      } else {
        // First time launch - use default config
        setConfig(defaultConfig);
        await saveConfig(defaultConfig);
      }
    } catch (error) {
      console.error('Error loading app config:', error);
      setConfig(defaultConfig);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Save configuration to storage
   */
  const saveConfig = async (configToSave: AppConfig): Promise<void> => {
    try {
      await AsyncStorage.setItem('app_config', JSON.stringify(configToSave));
    } catch (error) {
      console.error('Error saving app config:', error);
      throw error;
    }
  };

  /**
   * Update configuration
   */
  const updateConfig = async (updates: Partial<AppConfig>): Promise<void> => {
    if (!config) return;

    try {
      const updatedConfig = mergeConfigs(config, updates);
      setConfig(updatedConfig);
      await saveConfig(updatedConfig);
    } catch (error) {
      console.error('Error updating app config:', error);
      throw error;
    }
  };

  /**
   * Reset configuration to defaults
   */
  const resetConfig = async (): Promise<void> => {
    try {
      const resetConfig = { ...defaultConfig, isFirstLaunch: false };
      setConfig(resetConfig);
      await saveConfig(resetConfig);
    } catch (error) {
      console.error('Error resetting app config:', error);
      throw error;
    }
  };

  /**
   * Mark first launch as complete
   */
  const setFirstLaunchComplete = async (): Promise<void> => {
    await updateConfig({ isFirstLaunch: false });
  };

  /**
   * Deep merge two configuration objects
   */
  const mergeConfigs = (base: AppConfig, updates: Partial<AppConfig>): AppConfig => {
    const merged = { ...base };

    Object.keys(updates).forEach(key => {
      const updateValue = updates[key as keyof AppConfig];
      const baseValue = base[key as keyof AppConfig];

      if (updateValue !== undefined) {
        if (typeof updateValue === 'object' && updateValue !== null && !Array.isArray(updateValue)) {
          // Deep merge objects
          merged[key as keyof AppConfig] = {
            ...baseValue as any,
            ...updateValue,
          };
        } else {
          // Direct assignment for primitives and arrays
          merged[key as keyof AppConfig] = updateValue as any;
        }
      }
    });

    return merged;
  };

  const contextValue: AppConfigContextType = {
    config,
    isLoading,
    updateConfig,
    resetConfig,
    setFirstLaunchComplete,
  };

  return (
    <AppConfigContext.Provider value={contextValue}>
      {children}
    </AppConfigContext.Provider>
  );
};

/**
 * Hook to use app config context
 */
export const useAppConfig = (): AppConfigContextType => {
  const context = useContext(AppConfigContext);
  if (context === undefined) {
    throw new Error('useAppConfig must be used within an AppConfigProvider');
  }
  return context;
};

/**
 * Helper hooks for specific config sections
 */
export const usePersonalityConfig = () => {
  const { config, updateConfig } = useAppConfig();
  
  const updatePersonality = async (updates: Partial<AppConfig['personality']>) => {
    if (!config) return;
    await updateConfig({
      personality: { ...config.personality, ...updates }
    });
  };

  return {
    personality: config?.personality,
    updatePersonality,
  };
};

export const useVoiceSettings = () => {
  const { config, updateConfig } = useAppConfig();
  
  const updateVoiceSettings = async (updates: Partial<AppConfig['voiceSettings']>) => {
    if (!config) return;
    await updateConfig({
      voiceSettings: { ...config.voiceSettings, ...updates }
    });
  };

  return {
    voiceSettings: config?.voiceSettings,
    updateVoiceSettings,
  };
};

export const usePrivacySettings = () => {
  const { config, updateConfig } = useAppConfig();
  
  const updatePrivacySettings = async (updates: Partial<AppConfig['privacySettings']>) => {
    if (!config) return;
    await updateConfig({
      privacySettings: { ...config.privacySettings, ...updates }
    });
  };

  return {
    privacySettings: config?.privacySettings,
    updatePrivacySettings,
  };
};

export const useUISettings = () => {
  const { config, updateConfig } = useAppConfig();
  
  const updateUISettings = async (updates: Partial<AppConfig['uiSettings']>) => {
    if (!config) return;
    await updateConfig({
      uiSettings: { ...config.uiSettings, ...updates }
    });
  };

  return {
    uiSettings: config?.uiSettings,
    updateUISettings,
  };
};

export default AppConfigProvider;
