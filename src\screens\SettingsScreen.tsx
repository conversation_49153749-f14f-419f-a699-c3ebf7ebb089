/**
 * SettingsScreen.tsx
 * Comprehensive settings screen with theme switching and biometric security
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../utils/ThemeContext';
import { useNavigation } from '@react-navigation/native';

export default function SettingsScreen() {
  const { theme, themeMode, setTheme, isSecurityEnabled, setSecurityEnabled } = useTheme();
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(false);

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme);
  };

  const handleSecurityToggle = async (enabled: boolean) => {
    if (enabled) {
      // In a real app, you would check for biometric availability here
      Alert.alert(
        'Enable Biometric Security',
        'This would enable fingerprint/face unlock for sensitive settings. (Demo mode)',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Enable',
            onPress: () => setSecurityEnabled(enabled),
          },
        ]
      );
    } else {
      setSecurityEnabled(enabled);
    }
  };

  const navigateToScreen = (screenName: string) => {
    navigation.navigate(screenName as never);
  };

  const settingSections = [
    {
      title: 'Appearance',
      items: [
        {
          icon: 'palette',
          title: 'Theme',
          subtitle: `Current: ${themeMode.charAt(0).toUpperCase() + themeMode.slice(1)}`,
          type: 'theme-selector',
        },
      ],
    },
    {
      title: 'Security',
      items: [
        {
          icon: 'fingerprint',
          title: 'Biometric Security',
          subtitle: 'Protect sensitive settings with biometrics',
          type: 'switch',
          value: isSecurityEnabled,
          onToggle: handleSecurityToggle,
        },
      ],
    },
    {
      title: 'Automation',
      items: [
        {
          icon: 'robot',
          title: 'Automation Builder',
          subtitle: 'Create and manage automation rules',
          type: 'navigation',
          screen: 'AutomationBuilder',
        },
        {
          icon: 'timeline-clock',
          title: 'Automation Timeline',
          subtitle: 'View scheduled automations',
          type: 'navigation',
          screen: 'AutomationTimeline',
        },
        {
          icon: 'import',
          title: 'Import/Export Rules',
          subtitle: 'Backup and share automation rules',
          type: 'navigation',
          screen: 'AutomationImportExport',
        },
      ],
    },
    {
      title: 'Data & Privacy',
      items: [
        {
          icon: 'chart-line',
          title: 'Learning Insights',
          subtitle: 'View pattern analysis and suggestions',
          type: 'navigation',
          screen: 'LearningInsights',
        },
        {
          icon: 'bug',
          title: 'Debug & Logs',
          subtitle: 'View system logs and debug information',
          type: 'navigation',
          screen: 'DebugAndLogs',
        },
      ],
    },
    {
      title: 'Help & Support',
      items: [
        {
          icon: 'help-circle',
          title: 'Help & Tutorial',
          subtitle: 'Learn how to use JARVIS Lite',
          type: 'navigation',
          screen: 'HelpAndTutorial',
        },
        {
          icon: 'information',
          title: 'System Status',
          subtitle: 'Check system health and performance',
          type: 'navigation',
          screen: 'SystemStatus',
        },
      ],
    },
  ];

  const renderThemeSelector = () => (
    <View style={styles.themeSelector}>
      {(['light', 'dark', 'system'] as const).map((themeName) => (
        <TouchableOpacity
          key={themeName}
          style={[
            styles.themeOption,
            themeMode === themeName && styles.themeOptionActive,
            { backgroundColor: themeMode === themeName ? theme.colors.primary : theme.colors.surface }
          ]}
          onPress={() => handleThemeChange(themeName)}
        >
          <Icon
            name={
              themeName === 'light' ? 'white-balance-sunny' :
              themeName === 'dark' ? 'moon-waning-crescent' : 'cog'
            }
            size={20}
            color={themeMode === themeName ? '#fff' : theme.colors.text}
          />
          <Text style={[
            styles.themeOptionText,
            { color: themeMode === themeName ? '#fff' : theme.colors.text }
          ]}>
            {themeName.charAt(0).toUpperCase() + themeName.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderSettingItem = (item: any) => {
    switch (item.type) {
      case 'theme-selector':
        return (
          <View key={item.title} style={[styles.settingItem, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.settingInfo}>
              <Icon name={item.icon} size={24} color={theme.colors.primary} />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.colors.text }]}>{item.title}</Text>
                <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary }]}>{item.subtitle}</Text>
              </View>
            </View>
            {renderThemeSelector()}
          </View>
        );

      case 'switch':
        return (
          <TouchableOpacity
            key={item.title}
            style={[styles.settingItem, { backgroundColor: theme.colors.surface }]}
            onPress={() => item.onToggle && item.onToggle(!item.value)}
          >
            <View style={styles.settingInfo}>
              <Icon name={item.icon} size={24} color={theme.colors.primary} />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.colors.text }]}>{item.title}</Text>
                <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary }]}>{item.subtitle}</Text>
              </View>
            </View>
            <Switch
              value={item.value}
              onValueChange={item.onToggle}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={item.value ? '#fff' : '#f4f3f4'}
            />
          </TouchableOpacity>
        );

      case 'navigation':
        return (
          <TouchableOpacity
            key={item.title}
            style={[styles.settingItem, { backgroundColor: theme.colors.surface }]}
            onPress={() => navigateToScreen(item.screen)}
          >
            <View style={styles.settingInfo}>
              <Icon name={item.icon} size={24} color={theme.colors.primary} />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.colors.text }]}>{item.title}</Text>
                <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary }]}>{item.subtitle}</Text>
              </View>
            </View>
            <Icon name="chevron-right" size={24} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        );

      default:
        return null;
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text style={[styles.heading, { color: theme.colors.primary }]}>⚙️ Settings</Text>

      {settingSections.map((section) => (
        <View key={section.title} style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>{section.title}</Text>
          {section.items.map(renderSettingItem)}
        </View>
      ))}

      <View style={styles.footer}>
        <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
          JARVIS Lite v1.0.0
        </Text>
        <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
          🔒 All data stays on your device
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  heading: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    paddingHorizontal: 4,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: 16,
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  settingSubtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  themeSelector: {
    flexDirection: 'row',
    marginTop: 12,
  },
  themeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    marginRight: 8,
  },
  themeOptionActive: {
    // Background color set dynamically
  },
  themeOptionText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 6,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  footerText: {
    fontSize: 12,
    marginBottom: 4,
    textAlign: 'center',
  },
});

export default SettingsScreen;
