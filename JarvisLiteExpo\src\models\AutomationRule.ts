/**
 * AutomationRule.ts
 * Model for automation rules with categories and actions
 */

export interface AutomationRule {
  id: string;
  triggerTime: string; // "HH:MM"
  action: string; // intent/action ID
  category: 'Media' | 'Utility' | 'Wellness' | 'Custom';
  enabled: boolean;
  createdAt?: string;
  lastTriggered?: string;
}

export interface AutomationAction {
  id: string;
  label: string;
  category: 'Media' | 'Utility' | 'Wellness' | 'Custom';
  description?: string;
}

export const AVAILABLE_ACTIONS: AutomationAction[] = [
  { 
    id: 'OPEN_APP_YOUTUBE', 
    label: 'Open YouTube', 
    category: 'Media',
    description: 'Launch YouTube app'
  },
  { 
    id: 'OPEN_APP_CAMERA', 
    label: 'Open Camera', 
    category: 'Media',
    description: 'Launch camera app'
  },
  { 
    id: 'OPEN_APP_GALLERY', 
    label: 'Open Gallery', 
    category: 'Media',
    description: 'Launch photo gallery'
  },
  { 
    id: 'TOGGLE_FLASHLIGHT', 
    label: 'Toggle Flashlight', 
    category: 'Utility',
    description: 'Turn flashlight on/off'
  },
  { 
    id: 'ADJUST_VOLUME_UP', 
    label: 'Volume Up', 
    category: 'Utility',
    description: 'Increase system volume'
  },
  { 
    id: 'ADJUST_VOLUME_DOWN', 
    label: 'Volume Down', 
    category: 'Utility',
    description: 'Decrease system volume'
  },
  { 
    id: 'ADJUST_BRIGHTNESS_UP', 
    label: 'Brightness Up', 
    category: 'Utility',
    description: 'Increase screen brightness'
  },
  { 
    id: 'ADJUST_BRIGHTNESS_DOWN', 
    label: 'Brightness Down', 
    category: 'Utility',
    description: 'Decrease screen brightness'
  },
  { 
    id: 'SEND_SMS', 
    label: 'Send SMS', 
    category: 'Utility',
    description: 'Send predefined SMS message'
  },
  { 
    id: 'SPEAK_QUOTE', 
    label: 'Speak a Quote', 
    category: 'Wellness',
    description: 'Speak random motivational quote'
  },
  { 
    id: 'SPEAK_TIME', 
    label: 'Speak Current Time', 
    category: 'Utility',
    description: 'Announce current time'
  },
  { 
    id: 'SPEAK_DATE', 
    label: 'Speak Current Date', 
    category: 'Utility',
    description: 'Announce current date'
  },
  { 
    id: 'DAILY_SUMMARY', 
    label: 'Daily Summary', 
    category: 'Wellness',
    description: 'Speak daily activity summary'
  },
  { 
    id: 'MORNING_GREETING', 
    label: 'Morning Greeting', 
    category: 'Wellness',
    description: 'Speak morning greeting'
  },
  { 
    id: 'EVENING_GREETING', 
    label: 'Evening Greeting', 
    category: 'Wellness',
    description: 'Speak evening greeting'
  }
];

export const CATEGORIES = ['Media', 'Utility', 'Wellness', 'Custom'] as const;

export function getActionsByCategory(category: string): AutomationAction[] {
  return AVAILABLE_ACTIONS.filter(action => action.category === category);
}

export function getActionById(id: string): AutomationAction | undefined {
  return AVAILABLE_ACTIONS.find(action => action.id === id);
}

export function validateRule(rule: Partial<AutomationRule>): string[] {
  const errors: string[] = [];

  if (!rule.triggerTime) {
    errors.push('Trigger time is required');
  } else if (!/^\d{2}:\d{2}$/.test(rule.triggerTime)) {
    errors.push('Trigger time must be in HH:MM format');
  }

  if (!rule.action) {
    errors.push('Action is required');
  } else if (!getActionById(rule.action)) {
    errors.push('Invalid action selected');
  }

  if (!rule.category || !CATEGORIES.includes(rule.category as any)) {
    errors.push('Valid category is required');
  }

  return errors;
}

export function createDefaultRule(): Partial<AutomationRule> {
  return {
    triggerTime: '09:00',
    action: AVAILABLE_ACTIONS[0].id,
    category: 'Media',
    enabled: true,
  };
}

export function formatTriggerTime(time: string): string {
  try {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  } catch {
    return time;
  }
}

export function parseTimeInput(input: string): string {
  // Convert various time formats to HH:MM
  const timeRegex = /(\d{1,2}):?(\d{2})?\s*(am|pm)?/i;
  const match = input.match(timeRegex);
  
  if (!match) return input;
  
  let hour = parseInt(match[1]);
  const minute = match[2] ? parseInt(match[2]) : 0;
  const meridiem = match[3]?.toLowerCase();
  
  if (meridiem === 'pm' && hour < 12) hour += 12;
  if (meridiem === 'am' && hour === 12) hour = 0;
  
  return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
}
