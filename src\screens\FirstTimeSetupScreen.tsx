/**
 * FirstTimeSetupScreen.tsx
 * Onboarding wizard for first-time users
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { RootStackParamList } from '@/types';
import { useTheme, getThemedStyles } from '@/utils/ThemeContext';
import { useAppConfig } from '@/utils/AppConfigContext';
import { systemControlAndroid } from '@/services/SystemControlAndroid';

type NavigationProp = StackNavigationProp<RootStackParamList, 'FirstTimeSetup'>;

const FirstTimeSetupScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { theme } = useTheme();
  const { setFirstLaunchComplete } = useAppConfig();
  const styles = getThemedStyles(theme);

  const [currentStep, setCurrentStep] = useState(0);
  const [permissions, setPermissions] = useState({
    microphone: false,
    phone: false,
    camera: false,
    storage: false,
  });

  const steps = [
    {
      title: 'Welcome to JARVIS Lite 🤖',
      content: 'Your personal, offline AI assistant.\nNo internet. No cloud. All on your phone.',
      action: 'Get Started',
    },
    {
      title: 'Microphone Permission 🎤',
      content: 'Used for voice commands only.\nNo internet access or recordings saved.',
      action: 'Request Microphone Access',
    },
    {
      title: 'Choose Your Assistant 🎭',
      content: 'Select personality and voice preferences.',
      action: 'Customize Assistant',
    },
    {
      title: 'Privacy & Consent 🔒',
      content: 'This assistant works 100% offline.\n• No internet needed\n• No data uploaded\n• Logs stored securely on-device',
      action: 'I Agree',
    },
    {
      title: 'Setup Complete! ✅',
      content: 'JARVIS Lite is ready to serve.\nTap the mic to begin.',
      action: 'Start Using JARVIS Lite',
    },
  ];

  const handleNext = async () => {
    if (currentStep === 1) {
      // Request microphone permission
      const granted = await systemControlAndroid.requestPermissions();
      const perms = await systemControlAndroid.checkPermissions();
      setPermissions(perms);
      
      if (!perms.microphone) {
        alert('Microphone permission is required for voice commands.');
        return;
      }
    }

    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete setup
      await setFirstLaunchComplete();
      navigation.replace('StartupGreeting');
    }
  };

  const currentStepData = steps[currentStep];

  return (
    <SafeAreaView style={[styles.container, localStyles.container]}>
      <ScrollView contentContainerStyle={localStyles.content}>
        {/* Progress Indicator */}
        <View style={localStyles.progressContainer}>
          {steps.map((_, index) => (
            <View
              key={index}
              style={[
                localStyles.progressDot,
                {
                  backgroundColor: index <= currentStep ? theme.colors.primary : theme.colors.border,
                },
              ]}
            />
          ))}
        </View>

        {/* Step Content */}
        <View style={localStyles.stepContainer}>
          <Text style={[styles.text, localStyles.title]}>
            {currentStepData.title}
          </Text>
          
          <Text style={[styles.textSecondary, localStyles.content]}>
            {currentStepData.content}
          </Text>

          {/* Special content for specific steps */}
          {currentStep === 1 && (
            <View style={localStyles.permissionStatus}>
              <Text style={[styles.textSecondary, localStyles.permissionText]}>
                Microphone: {permissions.microphone ? '✅ Granted' : '❌ Not granted'}
              </Text>
            </View>
          )}

          {currentStep === 2 && (
            <View style={localStyles.personalityPreview}>
              <Text style={localStyles.emojiPreview}>🤖</Text>
              <Text style={[styles.text, localStyles.personalityText]}>
                JARVIS Mode: Witty, helpful, and tech-savvy
              </Text>
            </View>
          )}
        </View>

        {/* Navigation Buttons */}
        <View style={localStyles.buttonContainer}>
          {currentStep > 0 && (
            <TouchableOpacity
              style={[localStyles.button, localStyles.backButton]}
              onPress={() => setCurrentStep(currentStep - 1)}
            >
              <Text style={[styles.textSecondary, localStyles.buttonText]}>
                Back
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[styles.button, localStyles.nextButton]}
            onPress={handleNext}
          >
            <Text style={[styles.buttonText, localStyles.nextButtonText]}>
              {currentStepData.action}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const localStyles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingVertical: 40,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 40,
  },
  progressDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginHorizontal: 6,
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  content: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 40,
  },
  permissionStatus: {
    marginTop: 20,
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  permissionText: {
    textAlign: 'center',
    fontSize: 14,
  },
  personalityPreview: {
    alignItems: 'center',
    marginTop: 20,
  },
  emojiPreview: {
    fontSize: 48,
    marginBottom: 10,
  },
  personalityText: {
    textAlign: 'center',
    fontSize: 14,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 40,
  },
  button: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
  backButton: {
    backgroundColor: 'transparent',
  },
  nextButton: {
    flex: 1,
    marginLeft: 16,
  },
  buttonText: {
    fontSize: 16,
  },
  nextButtonText: {
    fontWeight: 'bold',
  },
});

export default FirstTimeSetupScreen;
