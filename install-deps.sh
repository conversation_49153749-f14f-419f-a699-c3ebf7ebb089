#!/bin/bash

# JARVIS Lite - Dependency Installation Script
# This script installs all required dependencies for the project

echo "🤖 JARVIS Lite - Installing Dependencies"
echo "========================================"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install Node.js and npm first."
    exit 1
fi

# Install npm dependencies
echo "📦 Installing npm dependencies..."
npm install

# Install additional React Native dependencies
echo "📱 Installing React Native specific dependencies..."

# Core React Native packages
npm install react-native-vector-icons@^10.0.2
npm install moment@^2.29.4

# Voice and TTS packages (these may need manual setup)
echo "🗣️ Installing voice processing packages..."
npm install react-native-tts@^4.1.0

# Storage packages
echo "💾 Installing storage packages..."
npm install react-native-sqlite-storage@^6.0.1
npm install @react-native-async-storage/async-storage@^1.19.3

# Notification packages
echo "🔔 Installing notification packages..."
npm install react-native-push-notification@^8.1.1

# Date/Time packages
echo "📅 Installing date/time packages..."
npm install @react-native-community/datetimepicker@^7.6.2

# System control packages (may need manual setup)
echo "⚙️ Installing system control packages..."
echo "Note: Some packages may require manual Android setup"

# Background processing (may not be available)
echo "🔄 Installing background processing packages..."
echo "Note: Background job packages may need alternative implementations"

echo ""
echo "✅ Basic dependencies installed!"
echo ""
echo "📋 Next Steps:"
echo "1. Run 'npx react-native link' to link native dependencies"
echo "2. For Android: Run 'cd android && ./gradlew clean'"
echo "3. Run 'npm run android' to start the app"
echo ""
echo "⚠️  Note: Some advanced features may require additional setup:"
echo "   - Voice recognition (Vosk) requires manual integration"
echo "   - System control requires Android permissions"
echo "   - Background tasks may need alternative implementations"
echo ""
echo "🚀 Ready to start JARVIS Lite development!"
