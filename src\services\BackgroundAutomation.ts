/**
 * BackgroundAutomation.ts
 * Handles background automation tasks and scheduled actions
 * Runs automations even when app is in background
 */

import { AppState, Platform } from 'react-native';
import moment from 'moment';

// Mock implementations for development
const BackgroundJob = {
  register: (options: any) => console.log('[BackgroundJob] Register:', options),
  start: (options: any) => console.log('[BackgroundJob] Start:', options),
  stop: (options: any) => console.log('[BackgroundJob] Stop:', options),
  on: (event: string, callback: Function) => console.log('[BackgroundJob] On:', event),
};

// Mock functions - these will be replaced with actual imports when modules are available
async function getAllAutomationRules(): Promise<any[]> {
  return [];
}

async function analyzePatterns(): Promise<void> {
  console.log('[BackgroundAutomation] Mock pattern analysis');
}

function getSuggestions(hour: string): any[] {
  return [];
}

async function speakDailySummary(): Promise<void> {
  console.log('[BackgroundAutomation] Mock daily summary');
}

async function speak(text: string): Promise<void> {
  console.log('[BackgroundAutomation] Would speak:', text);
}

async function logInteraction(entry: any): Promise<void> {
  console.log('[BackgroundAutomation] Mock log interaction:', entry);
}

interface BackgroundTask {
  id: string;
  name: string;
  schedule: string; // cron-like format or 'interval:minutes'
  action: () => Promise<void>;
  isActive: boolean;
  lastRun?: Date;
}

let backgroundTasks: BackgroundTask[] = [];
let isBackgroundModeActive = false;
let appStateSubscription: any = null;

export function initializeBackgroundAutomation(): void {
  console.log('[BackgroundAutomation] Initializing...');

  // Setup default background tasks
  setupDefaultTasks();

  // Configure background job
  configureBackgroundJob();

  // Listen for app state changes
  setupAppStateListener();

  console.log('[BackgroundAutomation] Initialized with', backgroundTasks.length, 'tasks');
}

function setupDefaultTasks(): void {
  backgroundTasks = [
    {
      id: 'morning_greeting',
      name: 'Morning Greeting',
      schedule: '08:00', // 8 AM daily
      action: async () => {
        await speak('Good morning! JARVIS is ready to assist you today.');
        await logInteraction({
          timestamp: new Date().toISOString(),
          command: 'background:morning_greeting',
          intent: 'AUTOMATION_GREETING',
          actionTaken: 'morning_greeting_spoken',
        });
      },
      isActive: true,
    },
    {
      id: 'evening_summary',
      name: 'Evening Summary',
      schedule: '20:00', // 8 PM daily
      action: async () => {
        await speakDailySummary();
        await logInteraction({
          timestamp: new Date().toISOString(),
          command: 'background:evening_summary',
          intent: 'AUTOMATION_SUMMARY',
          actionTaken: 'daily_summary_spoken',
        });
      },
      isActive: true,
    },
    {
      id: 'pattern_analysis',
      name: 'Pattern Analysis',
      schedule: 'interval:60', // Every hour
      action: async () => {
        await analyzePatterns();
        console.log('[BackgroundAutomation] Pattern analysis completed');
      },
      isActive: true,
    },
    {
      id: 'smart_suggestions',
      name: 'Smart Suggestions Check',
      schedule: 'interval:30', // Every 30 minutes
      action: async () => {
        const currentHour = moment().format('HH:00');
        const suggestions = getSuggestions(currentHour);

        if (suggestions.length > 0 && Math.random() < 0.3) { // 30% chance to suggest
          const suggestion = suggestions[0];
          await speak(`Based on your patterns, you might want to ${suggestion.resultAction}`);
        }
      },
      isActive: true,
    },
    {
      id: 'automation_check',
      name: 'Automation Rules Check',
      schedule: 'interval:5', // Every 5 minutes
      action: async () => {
        await checkAutomationRules();
      },
      isActive: true,
    },
  ];
}

function configureBackgroundJob(): void {
  // Register background job for automation tasks
  BackgroundJob.register({
    jobKey: 'backgroundAutomation',
    requiredNetworkType: 'none',
    requiresCharging: false,
    requiresDeviceIdle: false,
  });

  // Set up periodic execution
  BackgroundJob.on('backgroundAutomation', () => {
    executeScheduledTasks();
  });
}

function setupAppStateListener(): void {
  appStateSubscription = AppState.addEventListener('change', handleAppStateChange);
}

function handleAppStateChange(nextAppState: string): void {
  console.log('[BackgroundAutomation] App state changed to:', nextAppState);

  if (nextAppState === 'background') {
    startBackgroundMode();
  } else if (nextAppState === 'active') {
    stopBackgroundMode();
  }
}

function startBackgroundMode(): void {
  if (isBackgroundModeActive) return;

  console.log('[BackgroundAutomation] Starting background mode');
  isBackgroundModeActive = true;

  // Start background job with 1-minute intervals
  BackgroundJob.start({
    jobKey: 'backgroundAutomation',
    period: 60000, // 1 minute
  });
}

function stopBackgroundMode(): void {
  if (!isBackgroundModeActive) return;

  console.log('[BackgroundAutomation] Stopping background mode');
  isBackgroundModeActive = false;

  BackgroundJob.stop({
    jobKey: 'backgroundAutomation',
  });
}

async function executeScheduledTasks(): Promise<void> {
  const now = moment();

  for (const task of backgroundTasks) {
    if (!task.isActive) continue;

    if (shouldExecuteTask(task, now)) {
      try {
        console.log(`[BackgroundAutomation] Executing task: ${task.name}`);
        await task.action();
        task.lastRun = now.toDate();
      } catch (error) {
        console.error(`[BackgroundAutomation] Error executing task ${task.name}:`, error);
      }
    }
  }
}

function shouldExecuteTask(task: BackgroundTask, now: moment.Moment): boolean {
  const { schedule, lastRun } = task;

  if (schedule.startsWith('interval:')) {
    // Interval-based scheduling
    const minutes = parseInt(schedule.split(':')[1]);
    if (!lastRun) return true;

    const nextRun = moment(lastRun).add(minutes, 'minutes');
    return now.isSameOrAfter(nextRun);
  } else {
    // Time-based scheduling (HH:MM format)
    const [hour, minute] = schedule.split(':').map(Number);
    const scheduledTime = now.clone().hour(hour).minute(minute).second(0);

    // Check if it's time to run and hasn't run today
    if (now.isSameOrAfter(scheduledTime, 'minute')) {
      if (!lastRun) return true;

      const lastRunMoment = moment(lastRun);
      return !lastRunMoment.isSame(now, 'day');
    }
  }

  return false;
}

async function checkAutomationRules(): Promise<void> {
  try {
    const rules = getAllAutomationRules();
    const now = moment();

    for (const rule of rules) {
      if (!rule.isActive) continue;

      if (rule.trigger.type === 'time') {
        const { hour, minute } = rule.trigger.condition;
        const scheduledTime = now.clone().hour(hour).minute(minute || 0);

        if (now.isSame(scheduledTime, 'minute')) {
          // Check if already triggered today
          const lastTriggered = rule.lastTriggered ? moment(rule.lastTriggered) : null;
          const alreadyTriggeredToday = lastTriggered && lastTriggered.isSame(now, 'day');

          if (!alreadyTriggeredToday) {
            await executeAutomationRule(rule);
          }
        }
      }
    }
  } catch (error) {
    console.error('[BackgroundAutomation] Error checking automation rules:', error);
  }
}

async function executeAutomationRule(rule: any): Promise<void> {
  console.log(`[BackgroundAutomation] Executing automation rule: ${rule.name}`);

  try {
    for (const action of rule.actions) {
      switch (action.type) {
        case 'speak':
          await speak(action.parameters.text);
          break;
        case 'open_app':
          // Note: Opening apps from background may not work on all Android versions
          console.log(`[BackgroundAutomation] Would open app: ${action.parameters.appName}`);
          break;
        case 'system_control':
          console.log(`[BackgroundAutomation] Would execute system control: ${action.parameters.action}`);
          break;
        default:
          console.log(`[BackgroundAutomation] Unknown action type: ${action.type}`);
      }
    }

    // Update last triggered time
    rule.lastTriggered = new Date().toISOString();

    // Log the automation execution
    await logInteraction({
      timestamp: new Date().toISOString(),
      command: `automation:${rule.name}`,
      intent: 'AUTOMATION_RULE',
      parameters: JSON.stringify(rule.trigger),
      actionTaken: `executed_${rule.actions.length}_actions`,
    });
  } catch (error) {
    console.error(`[BackgroundAutomation] Error executing automation rule ${rule.name}:`, error);
  }
}

export function addBackgroundTask(task: Omit<BackgroundTask, 'id'>): string {
  const newTask: BackgroundTask = {
    ...task,
    id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  };

  backgroundTasks.push(newTask);
  console.log('[BackgroundAutomation] Added background task:', newTask.name);

  return newTask.id;
}

export function removeBackgroundTask(taskId: string): boolean {
  const index = backgroundTasks.findIndex(task => task.id === taskId);
  if (index !== -1) {
    const removedTask = backgroundTasks.splice(index, 1)[0];
    console.log('[BackgroundAutomation] Removed background task:', removedTask.name);
    return true;
  }
  return false;
}

export function toggleBackgroundTask(taskId: string): boolean {
  const task = backgroundTasks.find(t => t.id === taskId);
  if (task) {
    task.isActive = !task.isActive;
    console.log(`[BackgroundAutomation] Task ${task.name} is now ${task.isActive ? 'active' : 'inactive'}`);
    return true;
  }
  return false;
}

export function getAllBackgroundTasks(): BackgroundTask[] {
  return [...backgroundTasks];
}

export function getActiveBackgroundTasks(): BackgroundTask[] {
  return backgroundTasks.filter(task => task.isActive);
}

export function isBackgroundAutomationActive(): boolean {
  return isBackgroundModeActive;
}

export function forceExecuteTask(taskId: string): Promise<void> {
  const task = backgroundTasks.find(t => t.id === taskId);
  if (task) {
    return task.action();
  }
  return Promise.reject(new Error('Task not found'));
}

export function cleanup(): void {
  console.log('[BackgroundAutomation] Cleaning up...');

  stopBackgroundMode();

  if (appStateSubscription) {
    appStateSubscription.remove();
    appStateSubscription = null;
  }

  BackgroundJob.stop({ jobKey: 'backgroundAutomation' });

  console.log('[BackgroundAutomation] Cleanup completed');
}

// Export types
export type { BackgroundTask };
