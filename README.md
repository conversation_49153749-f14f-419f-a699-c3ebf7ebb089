# JARVIS Lite - Offline Android AI Assistant

JARVIS <PERSON>te is a comprehensive offline AI assistant for Android devices that operates entirely without internet connectivity. Built with React Native and TypeScript, it provides voice commands, intelligent automation, and personalized assistance while keeping all data secure on your device.

## 🌟 Features

### 🗣 Voice Processing
- **Offline Speech-to-Text**: Uses Vosk for real-time voice recognition
- **React Native TTS**: High-quality text-to-speech with multiple voice profiles
- **Voice Profiles**: JARVIS, Calm, and Robotic personality modes
- **Real-time Processing**: Immediate voice command processing and response

### 🧠 AI Intelligence
- **Intent Parsing**: Rule-based pattern matching with keyword fallback
- **Local LLM**: Mock conversational AI with future GGUF model support
- **Pattern Learning**: Frequency-based habit analysis from interaction logs
- **Smart Automation**: Time-based and event-driven automation rules

### 📱 System Control
- **App Management**: Launch camera, gallery, YouTube, WhatsApp by voice
- **Device Control**: Flashlight toggle, volume/brightness adjustment
- **Phone Functions**: Make calls with permission handling
- **System Integration**: React Native System Setting integration

### 📊 Analytics & Learning
- **Usage Tracking**: Local SQLite logging of all interactions
- **Behavior Analysis**: Pattern recognition and habit learning
- **Smart Suggestions**: Proactive recommendations based on usage
- **Privacy-First**: All data stays on device

### 🎭 Personalization
- **Multiple Personalities**: Professional, Playful, Calm, JARVIS modes
- **Custom Avatars**: Emoji-based assistant representation
- **Voice Profiles**: Multiple TTS voice options
- **Response Styles**: Configurable formality and verbosity

## 🏗 Architecture

### Core Modules
- **VoiceProcessor.ts**: Vosk-based speech-to-text processing
- **IntentParser.ts**: Rule-based intent recognition with keyword fallback
- **JARVIS.ts**: Main orchestrator coordinating all modules
- **InteractionLogger.ts**: SQLite-based activity logging with export
- **LocalMemory.ts**: Pattern learning from interaction frequency
- **LLMEngine.ts**: Mock conversational AI with future GGUF support
- **OfflineTTS.ts**: React Native TTS with voice profiles
- **SystemControlAndroid.ts**: System control via react-native-system-setting
- **AutomationEngine.ts**: Time-based and event-driven automation

### UI Screens
- **MainAssistantScreen**: Primary interface with floating mic button
- **ActivityTimelineScreen**: Usage history and analytics
- **SettingsScreen**: Configuration and preferences
- **AssistantPersonalityScreen**: Personality customization
- **ReminderScheduleScreen**: Task and reminder management
- **AutomationBuilderScreen**: Custom automation creation
- **LearningInsightsScreen**: AI learning dashboard
- **DebugAndLogsScreen**: Developer tools and diagnostics

## 🚀 Getting Started

### Prerequisites
- Node.js 16+
- React Native CLI
- Android Studio
- Android SDK (API 21+)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd jarvis-lite
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Install iOS dependencies (if needed)**
   ```bash
   cd ios && pod install && cd ..
   ```

4. **Start Metro bundler**
   ```bash
   npm start
   ```

5. **Run on Android**
   ```bash
   npm run android
   ```

### Development Setup

1. **Configure Android permissions**
   - Microphone access for voice input
   - Phone access for call functionality
   - Camera access for camera control
   - Storage access for local data

2. **Set up native modules**
   - Voice processing native bridge
   - System control native bridge
   - TTS engine integration

## 📱 Usage

### Voice Commands
- **App Control**: "Open camera", "Launch Spotify"
- **System Control**: "Turn on flashlight", "Volume up"
- **Information**: "What time is it?", "Show my activity"
- **Reminders**: "Remind me to call mom at 5 PM"
- **Automation**: "When I plug in charger, play music"

### Automation Examples
- Time-based triggers: "At 9 AM, open calendar"
- App-based triggers: "When WhatsApp opens, set volume to silent"
- Device state triggers: "When WiFi connects, sync data"

## 🔒 Privacy & Security

- **100% Offline**: No internet required for core functionality
- **Local Storage**: All data stored in encrypted SQLite database
- **No Cloud Sync**: Data never leaves your device
- **Transparent Logging**: Full control over what gets tracked
- **Data Retention**: Configurable automatic cleanup

## 🛠 Development

### Project Structure
```
src/
├── components/          # Reusable UI components
├── screens/            # Main application screens
├── services/           # Core business logic
├── types/              # TypeScript type definitions
├── utils/              # Utility functions and contexts
└── App.tsx            # Main application component
```

### Key Technologies
- **React Native 0.72+**: Cross-platform mobile framework
- **TypeScript**: Type-safe development
- **Vosk**: Offline speech recognition
- **React Native TTS**: Text-to-speech synthesis
- **SQLite**: Local database storage with react-native-sqlite-storage
- **React Navigation**: Screen navigation
- **System Setting**: Device control via react-native-system-setting
- **Intent Launcher**: App launching via react-native-intent-launcher
- **Moment.js**: Date/time handling for automation
- **Reanimated**: Smooth animations

### Adding New Features

1. **Voice Commands**: Add patterns to `IntentParser.ts`
2. **System Actions**: Extend `SystemControlAndroid.ts`
3. **UI Screens**: Create new components in `screens/`
4. **Automations**: Add triggers in `AutomationBuilder`

## 🧪 Testing

```bash
# Run tests
npm test

# Run linting
npm run lint

# Type checking
npx tsc --noEmit
```

## 📦 Building

### Debug Build
```bash
npm run android
```

### Release Build
```bash
cd android
./gradlew assembleRelease
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Vosk for offline speech recognition
- Coqui TTS for text-to-speech
- React Native community for excellent tooling
- All contributors and testers

## 📞 Support

For issues, questions, or contributions:
- Create an issue on GitHub
- Check the documentation
- Review existing discussions

---

**JARVIS Lite** - Your personal, private, offline AI assistant. No cloud, no tracking, just intelligent assistance when you need it.
