@echo off
echo ========================================
echo JARVIS Lite - APK Creation Script
echo ========================================
echo.

echo Creating standalone APK...
echo.

echo Step 1: Installing APK Builder...
npm install -g react-native-make-apk

echo.
echo Step 2: Building APK...
npx react-native-make-apk --output=jarvis-lite.apk

echo.
echo Step 3: Installing on device...
adb install -r jarvis-lite.apk

if %errorlevel% neq 0 (
    echo ❌ Installation failed
    echo Please install manually: adb install jarvis-lite.apk
    goto :end
)

echo ✅ JARVIS Lite APK installed successfully!
echo.
echo Starting the app...
adb shell am start -n com.jarvislite/.MainActivity

:end
pause
