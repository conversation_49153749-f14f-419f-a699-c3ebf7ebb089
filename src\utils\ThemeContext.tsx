/**
 * ThemeContext.tsx
 * Theme management for JARVIS Lite
 * Supports light/dark mode with custom colors
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  error: string;
  warning: string;
  success: string;
  info: string;
}

export interface Theme {
  isDark: boolean;
  colors: ThemeColors;
}

const lightTheme: Theme = {
  isDark: false,
  colors: {
    primary: '#2196F3',
    secondary: '#03DAC6',
    accent: '#FF5722',
    background: '#FFFFFF',
    surface: '#F5F5F5',
    text: '#212121',
    textSecondary: '#757575',
    border: '#E0E0E0',
    error: '#F44336',
    warning: '#FF9800',
    success: '#4CAF50',
    info: '#2196F3',
  },
};

const darkTheme: Theme = {
  isDark: true,
  colors: {
    primary: '#BB86FC',
    secondary: '#03DAC6',
    accent: '#CF6679',
    background: '#121212',
    surface: '#1E1E1E',
    text: '#FFFFFF',
    textSecondary: '#B3B3B3',
    border: '#333333',
    error: '#CF6679',
    warning: '#FFB74D',
    success: '#81C784',
    info: '#64B5F6',
  },
};

// JARVIS-specific theme variants
const jarvisLightTheme: Theme = {
  ...lightTheme,
  colors: {
    ...lightTheme.colors,
    primary: '#1976D2',
    accent: '#FFC107',
    surface: '#F8F9FA',
  },
};

const jarvisDarkTheme: Theme = {
  ...darkTheme,
  colors: {
    ...darkTheme.colors,
    primary: '#64B5F6',
    accent: '#FFD54F',
    background: '#0D1117',
    surface: '#161B22',
  },
};

interface ThemeContextType {
  theme: Theme;
  isDark: boolean;
  toggleTheme: () => void;
  setTheme: (themeName: 'light' | 'dark' | 'system') => void;
  themeMode: 'light' | 'dark' | 'system';
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
  initialTheme?: 'light' | 'dark' | 'system';
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  initialTheme = 'system',
}) => {
  const systemColorScheme = useColorScheme();
  const [themeMode, setThemeMode] = useState<'light' | 'dark' | 'system'>(initialTheme);
  const [currentTheme, setCurrentTheme] = useState<Theme>(
    systemColorScheme === 'dark' ? jarvisDarkTheme : jarvisLightTheme
  );

  useEffect(() => {
    loadThemePreference();
  }, []);

  useEffect(() => {
    updateTheme();
  }, [themeMode, systemColorScheme]);

  /**
   * Load theme preference from storage
   */
  const loadThemePreference = async (): Promise<void> => {
    try {
      const savedTheme = await AsyncStorage.getItem('theme_preference');
      if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
        setThemeMode(savedTheme as 'light' | 'dark' | 'system');
      }
    } catch (error) {
      console.error('Error loading theme preference:', error);
    }
  };

  /**
   * Save theme preference to storage
   */
  const saveThemePreference = async (theme: 'light' | 'dark' | 'system'): Promise<void> => {
    try {
      await AsyncStorage.setItem('theme_preference', theme);
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  /**
   * Update current theme based on mode and system preference
   */
  const updateTheme = (): void => {
    let newTheme: Theme;

    switch (themeMode) {
      case 'light':
        newTheme = jarvisLightTheme;
        break;
      case 'dark':
        newTheme = jarvisDarkTheme;
        break;
      case 'system':
      default:
        newTheme = systemColorScheme === 'dark' ? jarvisDarkTheme : jarvisLightTheme;
        break;
    }

    setCurrentTheme(newTheme);
  };

  /**
   * Toggle between light and dark theme
   */
  const toggleTheme = (): void => {
    const newMode = currentTheme.isDark ? 'light' : 'dark';
    setTheme(newMode);
  };

  /**
   * Set specific theme mode
   */
  const setTheme = (themeName: 'light' | 'dark' | 'system'): void => {
    setThemeMode(themeName);
    saveThemePreference(themeName);
  };

  const contextValue: ThemeContextType = {
    theme: currentTheme,
    isDark: currentTheme.isDark,
    toggleTheme,
    setTheme,
    themeMode,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Hook to use theme context
 */
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

/**
 * Create themed styles helper
 */
export const createThemedStyles = <T extends Record<string, any>>(
  styleFactory: (theme: Theme) => T
) => {
  return (theme: Theme): T => styleFactory(theme);
};

/**
 * Common themed style utilities
 */
export const getThemedStyles = (theme: Theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  surface: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    padding: 16,
    marginVertical: 8,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: theme.isDark ? 0.3 : 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  text: {
    color: theme.colors.text,
    fontSize: 16,
  },
  textSecondary: {
    color: theme.colors.textSecondary,
    fontSize: 14,
  },
  button: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  buttonText: {
    color: theme.isDark ? theme.colors.background : theme.colors.background,
    fontSize: 16,
    fontWeight: 'bold' as const,
  },
  input: {
    backgroundColor: theme.colors.surface,
    borderColor: theme.colors.border,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    color: theme.colors.text,
    fontSize: 16,
  },
  card: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    marginHorizontal: 16,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: theme.isDark ? 0.3 : 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  divider: {
    height: 1,
    backgroundColor: theme.colors.border,
    marginVertical: 8,
  },
  iconButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.primary,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  floatingButton: {
    position: 'absolute' as const,
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: theme.colors.accent,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});

export default ThemeProvider;
