/**
 * OnboardingScreen.tsx
 * Welcome screen with voice tips and usage guide
 */

import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

export default function OnboardingScreen() {
  const navigation = useNavigation();

  const tips = [
    {
      icon: 'microphone',
      text: 'Say: "Remind me to drink water"',
      description: 'Set voice reminders easily'
    },
    {
      icon: 'clock-outline',
      text: 'Say: "What runs at 9?" or "Show my rules"',
      description: 'Check your automation schedule'
    },
    {
      icon: 'delete',
      text: 'Say: "Delete rule at 8:30"',
      description: 'Remove automations by voice'
    },
    {
      icon: 'calendar-clock',
      text: 'Say: "How many tasks today?"',
      description: 'Get your daily task count'
    },
    {
      icon: 'cog',
      text: 'Say: "Open settings"',
      description: 'Access settings with voice'
    },
    {
      icon: 'chart-line',
      text: 'Track automations in real time',
      description: 'Monitor your productivity'
    }
  ];

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.heading}>🧠 Welcome to JARVIS Lite</Text>
      
      <Text style={styles.description}>
        Your offline AI assistant is ready to help you automate your daily tasks and boost productivity.
      </Text>

      <View style={styles.tipsContainer}>
        {tips.map((tip, index) => (
          <View key={index} style={styles.tipItem}>
            <Icon name={tip.icon} size={24} color="#00FFB2" style={styles.tipIcon} />
            <View style={styles.tipContent}>
              <Text style={styles.tipText}>{tip.text}</Text>
              <Text style={styles.tipDescription}>{tip.description}</Text>
            </View>
          </View>
        ))}
      </View>

      <View style={styles.featuresContainer}>
        <Text style={styles.featuresTitle}>✨ Key Features</Text>
        <Text style={styles.featureItem}>🔒 100% Offline - Your data never leaves your device</Text>
        <Text style={styles.featureItem}>🎙️ Voice Control - Natural language commands</Text>
        <Text style={styles.featureItem}>⚡ Smart Automation - Time-based task execution</Text>
        <Text style={styles.featureItem}>📊 Pattern Learning - Adapts to your habits</Text>
        <Text style={styles.featureItem}>🔐 Secure - Biometric settings protection</Text>
      </View>

      <TouchableOpacity
        style={styles.button}
        onPress={() => navigation.replace('MainAssistant' as never)}
      >
        <Text style={styles.buttonText}>🚀 Get Started</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.skipButton}
        onPress={() => navigation.replace('MainAssistant' as never)}
      >
        <Text style={styles.skipButtonText}>Skip Tutorial</Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#111',
  },
  heading: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#00FFB2',
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#ccc',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
    paddingHorizontal: 16,
  },
  tipsContainer: {
    width: '100%',
    marginBottom: 32,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  tipIcon: {
    marginRight: 16,
    width: 24,
  },
  tipContent: {
    flex: 1,
  },
  tipText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
    marginBottom: 4,
  },
  tipDescription: {
    fontSize: 14,
    color: '#888',
  },
  featuresContainer: {
    width: '100%',
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 20,
    marginBottom: 32,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#00FFB2',
    marginBottom: 16,
    textAlign: 'center',
  },
  featureItem: {
    fontSize: 14,
    color: '#ccc',
    marginBottom: 8,
    lineHeight: 20,
  },
  button: {
    backgroundColor: '#00FFB2',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 8,
    marginBottom: 16,
    minWidth: 200,
  },
  buttonText: {
    textAlign: 'center',
    fontWeight: 'bold',
    color: '#000',
    fontSize: 18,
  },
  skipButton: {
    paddingVertical: 12,
  },
  skipButtonText: {
    color: '#666',
    fontSize: 16,
    textAlign: 'center',
  },
});
