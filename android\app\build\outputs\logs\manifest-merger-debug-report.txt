-- Merging decision tree log ---
manifest
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
MERGED from [com.facebook.react:react-android:0.72.6] F:\gradle-cache-new\caches\transforms-3\8da87a713126dbd68b0d50201bac03f6\transformed\jetified-react-android-0.72.6-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] F:\gradle-cache-new\caches\transforms-3\c63fa670d27ad95fe21f3b21f3bb2705\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [org.webkit:android-jsc:r174650] F:\gradle-cache-new\caches\transforms-3\4ad66c3c79c5ebd2aa0ce145fc6f76ee\transformed\jetified-android-jsc-r174650\AndroidManifest.xml:1:1-3:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] F:\gradle-cache-new\caches\transforms-3\b99849291fad6b9eedef06190f8ed001\transformed\jetified-imagepipeline-okhttp3-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat:1.4.1] F:\gradle-cache-new\caches\transforms-3\1b52c21370a38b025fe38599105dc471\transformed\appcompat-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.4.1] F:\gradle-cache-new\caches\transforms-3\a62663212d8018ec07cb8901f524e165\transformed\jetified-appcompat-resources-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] F:\gradle-cache-new\caches\transforms-3\af51cb665c8d7bf7ef7bd1d24cdcdc71\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] F:\gradle-cache-new\caches\transforms-3\09133080737623630f01e7bd484eb603\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.2.4] F:\gradle-cache-new\caches\transforms-3\5f3e6f0b373cf06cff8e5b8dbac748e6\transformed\jetified-activity-1.2.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] F:\gradle-cache-new\caches\transforms-3\629a487764eb5b7a55262072b2636b14\transformed\jetified-emoji2-views-helper-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache-new\caches\transforms-3\b7a4b4b32fa1a3f9ef62577eecee019c\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.startup:startup-runtime:1.0.0] F:\gradle-cache-new\caches\transforms-3\1452abd06e3eae42a169aa0a1af4c1f0\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0] F:\gradle-cache-new\caches\transforms-3\3a903aae5748c6b1c0ca2a3cf6b64a58\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fbjni:fbjni:0.3.0] F:\gradle-cache-new\caches\transforms-3\4506787739007a25dba5df85abed42f8\transformed\jetified-fbjni-0.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:fresco:2.5.0] F:\gradle-cache-new\caches\transforms-3\995a2517b9ee5c7965aa1d0eb2bee262\transformed\jetified-fresco-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:drawee:2.5.0] F:\gradle-cache-new\caches\transforms-3\bd363b705b40f5d54837117e209a877a\transformed\jetified-drawee-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:middleware:2.5.0] F:\gradle-cache-new\caches\transforms-3\19887b8cc904e8a897809cc815434274\transformed\jetified-middleware-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:ui-common:2.5.0] F:\gradle-cache-new\caches\transforms-3\ed0cd6359946dc4ae454a9bc9cb7ce47\transformed\jetified-ui-common-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:nativeimagefilters:2.5.0] F:\gradle-cache-new\caches\transforms-3\4dad05961f7ccbc0a684da03eb97bf3b\transformed\jetified-nativeimagefilters-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-native:2.5.0] F:\gradle-cache-new\caches\transforms-3\ee3687805c9531382fe806f923a73b87\transformed\jetified-memory-type-native-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-java:2.5.0] F:\gradle-cache-new\caches\transforms-3\f5484f588c2db913022ab4492c0fd4da\transformed\jetified-memory-type-java-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-native:2.5.0] F:\gradle-cache-new\caches\transforms-3\375cfbc488793c6148fb194316b8b0a0\transformed\jetified-imagepipeline-native-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-ashmem:2.5.0] F:\gradle-cache-new\caches\transforms-3\8667c21550ecb50554f53c358d028513\transformed\jetified-memory-type-ashmem-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline:2.5.0] F:\gradle-cache-new\caches\transforms-3\34fccc5487a9c865193817d090e9fa3b\transformed\jetified-imagepipeline-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.5.0] F:\gradle-cache-new\caches\transforms-3\8670a87ece610fe795012529cbcdb3c9\transformed\jetified-nativeimagetranscoder-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-base:2.5.0] F:\gradle-cache-new\caches\transforms-3\fdb920e6c8a927e2d8466f080c79a369\transformed\jetified-imagepipeline-base-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:soloader:2.5.0] F:\gradle-cache-new\caches\transforms-3\fe625571ae7635ea70d6bebec7dd304e\transformed\jetified-soloader-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache-new\caches\transforms-3\7a78738add03003b048c09f83d1118f1\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:2:1-17:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] F:\gradle-cache-new\caches\transforms-3\2af432480cf1f390bc76f762c4801bf9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] F:\gradle-cache-new\caches\transforms-3\670a8141585c00088264dc1879ba1e39\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] F:\gradle-cache-new\caches\transforms-3\0259d135d69d9853d2cec2e88a660727\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] F:\gradle-cache-new\caches\transforms-3\6d30fe891b6b72fd530a13844e3eab38\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] F:\gradle-cache-new\caches\transforms-3\9ea26edee1cf8786b25e840c1f6be8ab\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] F:\gradle-cache-new\caches\transforms-3\cbf2828846bf324bca3004c988c813f4\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] F:\gradle-cache-new\caches\transforms-3\2262f4b93e23c313cd4bdc9d63b84d4a\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.7.0] F:\gradle-cache-new\caches\transforms-3\a8962bb5790e2a717af128f173a274ed\transformed\core-1.7.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] F:\gradle-cache-new\caches\transforms-3\9487bcbe8122303e4d29f9e52ccb130e\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] F:\gradle-cache-new\caches\transforms-3\24c0379f7e52a0d0823681718815b7c4\transformed\jetified-lifecycle-viewmodel-savedstate-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.1.0] F:\gradle-cache-new\caches\transforms-3\2109f8a22eecb207574bb92d7e3948da\transformed\jetified-savedstate-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] F:\gradle-cache-new\caches\transforms-3\4f73a8555db64d6779fd86b6be461ac7\transformed\lifecycle-viewmodel-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] F:\gradle-cache-new\caches\transforms-3\a0346a322bb1089a487390e996b59837\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] F:\gradle-cache-new\caches\transforms-3\1c9666c86b2a52d47fd0450d25f3dbdc\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] F:\gradle-cache-new\caches\transforms-3\6c76bde2187d1415cb36e9f30f96cb59\transformed\lifecycle-livedata-core-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.4.0] F:\gradle-cache-new\caches\transforms-3\0160e3d4e8b11fca2887edd9f032b0bc\transformed\lifecycle-runtime-2.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] F:\gradle-cache-new\caches\transforms-3\9d9a10e0d1651e93ebf8ab54caace060\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:fbcore:2.5.0] F:\gradle-cache-new\caches\transforms-3\6cba262f0bee94129cb5a913e6bf5cd7\transformed\jetified-fbcore-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.annotation:annotation-experimental:1.1.0] F:\gradle-cache-new\caches\transforms-3\c76d94935246982c69a49c04f65a0632\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:17:1-24:12
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
	package
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:2:5-29
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:4:5-67
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:4:22-64
uses-permission#android.permission.RECORD_AUDIO
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:5:5-71
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:5:22-68
uses-permission#android.permission.CALL_PHONE
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:6:5-69
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:6:22-66
uses-permission#android.permission.CAMERA
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:7:5-65
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:7:22-62
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:8:5-81
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:9:5-80
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:9:22-77
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:10:5-76
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:10:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:11:5-76
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:11:22-73
uses-permission#android.permission.BLUETOOTH
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:12:5-68
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:12:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:13:5-74
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:13:22-71
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:14:5-78
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:14:22-75
uses-permission#android.permission.PACKAGE_USAGE_STATS
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:15:5-78
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:15:22-75
uses-permission#android.permission.ACCESS_NOTIFICATION_POLICY
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:16:5-85
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:16:22-82
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:17:5-77
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:17:22-74
uses-permission#android.permission.WRITE_SETTINGS
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:18:5-73
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:18:22-70
uses-permission#android.permission.FLASHLIGHT
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:19:5-69
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:19:22-66
application
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:21:5-40:19
MERGED from [org.webkit:android-jsc:r174650] F:\gradle-cache-new\caches\transforms-3\4ad66c3c79c5ebd2aa0ce145fc6f76ee\transformed\jetified-android-jsc-r174650\AndroidManifest.xml:2:3-17
MERGED from [org.webkit:android-jsc:r174650] F:\gradle-cache-new\caches\transforms-3\4ad66c3c79c5ebd2aa0ce145fc6f76ee\transformed\jetified-android-jsc-r174650\AndroidManifest.xml:2:3-17
MERGED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache-new\caches\transforms-3\b7a4b4b32fa1a3f9ef62577eecee019c\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache-new\caches\transforms-3\b7a4b4b32fa1a3f9ef62577eecee019c\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.0.0] F:\gradle-cache-new\caches\transforms-3\1452abd06e3eae42a169aa0a1af4c1f0\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.0.0] F:\gradle-cache-new\caches\transforms-3\1452abd06e3eae42a169aa0a1af4c1f0\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:25:5-31:19
MERGED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache-new\caches\transforms-3\7a78738add03003b048c09f83d1118f1\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache-new\caches\transforms-3\7a78738add03003b048c09f83d1118f1\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
MERGED from [androidx.core:core:1.7.0] F:\gradle-cache-new\caches\transforms-3\a8962bb5790e2a717af128f173a274ed\transformed\core-1.7.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.7.0] F:\gradle-cache-new\caches\transforms-3\a8962bb5790e2a717af128f173a274ed\transformed\core-1.7.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] F:\gradle-cache-new\caches\transforms-3\a0346a322bb1089a487390e996b59837\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] F:\gradle-cache-new\caches\transforms-3\a0346a322bb1089a487390e996b59837\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.7.0] F:\gradle-cache-new\caches\transforms-3\a8962bb5790e2a717af128f173a274ed\transformed\core-1.7.0\AndroidManifest.xml:24:18-86
	android:label
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:23:7-39
	android:roundIcon
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:25:7-52
	android:icon
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:24:7-41
	android:allowBackup
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:26:7-34
	android:theme
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:27:7-38
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:22:7-38
activity#com.jarvislite.MainActivity
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:28:7-39:18
	android:label
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:30:9-41
	android:launchMode
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:32:9-40
	android:windowSoftInputMode
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:33:9-51
	android:exported
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:34:9-32
	android:configChanges
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:31:9-118
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:29:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:35:9-38:25
action#android.intent.action.MAIN
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:36:13-65
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:36:21-62
category#android.intent.category.LAUNCHER
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:37:13-73
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:37:23-70
uses-sdk
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
MERGED from [com.facebook.react:react-android:0.72.6] F:\gradle-cache-new\caches\transforms-3\8da87a713126dbd68b0d50201bac03f6\transformed\jetified-react-android-0.72.6-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.72.6] F:\gradle-cache-new\caches\transforms-3\8da87a713126dbd68b0d50201bac03f6\transformed\jetified-react-android-0.72.6-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] F:\gradle-cache-new\caches\transforms-3\c63fa670d27ad95fe21f3b21f3bb2705\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] F:\gradle-cache-new\caches\transforms-3\c63fa670d27ad95fe21f3b21f3bb2705\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] F:\gradle-cache-new\caches\transforms-3\b99849291fad6b9eedef06190f8ed001\transformed\jetified-imagepipeline-okhttp3-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] F:\gradle-cache-new\caches\transforms-3\b99849291fad6b9eedef06190f8ed001\transformed\jetified-imagepipeline-okhttp3-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.4.1] F:\gradle-cache-new\caches\transforms-3\1b52c21370a38b025fe38599105dc471\transformed\appcompat-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.4.1] F:\gradle-cache-new\caches\transforms-3\1b52c21370a38b025fe38599105dc471\transformed\appcompat-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.4.1] F:\gradle-cache-new\caches\transforms-3\a62663212d8018ec07cb8901f524e165\transformed\jetified-appcompat-resources-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.4.1] F:\gradle-cache-new\caches\transforms-3\a62663212d8018ec07cb8901f524e165\transformed\jetified-appcompat-resources-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] F:\gradle-cache-new\caches\transforms-3\af51cb665c8d7bf7ef7bd1d24cdcdc71\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] F:\gradle-cache-new\caches\transforms-3\af51cb665c8d7bf7ef7bd1d24cdcdc71\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] F:\gradle-cache-new\caches\transforms-3\09133080737623630f01e7bd484eb603\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] F:\gradle-cache-new\caches\transforms-3\09133080737623630f01e7bd484eb603\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.2.4] F:\gradle-cache-new\caches\transforms-3\5f3e6f0b373cf06cff8e5b8dbac748e6\transformed\jetified-activity-1.2.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.2.4] F:\gradle-cache-new\caches\transforms-3\5f3e6f0b373cf06cff8e5b8dbac748e6\transformed\jetified-activity-1.2.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] F:\gradle-cache-new\caches\transforms-3\629a487764eb5b7a55262072b2636b14\transformed\jetified-emoji2-views-helper-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] F:\gradle-cache-new\caches\transforms-3\629a487764eb5b7a55262072b2636b14\transformed\jetified-emoji2-views-helper-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache-new\caches\transforms-3\b7a4b4b32fa1a3f9ef62577eecee019c\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache-new\caches\transforms-3\b7a4b4b32fa1a3f9ef62577eecee019c\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.0.0] F:\gradle-cache-new\caches\transforms-3\1452abd06e3eae42a169aa0a1af4c1f0\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.0.0] F:\gradle-cache-new\caches\transforms-3\1452abd06e3eae42a169aa0a1af4c1f0\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0] F:\gradle-cache-new\caches\transforms-3\3a903aae5748c6b1c0ca2a3cf6b64a58\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0] F:\gradle-cache-new\caches\transforms-3\3a903aae5748c6b1c0ca2a3cf6b64a58\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fbjni:fbjni:0.3.0] F:\gradle-cache-new\caches\transforms-3\4506787739007a25dba5df85abed42f8\transformed\jetified-fbjni-0.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fbjni:fbjni:0.3.0] F:\gradle-cache-new\caches\transforms-3\4506787739007a25dba5df85abed42f8\transformed\jetified-fbjni-0.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fresco:2.5.0] F:\gradle-cache-new\caches\transforms-3\995a2517b9ee5c7965aa1d0eb2bee262\transformed\jetified-fresco-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fresco:2.5.0] F:\gradle-cache-new\caches\transforms-3\995a2517b9ee5c7965aa1d0eb2bee262\transformed\jetified-fresco-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:drawee:2.5.0] F:\gradle-cache-new\caches\transforms-3\bd363b705b40f5d54837117e209a877a\transformed\jetified-drawee-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:drawee:2.5.0] F:\gradle-cache-new\caches\transforms-3\bd363b705b40f5d54837117e209a877a\transformed\jetified-drawee-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:middleware:2.5.0] F:\gradle-cache-new\caches\transforms-3\19887b8cc904e8a897809cc815434274\transformed\jetified-middleware-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:middleware:2.5.0] F:\gradle-cache-new\caches\transforms-3\19887b8cc904e8a897809cc815434274\transformed\jetified-middleware-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:ui-common:2.5.0] F:\gradle-cache-new\caches\transforms-3\ed0cd6359946dc4ae454a9bc9cb7ce47\transformed\jetified-ui-common-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:ui-common:2.5.0] F:\gradle-cache-new\caches\transforms-3\ed0cd6359946dc4ae454a9bc9cb7ce47\transformed\jetified-ui-common-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagefilters:2.5.0] F:\gradle-cache-new\caches\transforms-3\4dad05961f7ccbc0a684da03eb97bf3b\transformed\jetified-nativeimagefilters-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagefilters:2.5.0] F:\gradle-cache-new\caches\transforms-3\4dad05961f7ccbc0a684da03eb97bf3b\transformed\jetified-nativeimagefilters-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-native:2.5.0] F:\gradle-cache-new\caches\transforms-3\ee3687805c9531382fe806f923a73b87\transformed\jetified-memory-type-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-native:2.5.0] F:\gradle-cache-new\caches\transforms-3\ee3687805c9531382fe806f923a73b87\transformed\jetified-memory-type-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-java:2.5.0] F:\gradle-cache-new\caches\transforms-3\f5484f588c2db913022ab4492c0fd4da\transformed\jetified-memory-type-java-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-java:2.5.0] F:\gradle-cache-new\caches\transforms-3\f5484f588c2db913022ab4492c0fd4da\transformed\jetified-memory-type-java-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-native:2.5.0] F:\gradle-cache-new\caches\transforms-3\375cfbc488793c6148fb194316b8b0a0\transformed\jetified-imagepipeline-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-native:2.5.0] F:\gradle-cache-new\caches\transforms-3\375cfbc488793c6148fb194316b8b0a0\transformed\jetified-imagepipeline-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-ashmem:2.5.0] F:\gradle-cache-new\caches\transforms-3\8667c21550ecb50554f53c358d028513\transformed\jetified-memory-type-ashmem-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-ashmem:2.5.0] F:\gradle-cache-new\caches\transforms-3\8667c21550ecb50554f53c358d028513\transformed\jetified-memory-type-ashmem-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline:2.5.0] F:\gradle-cache-new\caches\transforms-3\34fccc5487a9c865193817d090e9fa3b\transformed\jetified-imagepipeline-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline:2.5.0] F:\gradle-cache-new\caches\transforms-3\34fccc5487a9c865193817d090e9fa3b\transformed\jetified-imagepipeline-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.5.0] F:\gradle-cache-new\caches\transforms-3\8670a87ece610fe795012529cbcdb3c9\transformed\jetified-nativeimagetranscoder-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.5.0] F:\gradle-cache-new\caches\transforms-3\8670a87ece610fe795012529cbcdb3c9\transformed\jetified-nativeimagetranscoder-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-base:2.5.0] F:\gradle-cache-new\caches\transforms-3\fdb920e6c8a927e2d8466f080c79a369\transformed\jetified-imagepipeline-base-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-base:2.5.0] F:\gradle-cache-new\caches\transforms-3\fdb920e6c8a927e2d8466f080c79a369\transformed\jetified-imagepipeline-base-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:soloader:2.5.0] F:\gradle-cache-new\caches\transforms-3\fe625571ae7635ea70d6bebec7dd304e\transformed\jetified-soloader-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:soloader:2.5.0] F:\gradle-cache-new\caches\transforms-3\fe625571ae7635ea70d6bebec7dd304e\transformed\jetified-soloader-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache-new\caches\transforms-3\7a78738add03003b048c09f83d1118f1\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache-new\caches\transforms-3\7a78738add03003b048c09f83d1118f1\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] F:\gradle-cache-new\caches\transforms-3\2af432480cf1f390bc76f762c4801bf9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] F:\gradle-cache-new\caches\transforms-3\2af432480cf1f390bc76f762c4801bf9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] F:\gradle-cache-new\caches\transforms-3\670a8141585c00088264dc1879ba1e39\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] F:\gradle-cache-new\caches\transforms-3\670a8141585c00088264dc1879ba1e39\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] F:\gradle-cache-new\caches\transforms-3\0259d135d69d9853d2cec2e88a660727\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] F:\gradle-cache-new\caches\transforms-3\0259d135d69d9853d2cec2e88a660727\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] F:\gradle-cache-new\caches\transforms-3\6d30fe891b6b72fd530a13844e3eab38\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] F:\gradle-cache-new\caches\transforms-3\6d30fe891b6b72fd530a13844e3eab38\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] F:\gradle-cache-new\caches\transforms-3\9ea26edee1cf8786b25e840c1f6be8ab\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] F:\gradle-cache-new\caches\transforms-3\9ea26edee1cf8786b25e840c1f6be8ab\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] F:\gradle-cache-new\caches\transforms-3\cbf2828846bf324bca3004c988c813f4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] F:\gradle-cache-new\caches\transforms-3\cbf2828846bf324bca3004c988c813f4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] F:\gradle-cache-new\caches\transforms-3\2262f4b93e23c313cd4bdc9d63b84d4a\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] F:\gradle-cache-new\caches\transforms-3\2262f4b93e23c313cd4bdc9d63b84d4a\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.7.0] F:\gradle-cache-new\caches\transforms-3\a8962bb5790e2a717af128f173a274ed\transformed\core-1.7.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.7.0] F:\gradle-cache-new\caches\transforms-3\a8962bb5790e2a717af128f173a274ed\transformed\core-1.7.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] F:\gradle-cache-new\caches\transforms-3\9487bcbe8122303e4d29f9e52ccb130e\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] F:\gradle-cache-new\caches\transforms-3\9487bcbe8122303e4d29f9e52ccb130e\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] F:\gradle-cache-new\caches\transforms-3\24c0379f7e52a0d0823681718815b7c4\transformed\jetified-lifecycle-viewmodel-savedstate-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] F:\gradle-cache-new\caches\transforms-3\24c0379f7e52a0d0823681718815b7c4\transformed\jetified-lifecycle-viewmodel-savedstate-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.1.0] F:\gradle-cache-new\caches\transforms-3\2109f8a22eecb207574bb92d7e3948da\transformed\jetified-savedstate-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.1.0] F:\gradle-cache-new\caches\transforms-3\2109f8a22eecb207574bb92d7e3948da\transformed\jetified-savedstate-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] F:\gradle-cache-new\caches\transforms-3\4f73a8555db64d6779fd86b6be461ac7\transformed\lifecycle-viewmodel-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] F:\gradle-cache-new\caches\transforms-3\4f73a8555db64d6779fd86b6be461ac7\transformed\lifecycle-viewmodel-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] F:\gradle-cache-new\caches\transforms-3\a0346a322bb1089a487390e996b59837\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] F:\gradle-cache-new\caches\transforms-3\a0346a322bb1089a487390e996b59837\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] F:\gradle-cache-new\caches\transforms-3\1c9666c86b2a52d47fd0450d25f3dbdc\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] F:\gradle-cache-new\caches\transforms-3\1c9666c86b2a52d47fd0450d25f3dbdc\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] F:\gradle-cache-new\caches\transforms-3\6c76bde2187d1415cb36e9f30f96cb59\transformed\lifecycle-livedata-core-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] F:\gradle-cache-new\caches\transforms-3\6c76bde2187d1415cb36e9f30f96cb59\transformed\lifecycle-livedata-core-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.4.0] F:\gradle-cache-new\caches\transforms-3\0160e3d4e8b11fca2887edd9f032b0bc\transformed\lifecycle-runtime-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.4.0] F:\gradle-cache-new\caches\transforms-3\0160e3d4e8b11fca2887edd9f032b0bc\transformed\lifecycle-runtime-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] F:\gradle-cache-new\caches\transforms-3\9d9a10e0d1651e93ebf8ab54caace060\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] F:\gradle-cache-new\caches\transforms-3\9d9a10e0d1651e93ebf8ab54caace060\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:fbcore:2.5.0] F:\gradle-cache-new\caches\transforms-3\6cba262f0bee94129cb5a913e6bf5cd7\transformed\jetified-fbcore-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fbcore:2.5.0] F:\gradle-cache-new\caches\transforms-3\6cba262f0bee94129cb5a913e6bf5cd7\transformed\jetified-fbcore-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] F:\gradle-cache-new\caches\transforms-3\c76d94935246982c69a49c04f65a0632\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] F:\gradle-cache-new\caches\transforms-3\c76d94935246982c69a49c04f65a0632\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
uses-permission#android.permission.READ_PHONE_STATE
IMPLIED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12 reason: org.webkit.android_jsc has a targetSdkVersion < 4
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache-new\caches\transforms-3\b7a4b4b32fa1a3f9ef62577eecee019c\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache-new\caches\transforms-3\b7a4b4b32fa1a3f9ef62577eecee019c\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.0.0] F:\gradle-cache-new\caches\transforms-3\1452abd06e3eae42a169aa0a1af4c1f0\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.0.0] F:\gradle-cache-new\caches\transforms-3\1452abd06e3eae42a169aa0a1af4c1f0\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:30:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:28:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:29:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:27:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache-new\caches\transforms-3\c0f81c6d6f40018a3e2decc1b4ec9cfb\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:32:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache-new\caches\transforms-3\b7a4b4b32fa1a3f9ef62577eecee019c\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache-new\caches\transforms-3\b7a4b4b32fa1a3f9ef62577eecee019c\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache-new\caches\transforms-3\b7a4b4b32fa1a3f9ef62577eecee019c\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:32:17-78
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache-new\caches\transforms-3\7a78738add03003b048c09f83d1118f1\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache-new\caches\transforms-3\7a78738add03003b048c09f83d1118f1\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache-new\caches\transforms-3\7a78738add03003b048c09f83d1118f1\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:13:13-57
