-- Merging decision tree log ---
manifest
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
MERGED from [:react-native-async-storage_async-storage] F:\jarvis\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-gesture-handler] F:\jarvis\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-safe-area-context] F:\jarvis\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-screens] F:\jarvis\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-vector-icons] F:\jarvis\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.react:react-android:0.72.6] F:\gradle-cache\caches\transforms-3\59d2ff0b6347c1a315f288d4329a7d28\transformed\jetified-react-android-0.72.6-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] F:\gradle-cache\caches\transforms-3\f249f371f5d3b03ad219f3dc58423412\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [org.webkit:android-jsc:r174650] F:\gradle-cache\caches\transforms-3\3fce666accd8a1249425372750923c3c\transformed\jetified-android-jsc-r174650\AndroidManifest.xml:1:1-3:12
MERGED from [com.google.android.material:material:1.6.1] F:\gradle-cache\caches\transforms-3\c572b377194a8a385981f85402606c3e\transformed\material-1.6.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] F:\gradle-cache\caches\transforms-3\dc90cd28ca6ab5c37f0641415f960eae\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.4.2] F:\gradle-cache\caches\transforms-3\ab12076093e54ca9ba43148476352294\transformed\appcompat-1.4.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.4.2] F:\gradle-cache\caches\transforms-3\340ecd04f65bea04e220644699ca2e30\transformed\jetified-appcompat-resources-1.4.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] F:\gradle-cache\caches\transforms-3\310bc8789d92739ad542b189d9cba041\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] F:\gradle-cache\caches\transforms-3\d96d30db2bdb6412ed453dcd702c4bb4\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] F:\gradle-cache\caches\transforms-3\ef802cd2c4d5e3cdb9b62720a17abc6d\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.2.4] F:\gradle-cache\caches\transforms-3\8be5f71cbaaa9f66962f53349ed9d2f8\transformed\jetified-activity-1.2.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] F:\gradle-cache\caches\transforms-3\566761a0341f70aab2ab9eba03a03bfe\transformed\jetified-emoji2-views-helper-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache\caches\transforms-3\7752f00a9ab209825b03d268e4986842\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache\caches\transforms-3\b5799f2137ee91c1cc5ce05b0561e59f\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.startup:startup-runtime:1.0.0] F:\gradle-cache\caches\transforms-3\df5520077ecd40ff7181ebfe285cd272\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0] F:\gradle-cache\caches\transforms-3\90f3d923ad7e9d92b7d856b1a7d3d0d3\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fbjni:fbjni:0.3.0] F:\gradle-cache\caches\transforms-3\3cbf57ef10ebdb9ca717102a721f7ae1\transformed\jetified-fbjni-0.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:fresco:2.5.0] F:\gradle-cache\caches\transforms-3\9115548e1b554c0b0b72fbb3fa765bd7\transformed\jetified-fresco-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] F:\gradle-cache\caches\transforms-3\52fe9dc651d8450e47551f13b1aad5cc\transformed\jetified-imagepipeline-okhttp3-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:drawee:2.5.0] F:\gradle-cache\caches\transforms-3\d3fe09a9aefc14091dbfd11ae362fa89\transformed\jetified-drawee-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:middleware:2.5.0] F:\gradle-cache\caches\transforms-3\551d5463d7267f94d622f1ba00098d79\transformed\jetified-middleware-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:ui-common:2.5.0] F:\gradle-cache\caches\transforms-3\f472d2a5f8ed3ec95494afeae20c970c\transformed\jetified-ui-common-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:nativeimagefilters:2.5.0] F:\gradle-cache\caches\transforms-3\bdd453a01280b5b823bf9f611f01500c\transformed\jetified-nativeimagefilters-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-native:2.5.0] F:\gradle-cache\caches\transforms-3\bbc595742e4628e01604bbf2757ab114\transformed\jetified-memory-type-native-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-java:2.5.0] F:\gradle-cache\caches\transforms-3\31460dec06152be85c15e839da91ab23\transformed\jetified-memory-type-java-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-native:2.5.0] F:\gradle-cache\caches\transforms-3\336c223542d4a358ea6d9cbec9bd2c68\transformed\jetified-imagepipeline-native-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-ashmem:2.5.0] F:\gradle-cache\caches\transforms-3\8ff3fcf142768ad84bf49a78a8fb4980\transformed\jetified-memory-type-ashmem-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline:2.5.0] F:\gradle-cache\caches\transforms-3\072088e0a8ac3c808a4d79aadb74520d\transformed\jetified-imagepipeline-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.5.0] F:\gradle-cache\caches\transforms-3\69e3c1fafddba60a7a575be49baaea31\transformed\jetified-nativeimagetranscoder-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-base:2.5.0] F:\gradle-cache\caches\transforms-3\262ddfaf040113f2cd0e3f315a49502b\transformed\jetified-imagepipeline-base-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:soloader:2.5.0] F:\gradle-cache\caches\transforms-3\5b30bcba05f06c7747e0e4c0e8aebbd5\transformed\jetified-soloader-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache\caches\transforms-3\6fe96233a4c6c9c9c0ca0e1e6683f004\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:2:1-17:12
MERGED from [androidx.core:core-ktx:1.8.0] F:\gradle-cache\caches\transforms-3\dc41285db52eb0cd7e9663e166da2823\transformed\jetified-core-ktx-1.8.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] F:\gradle-cache\caches\transforms-3\2d68e271aea678e72589c8cc9877e0ad\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] F:\gradle-cache\caches\transforms-3\734fd1eb7fde439a594b28d9353b9b6f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] F:\gradle-cache\caches\transforms-3\911b3a60717855c9bc47a508c9c15643\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] F:\gradle-cache\caches\transforms-3\eb9891a991c1b5930bdaaf84d646fa6b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] F:\gradle-cache\caches\transforms-3\42bc0949eaa419c72e5229abf1d75c21\transformed\jetified-lifecycle-viewmodel-savedstate-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.1.0] F:\gradle-cache\caches\transforms-3\1439685bbe36ee41bd634acd15700d76\transformed\jetified-savedstate-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] F:\gradle-cache\caches\transforms-3\fb013eb07f69450eafc6cbad32a78253\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] F:\gradle-cache\caches\transforms-3\1029ff980d1a03b60d54839aa1fe7d0e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] F:\gradle-cache\caches\transforms-3\9c2c6180c8e0905ca6e3cba7ab825217\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] F:\gradle-cache\caches\transforms-3\8c21e01e5bf1e69b296125173074873e\transformed\lifecycle-viewmodel-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] F:\gradle-cache\caches\transforms-3\2bc856aff85ed6f93e284c5645ee9829\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] F:\gradle-cache\caches\transforms-3\3333bcbc629737d66729651b371837dd\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] F:\gradle-cache\caches\transforms-3\3db21b5b0927d179863295a6430118a1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] F:\gradle-cache\caches\transforms-3\15135d2e7a25889be5627095d6d2c745\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] F:\gradle-cache\caches\transforms-3\6e907c5a662ed5fb07b8fe8debef2071\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] F:\gradle-cache\caches\transforms-3\1b773223c0eb02fa5cbfe184d89aea94\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.8.0] F:\gradle-cache\caches\transforms-3\6c9de45700a7851c879b33c8d726a07f\transformed\core-1.8.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] F:\gradle-cache\caches\transforms-3\99f2d09009489e18333fade2fbe03210\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cardview:cardview:1.0.0] F:\gradle-cache\caches\transforms-3\2b55356f56543166e7bde63d258c8943\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] F:\gradle-cache\caches\transforms-3\cbc36a4d8f327ece8b6ae49408673b86\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] F:\gradle-cache\caches\transforms-3\3d29292ac7c40ac90abd3cfbd84f565b\transformed\lifecycle-livedata-core-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.4.0] F:\gradle-cache\caches\transforms-3\d5520a225def22ded685bd6e381b8dea\transformed\lifecycle-runtime-2.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] F:\gradle-cache\caches\transforms-3\ca49ffb86a46ebae83a53a03529281ab\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] F:\gradle-cache\caches\transforms-3\ed6e9658f25678eae325b6527b52d837\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] F:\gradle-cache\caches\transforms-3\132cf9228c99fecfead35c3e95297103\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] F:\gradle-cache\caches\transforms-3\da609291fd63f11cd50e53cebbbaabec\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:fbcore:2.5.0] F:\gradle-cache\caches\transforms-3\2bdc594ad0d5a607a9e15bfb15802d87\transformed\jetified-fbcore-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.annotation:annotation-experimental:1.1.0] F:\gradle-cache\caches\transforms-3\d09835cff6f6e8d50deb43579d8ac4c6\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:17:1-24:12
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
	package
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:2:5-29
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:4:5-67
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:4:22-64
uses-permission#android.permission.RECORD_AUDIO
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:5:5-71
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:5:22-68
uses-permission#android.permission.CALL_PHONE
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:6:5-69
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:6:22-66
uses-permission#android.permission.CAMERA
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:7:5-65
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:7:22-62
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:8:5-81
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:9:5-80
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:9:22-77
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:10:5-76
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:10:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:11:5-76
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:11:22-73
uses-permission#android.permission.BLUETOOTH
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:12:5-68
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:12:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:13:5-74
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:13:22-71
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:14:5-78
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:14:22-75
uses-permission#android.permission.PACKAGE_USAGE_STATS
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:15:5-78
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:15:22-75
uses-permission#android.permission.ACCESS_NOTIFICATION_POLICY
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:16:5-85
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:16:22-82
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:17:5-77
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:17:22-74
uses-permission#android.permission.WRITE_SETTINGS
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:18:5-73
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:18:22-70
uses-permission#android.permission.FLASHLIGHT
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:19:5-69
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:19:22-66
application
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:21:5-40:19
MERGED from [org.webkit:android-jsc:r174650] F:\gradle-cache\caches\transforms-3\3fce666accd8a1249425372750923c3c\transformed\jetified-android-jsc-r174650\AndroidManifest.xml:2:3-17
MERGED from [org.webkit:android-jsc:r174650] F:\gradle-cache\caches\transforms-3\3fce666accd8a1249425372750923c3c\transformed\jetified-android-jsc-r174650\AndroidManifest.xml:2:3-17
MERGED from [com.google.android.material:material:1.6.1] F:\gradle-cache\caches\transforms-3\c572b377194a8a385981f85402606c3e\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.6.1] F:\gradle-cache\caches\transforms-3\c572b377194a8a385981f85402606c3e\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] F:\gradle-cache\caches\transforms-3\dc90cd28ca6ab5c37f0641415f960eae\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] F:\gradle-cache\caches\transforms-3\dc90cd28ca6ab5c37f0641415f960eae\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache\caches\transforms-3\7752f00a9ab209825b03d268e4986842\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache\caches\transforms-3\7752f00a9ab209825b03d268e4986842\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache\caches\transforms-3\b5799f2137ee91c1cc5ce05b0561e59f\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache\caches\transforms-3\b5799f2137ee91c1cc5ce05b0561e59f\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.0.0] F:\gradle-cache\caches\transforms-3\df5520077ecd40ff7181ebfe285cd272\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.0.0] F:\gradle-cache\caches\transforms-3\df5520077ecd40ff7181ebfe285cd272\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:25:5-31:19
MERGED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache\caches\transforms-3\6fe96233a4c6c9c9c0ca0e1e6683f004\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache\caches\transforms-3\6fe96233a4c6c9c9c0ca0e1e6683f004\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
MERGED from [androidx.core:core:1.8.0] F:\gradle-cache\caches\transforms-3\6c9de45700a7851c879b33c8d726a07f\transformed\core-1.8.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.8.0] F:\gradle-cache\caches\transforms-3\6c9de45700a7851c879b33c8d726a07f\transformed\core-1.8.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] F:\gradle-cache\caches\transforms-3\99f2d09009489e18333fade2fbe03210\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] F:\gradle-cache\caches\transforms-3\99f2d09009489e18333fade2fbe03210\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.8.0] F:\gradle-cache\caches\transforms-3\6c9de45700a7851c879b33c8d726a07f\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
	android:label
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:23:7-39
	android:roundIcon
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:25:7-52
	android:icon
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:24:7-41
	android:allowBackup
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:26:7-34
	android:theme
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:27:7-38
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:22:7-38
activity#com.jarvislite.MainActivity
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:28:7-39:18
	android:label
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:30:9-41
	android:launchMode
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:32:9-40
	android:windowSoftInputMode
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:33:9-51
	android:exported
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:34:9-32
	android:configChanges
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:31:9-118
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:29:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:35:9-38:25
action#android.intent.action.MAIN
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:36:13-65
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:36:21-62
category#android.intent.category.LAUNCHER
ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:37:13-73
	android:name
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml:37:23-70
uses-sdk
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
MERGED from [:react-native-async-storage_async-storage] F:\jarvis\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-async-storage_async-storage] F:\jarvis\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-gesture-handler] F:\jarvis\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-gesture-handler] F:\jarvis\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-safe-area-context] F:\jarvis\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-safe-area-context] F:\jarvis\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-screens] F:\jarvis\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-screens] F:\jarvis\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-vector-icons] F:\jarvis\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-vector-icons] F:\jarvis\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.react:react-android:0.72.6] F:\gradle-cache\caches\transforms-3\59d2ff0b6347c1a315f288d4329a7d28\transformed\jetified-react-android-0.72.6-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.72.6] F:\gradle-cache\caches\transforms-3\59d2ff0b6347c1a315f288d4329a7d28\transformed\jetified-react-android-0.72.6-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] F:\gradle-cache\caches\transforms-3\f249f371f5d3b03ad219f3dc58423412\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] F:\gradle-cache\caches\transforms-3\f249f371f5d3b03ad219f3dc58423412\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.6.1] F:\gradle-cache\caches\transforms-3\c572b377194a8a385981f85402606c3e\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.6.1] F:\gradle-cache\caches\transforms-3\c572b377194a8a385981f85402606c3e\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] F:\gradle-cache\caches\transforms-3\dc90cd28ca6ab5c37f0641415f960eae\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] F:\gradle-cache\caches\transforms-3\dc90cd28ca6ab5c37f0641415f960eae\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.4.2] F:\gradle-cache\caches\transforms-3\ab12076093e54ca9ba43148476352294\transformed\appcompat-1.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.4.2] F:\gradle-cache\caches\transforms-3\ab12076093e54ca9ba43148476352294\transformed\appcompat-1.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.4.2] F:\gradle-cache\caches\transforms-3\340ecd04f65bea04e220644699ca2e30\transformed\jetified-appcompat-resources-1.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.4.2] F:\gradle-cache\caches\transforms-3\340ecd04f65bea04e220644699ca2e30\transformed\jetified-appcompat-resources-1.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] F:\gradle-cache\caches\transforms-3\310bc8789d92739ad542b189d9cba041\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] F:\gradle-cache\caches\transforms-3\310bc8789d92739ad542b189d9cba041\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] F:\gradle-cache\caches\transforms-3\d96d30db2bdb6412ed453dcd702c4bb4\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] F:\gradle-cache\caches\transforms-3\d96d30db2bdb6412ed453dcd702c4bb4\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] F:\gradle-cache\caches\transforms-3\ef802cd2c4d5e3cdb9b62720a17abc6d\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] F:\gradle-cache\caches\transforms-3\ef802cd2c4d5e3cdb9b62720a17abc6d\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.2.4] F:\gradle-cache\caches\transforms-3\8be5f71cbaaa9f66962f53349ed9d2f8\transformed\jetified-activity-1.2.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.2.4] F:\gradle-cache\caches\transforms-3\8be5f71cbaaa9f66962f53349ed9d2f8\transformed\jetified-activity-1.2.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] F:\gradle-cache\caches\transforms-3\566761a0341f70aab2ab9eba03a03bfe\transformed\jetified-emoji2-views-helper-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] F:\gradle-cache\caches\transforms-3\566761a0341f70aab2ab9eba03a03bfe\transformed\jetified-emoji2-views-helper-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache\caches\transforms-3\7752f00a9ab209825b03d268e4986842\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache\caches\transforms-3\7752f00a9ab209825b03d268e4986842\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache\caches\transforms-3\b5799f2137ee91c1cc5ce05b0561e59f\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache\caches\transforms-3\b5799f2137ee91c1cc5ce05b0561e59f\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.0.0] F:\gradle-cache\caches\transforms-3\df5520077ecd40ff7181ebfe285cd272\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.0.0] F:\gradle-cache\caches\transforms-3\df5520077ecd40ff7181ebfe285cd272\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0] F:\gradle-cache\caches\transforms-3\90f3d923ad7e9d92b7d856b1a7d3d0d3\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0] F:\gradle-cache\caches\transforms-3\90f3d923ad7e9d92b7d856b1a7d3d0d3\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fbjni:fbjni:0.3.0] F:\gradle-cache\caches\transforms-3\3cbf57ef10ebdb9ca717102a721f7ae1\transformed\jetified-fbjni-0.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fbjni:fbjni:0.3.0] F:\gradle-cache\caches\transforms-3\3cbf57ef10ebdb9ca717102a721f7ae1\transformed\jetified-fbjni-0.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fresco:2.5.0] F:\gradle-cache\caches\transforms-3\9115548e1b554c0b0b72fbb3fa765bd7\transformed\jetified-fresco-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fresco:2.5.0] F:\gradle-cache\caches\transforms-3\9115548e1b554c0b0b72fbb3fa765bd7\transformed\jetified-fresco-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] F:\gradle-cache\caches\transforms-3\52fe9dc651d8450e47551f13b1aad5cc\transformed\jetified-imagepipeline-okhttp3-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] F:\gradle-cache\caches\transforms-3\52fe9dc651d8450e47551f13b1aad5cc\transformed\jetified-imagepipeline-okhttp3-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:drawee:2.5.0] F:\gradle-cache\caches\transforms-3\d3fe09a9aefc14091dbfd11ae362fa89\transformed\jetified-drawee-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:drawee:2.5.0] F:\gradle-cache\caches\transforms-3\d3fe09a9aefc14091dbfd11ae362fa89\transformed\jetified-drawee-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:middleware:2.5.0] F:\gradle-cache\caches\transforms-3\551d5463d7267f94d622f1ba00098d79\transformed\jetified-middleware-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:middleware:2.5.0] F:\gradle-cache\caches\transforms-3\551d5463d7267f94d622f1ba00098d79\transformed\jetified-middleware-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:ui-common:2.5.0] F:\gradle-cache\caches\transforms-3\f472d2a5f8ed3ec95494afeae20c970c\transformed\jetified-ui-common-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:ui-common:2.5.0] F:\gradle-cache\caches\transforms-3\f472d2a5f8ed3ec95494afeae20c970c\transformed\jetified-ui-common-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagefilters:2.5.0] F:\gradle-cache\caches\transforms-3\bdd453a01280b5b823bf9f611f01500c\transformed\jetified-nativeimagefilters-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagefilters:2.5.0] F:\gradle-cache\caches\transforms-3\bdd453a01280b5b823bf9f611f01500c\transformed\jetified-nativeimagefilters-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-native:2.5.0] F:\gradle-cache\caches\transforms-3\bbc595742e4628e01604bbf2757ab114\transformed\jetified-memory-type-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-native:2.5.0] F:\gradle-cache\caches\transforms-3\bbc595742e4628e01604bbf2757ab114\transformed\jetified-memory-type-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-java:2.5.0] F:\gradle-cache\caches\transforms-3\31460dec06152be85c15e839da91ab23\transformed\jetified-memory-type-java-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-java:2.5.0] F:\gradle-cache\caches\transforms-3\31460dec06152be85c15e839da91ab23\transformed\jetified-memory-type-java-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-native:2.5.0] F:\gradle-cache\caches\transforms-3\336c223542d4a358ea6d9cbec9bd2c68\transformed\jetified-imagepipeline-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-native:2.5.0] F:\gradle-cache\caches\transforms-3\336c223542d4a358ea6d9cbec9bd2c68\transformed\jetified-imagepipeline-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-ashmem:2.5.0] F:\gradle-cache\caches\transforms-3\8ff3fcf142768ad84bf49a78a8fb4980\transformed\jetified-memory-type-ashmem-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-ashmem:2.5.0] F:\gradle-cache\caches\transforms-3\8ff3fcf142768ad84bf49a78a8fb4980\transformed\jetified-memory-type-ashmem-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline:2.5.0] F:\gradle-cache\caches\transforms-3\072088e0a8ac3c808a4d79aadb74520d\transformed\jetified-imagepipeline-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline:2.5.0] F:\gradle-cache\caches\transforms-3\072088e0a8ac3c808a4d79aadb74520d\transformed\jetified-imagepipeline-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.5.0] F:\gradle-cache\caches\transforms-3\69e3c1fafddba60a7a575be49baaea31\transformed\jetified-nativeimagetranscoder-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.5.0] F:\gradle-cache\caches\transforms-3\69e3c1fafddba60a7a575be49baaea31\transformed\jetified-nativeimagetranscoder-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-base:2.5.0] F:\gradle-cache\caches\transforms-3\262ddfaf040113f2cd0e3f315a49502b\transformed\jetified-imagepipeline-base-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-base:2.5.0] F:\gradle-cache\caches\transforms-3\262ddfaf040113f2cd0e3f315a49502b\transformed\jetified-imagepipeline-base-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:soloader:2.5.0] F:\gradle-cache\caches\transforms-3\5b30bcba05f06c7747e0e4c0e8aebbd5\transformed\jetified-soloader-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:soloader:2.5.0] F:\gradle-cache\caches\transforms-3\5b30bcba05f06c7747e0e4c0e8aebbd5\transformed\jetified-soloader-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache\caches\transforms-3\6fe96233a4c6c9c9c0ca0e1e6683f004\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache\caches\transforms-3\6fe96233a4c6c9c9c0ca0e1e6683f004\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.core:core-ktx:1.8.0] F:\gradle-cache\caches\transforms-3\dc41285db52eb0cd7e9663e166da2823\transformed\jetified-core-ktx-1.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-ktx:1.8.0] F:\gradle-cache\caches\transforms-3\dc41285db52eb0cd7e9663e166da2823\transformed\jetified-core-ktx-1.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] F:\gradle-cache\caches\transforms-3\2d68e271aea678e72589c8cc9877e0ad\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] F:\gradle-cache\caches\transforms-3\2d68e271aea678e72589c8cc9877e0ad\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] F:\gradle-cache\caches\transforms-3\734fd1eb7fde439a594b28d9353b9b6f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] F:\gradle-cache\caches\transforms-3\734fd1eb7fde439a594b28d9353b9b6f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] F:\gradle-cache\caches\transforms-3\911b3a60717855c9bc47a508c9c15643\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] F:\gradle-cache\caches\transforms-3\911b3a60717855c9bc47a508c9c15643\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] F:\gradle-cache\caches\transforms-3\eb9891a991c1b5930bdaaf84d646fa6b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] F:\gradle-cache\caches\transforms-3\eb9891a991c1b5930bdaaf84d646fa6b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] F:\gradle-cache\caches\transforms-3\42bc0949eaa419c72e5229abf1d75c21\transformed\jetified-lifecycle-viewmodel-savedstate-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] F:\gradle-cache\caches\transforms-3\42bc0949eaa419c72e5229abf1d75c21\transformed\jetified-lifecycle-viewmodel-savedstate-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.1.0] F:\gradle-cache\caches\transforms-3\1439685bbe36ee41bd634acd15700d76\transformed\jetified-savedstate-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.1.0] F:\gradle-cache\caches\transforms-3\1439685bbe36ee41bd634acd15700d76\transformed\jetified-savedstate-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] F:\gradle-cache\caches\transforms-3\fb013eb07f69450eafc6cbad32a78253\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] F:\gradle-cache\caches\transforms-3\fb013eb07f69450eafc6cbad32a78253\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] F:\gradle-cache\caches\transforms-3\1029ff980d1a03b60d54839aa1fe7d0e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] F:\gradle-cache\caches\transforms-3\1029ff980d1a03b60d54839aa1fe7d0e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] F:\gradle-cache\caches\transforms-3\9c2c6180c8e0905ca6e3cba7ab825217\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] F:\gradle-cache\caches\transforms-3\9c2c6180c8e0905ca6e3cba7ab825217\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] F:\gradle-cache\caches\transforms-3\8c21e01e5bf1e69b296125173074873e\transformed\lifecycle-viewmodel-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] F:\gradle-cache\caches\transforms-3\8c21e01e5bf1e69b296125173074873e\transformed\lifecycle-viewmodel-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] F:\gradle-cache\caches\transforms-3\2bc856aff85ed6f93e284c5645ee9829\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] F:\gradle-cache\caches\transforms-3\2bc856aff85ed6f93e284c5645ee9829\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] F:\gradle-cache\caches\transforms-3\3333bcbc629737d66729651b371837dd\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] F:\gradle-cache\caches\transforms-3\3333bcbc629737d66729651b371837dd\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] F:\gradle-cache\caches\transforms-3\3db21b5b0927d179863295a6430118a1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] F:\gradle-cache\caches\transforms-3\3db21b5b0927d179863295a6430118a1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] F:\gradle-cache\caches\transforms-3\15135d2e7a25889be5627095d6d2c745\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] F:\gradle-cache\caches\transforms-3\15135d2e7a25889be5627095d6d2c745\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] F:\gradle-cache\caches\transforms-3\6e907c5a662ed5fb07b8fe8debef2071\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] F:\gradle-cache\caches\transforms-3\6e907c5a662ed5fb07b8fe8debef2071\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] F:\gradle-cache\caches\transforms-3\1b773223c0eb02fa5cbfe184d89aea94\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] F:\gradle-cache\caches\transforms-3\1b773223c0eb02fa5cbfe184d89aea94\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.8.0] F:\gradle-cache\caches\transforms-3\6c9de45700a7851c879b33c8d726a07f\transformed\core-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.8.0] F:\gradle-cache\caches\transforms-3\6c9de45700a7851c879b33c8d726a07f\transformed\core-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] F:\gradle-cache\caches\transforms-3\99f2d09009489e18333fade2fbe03210\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] F:\gradle-cache\caches\transforms-3\99f2d09009489e18333fade2fbe03210\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] F:\gradle-cache\caches\transforms-3\2b55356f56543166e7bde63d258c8943\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] F:\gradle-cache\caches\transforms-3\2b55356f56543166e7bde63d258c8943\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] F:\gradle-cache\caches\transforms-3\cbc36a4d8f327ece8b6ae49408673b86\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] F:\gradle-cache\caches\transforms-3\cbc36a4d8f327ece8b6ae49408673b86\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] F:\gradle-cache\caches\transforms-3\3d29292ac7c40ac90abd3cfbd84f565b\transformed\lifecycle-livedata-core-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] F:\gradle-cache\caches\transforms-3\3d29292ac7c40ac90abd3cfbd84f565b\transformed\lifecycle-livedata-core-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.4.0] F:\gradle-cache\caches\transforms-3\d5520a225def22ded685bd6e381b8dea\transformed\lifecycle-runtime-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.4.0] F:\gradle-cache\caches\transforms-3\d5520a225def22ded685bd6e381b8dea\transformed\lifecycle-runtime-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] F:\gradle-cache\caches\transforms-3\ca49ffb86a46ebae83a53a03529281ab\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] F:\gradle-cache\caches\transforms-3\ca49ffb86a46ebae83a53a03529281ab\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] F:\gradle-cache\caches\transforms-3\ed6e9658f25678eae325b6527b52d837\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] F:\gradle-cache\caches\transforms-3\ed6e9658f25678eae325b6527b52d837\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] F:\gradle-cache\caches\transforms-3\132cf9228c99fecfead35c3e95297103\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] F:\gradle-cache\caches\transforms-3\132cf9228c99fecfead35c3e95297103\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] F:\gradle-cache\caches\transforms-3\da609291fd63f11cd50e53cebbbaabec\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] F:\gradle-cache\caches\transforms-3\da609291fd63f11cd50e53cebbbaabec\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:fbcore:2.5.0] F:\gradle-cache\caches\transforms-3\2bdc594ad0d5a607a9e15bfb15802d87\transformed\jetified-fbcore-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fbcore:2.5.0] F:\gradle-cache\caches\transforms-3\2bdc594ad0d5a607a9e15bfb15802d87\transformed\jetified-fbcore-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] F:\gradle-cache\caches\transforms-3\d09835cff6f6e8d50deb43579d8ac4c6\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] F:\gradle-cache\caches\transforms-3\d09835cff6f6e8d50deb43579d8ac4c6\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
		ADDED from F:\jarvis\android\app\src\main\AndroidManifest.xml
		INJECTED from F:\jarvis\android\app\src\main\AndroidManifest.xml
uses-permission#android.permission.READ_PHONE_STATE
IMPLIED from F:\jarvis\android\app\src\main\AndroidManifest.xml:1:1-41:12 reason: org.webkit.android_jsc has a targetSdkVersion < 4
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache\caches\transforms-3\7752f00a9ab209825b03d268e4986842\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache\caches\transforms-3\b5799f2137ee91c1cc5ce05b0561e59f\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache\caches\transforms-3\b5799f2137ee91c1cc5ce05b0561e59f\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.0.0] F:\gradle-cache\caches\transforms-3\df5520077ecd40ff7181ebfe285cd272\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.0.0] F:\gradle-cache\caches\transforms-3\df5520077ecd40ff7181ebfe285cd272\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache\caches\transforms-3\7752f00a9ab209825b03d268e4986842\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:30:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache\caches\transforms-3\7752f00a9ab209825b03d268e4986842\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:28:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache\caches\transforms-3\7752f00a9ab209825b03d268e4986842\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:29:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache\caches\transforms-3\7752f00a9ab209825b03d268e4986842\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:27:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache\caches\transforms-3\7752f00a9ab209825b03d268e4986842\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache\caches\transforms-3\7752f00a9ab209825b03d268e4986842\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.0.0] F:\gradle-cache\caches\transforms-3\7752f00a9ab209825b03d268e4986842\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:32:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache\caches\transforms-3\b5799f2137ee91c1cc5ce05b0561e59f\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache\caches\transforms-3\b5799f2137ee91c1cc5ce05b0561e59f\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.0] F:\gradle-cache\caches\transforms-3\b5799f2137ee91c1cc5ce05b0561e59f\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:32:17-78
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache\caches\transforms-3\6fe96233a4c6c9c9c0ca0e1e6683f004\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache\caches\transforms-3\6fe96233a4c6c9c9c0ca0e1e6683f004\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.10.5] F:\gradle-cache\caches\transforms-3\6fe96233a4c6c9c9c0ca0e1e6683f004\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:13:13-57
